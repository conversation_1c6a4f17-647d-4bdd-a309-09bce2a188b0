"""remove api key usage

Revision ID: 05bc714ef0c9
Revises: 2a3b4c5d6e7f
Create Date: 2025-06-02 21:28:02.631316

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '05bc714ef0c9'
down_revision: Union[str, None] = '2a3b4c5d6e7f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('api_key_usage')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('api_key_usage',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('api_key_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('endpoint', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('status_code', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('processing_time', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['api_key_id'], ['api_keys.id'], name='api_key_usage_api_key_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='api_key_usage_pkey')
    )
    # ### end Alembic commands ###
