"""create llm configurations

Revision ID: f7d2e23aee66
Revises:
Create Date: 2025-05-07 20:10:00.873019

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB


# revision identifiers, used by Alembic.
revision: str = 'f7d2e23aee66'
down_revision: Union[str, None] = None  # This is the first migration
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create llm_configurations table
    op.create_table(
        'llm_configurations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('model_id', sa.String(), nullable=False),
        sa.Column('provider', sa.String(), nullable=False),
        sa.Column('system_instruction', sa.String(), nullable=False),
        sa.Column('temperature', sa.Float(), nullable=False, server_default='0'),
        sa.Column('top_p', sa.Float(), nullable=False, server_default='0.7'),
        sa.Column('top_k', sa.Integer(), nullable=False, server_default='5'),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )

    # Add seed data
    op.bulk_insert(
        sa.table('llm_configurations',
            sa.Column('model_id', sa.String()),
            sa.Column('provider', sa.String()),
            sa.Column('system_instruction', sa.String()),
            sa.Column('temperature', sa.Float()),
            sa.Column('top_p', sa.Float()),
            sa.Column('top_k', sa.Integer())
        ),
        [
            {
                'model_id': 'gemini-2.0-flash-lite',
                'provider': 'vertex-ai',
                'system_instruction': "Extract the following information from the provided invoice pdf and match it with user input. Return the results in a JSON structure.\n\n**Field Definitions for Extraction and Matching:**\n\n1.  **`vendor_name` (Vendor Name)**\n    * IMPORTANT: In most cases, the customer on the invoice will be Komatsu Indonesia, meaning it is highly unlikely that the vendor name is also Komatsu Indonesia.\n    * Identify the actual vendor (seller/supplier) from the invoice.\n\n2.  **`invoice_date` (Tanggal / Invoice Date)**\n    * Extract the date from the invoice and return it in YYYY-MM-DD format.\n    * If the invoice date format is different (e.g., DD/MM/YYYY, MM/DD/YYYY, DD Mon YY), convert it accordingly to YYYY-MM-DD.\n    * If the user provides `invoice_date` in a different format, attempt to convert it to YYYY-MM-DD for comparison.\n\n3.  **`invoice_number` (Nomor Invoice / Invoice Number)**\n    * Extract the unique invoice identifier from the document.\n    * **CRITICAL: The invoice number *must* be extracted with 100% accuracy. Every character must be verified against the image/pdf.**\n    * **General Instruction:** The invoice number is typically a series of digits or alphanumeric characters that uniquely identifies the invoice. Look for a field labeled \\\"Invoice No.\\\", \\\"Invoice Number\\\", or similar.\n    * **Edge Case Handling:**\n        * **IF** the invoice number is found in a column labeled \\\"No./Date\\\" (or similar) **AND** the value in that column contains two pieces of information separated by a slash (\\\"/\\\"), **THEN**:\n            * The invoice number is the *first* value in this column, *before* the slash.\n            * The date is the *second* value in this column, *after* the slash.\n            * Extract *only* the value *before* the slash as the invoice number.\n            * Do *not* include the slash or the date in the extracted invoice number.\n        * **ELSE** (if the above conditions are not met):\n            * Extract the invoice number as it appears in the labeled field, without any special handling.\n\n4.  **`tax_invoice_number` (Kode dan Nomor Seri Faktur Pajak / Electronic Tax Number)**\n    * Extract the electronic tax invoice number (if available).\n    * This is most commonly found in a Faktur Pajak document.\n\n5.  **`invoice_amount` (Nominal Rupiah / Total Amount)**\n    * Extract the total invoice amount in Indonesian Rupiah (IDR) from the invoice.\n    * Remove any thousand separators (e.g., \\\"1.500.000\\\" → \\\"1500000\\\"). Ensure the extracted value is a string of digits, optionally with a decimal point for non-IDR currencies if applicable, but for IDR, typically whole numbers.\n    * This amount should include VAT (PPN) if applicable.\n    * If the user provides `invoice_amount` with separators, normalize it by removing them before comparison.\n\n6.  **`vat_amount` (Pajak Pertambahan Nilai / Total PPN)**\n    * Extract the total PPN (Value-Added Tax, VAT) amount in IDR from the invoice.\n    * Remove any thousand separators. Ensure the extracted value is a string of digits.\n    * This is most commonly found in a *Faktur Pajak* document.\n    * If the user provides `vat_amount` with separators, normalize it by removing them before comparison.\n\n**Document Type Classification:**\n\n7.  **`document_type`**\n    * Determine if the document being processed is a \\\"standard_invoice\\\" or a \\\"tax_invoice\\\" (Faktur Pajak).\n    * If neither, return \\\"unknown\\\".\n    * This will be reported under the `document` object in the JSON output.\n\n**GENERAL INSTRUCTIONS FOR ALL FIELDS:**\n\n* **Accuracy and Confidence:**\n    * Pay extremely close attention to individual characters and digits.\n    * For each extracted field, include a `confidence_level` (\\\"High\\\", \\\"Medium\\\", \\\"Low\\\").\n    * If there is *any* ambiguity or uncertainty in reading a character or digit (e.g., a '3' looking like a '5', a '0' looking like an 'O', a '/' being unclear), lower the `confidence_level` to 'Medium' or 'Low'.\n    * For `invoice_date` conversion, any uncertainty also warrants a lower `confidence_level`.\n* **Handling Missing Data:**\n    * If any field's value cannot be found in the invoice, its `ocr_value` in the JSON output should be the string \\\"not_found\\\", and its `status` (if user input was provided for it) will be \\\"not_found\\\".\n    * Ensure high accuracy in extraction, even if the invoice layout varies."
            }
        ]
    )


def downgrade() -> None:
    # Drop table
    op.drop_table('llm_configurations')

