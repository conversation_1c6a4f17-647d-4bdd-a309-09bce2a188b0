"""add status to document processing logs

Revision ID: 2a3b4c5d6e7f
Revises: 1eb03bdacd94
Create Date: 2025-05-15 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2a3b4c5d6e7f'
down_revision: Union[str, None] = '1eb03bdacd94'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add status column with default value 'success'
    op.add_column('document_processing_logs', sa.Column('status', sa.String(), nullable=False, server_default=sa.text("'success'")))


def downgrade() -> None:
    # Remove status column
    op.drop_column('document_processing_logs', 'status')
