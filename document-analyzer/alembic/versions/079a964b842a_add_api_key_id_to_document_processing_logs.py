"""Add api_key_id to document_processing_logs

Revision ID: 079a964b842a
Revises: 05bc714ef0c9
Create Date: 2025-06-03 04:56:26.606555

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '079a964b842a'
down_revision: Union[str, None] = '05bc714ef0c9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('document_processing_logs', sa.Column('api_key_id', sa.UUID(), nullable=True))
    op.create_foreign_key(None, 'document_processing_logs', 'api_keys', ['api_key_id'], ['id'])


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'document_processing_logs', type_='foreignkey')
    op.drop_column('document_processing_logs', 'api_key_id')
