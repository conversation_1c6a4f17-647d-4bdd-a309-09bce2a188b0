#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create an initial API key.

This script creates an initial API key for bootstrapping the system.
"""

import sys
import os
import logging

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.database import SessionLocal
from app.repositories.api_key_repository import create_api_key
from app.utils.env_loader import load_env_file

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables from .env file if it exists
load_env_file()


def create_initial_key(db: Session) -> None:
    """
    Create an initial API key.

    Args:
        db (Session): Database session
    """
    # Create an initial API key
    db_api_key, key_value = create_api_key(
        db=db,
        name="Initial API Key",
        description="Initial API key for bootstrapping the system",
        is_active=True
    )

    logger.info(f"Created initial API key with ID: {db_api_key.id}")
    logger.info(f"API Key: {key_value}")
    logger.info("IMPORTANT: Save this API key in a secure location. It will not be shown again.")


def main() -> None:
    """Main function."""
    # Create a database session
    db = SessionLocal()

    try:
        create_initial_key(db)
    finally:
        db.close()


if __name__ == "__main__":
    main()
