#!/bin/bash
# Script to set up Google Application Credentials for Docker deployment

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Setting up Google Application Credentials for Docker deployment${NC}"
echo "This script will help you set up your Google credentials for production deployment."

# Create credentials directory if it doesn't exist
if [ ! -d "./credentials" ]; then
    echo -e "${YELLOW}Creating credentials directory...${NC}"
    mkdir -p ./credentials
    echo -e "${GREEN}Created credentials directory.${NC}"
else
    echo -e "${GREEN}Credentials directory already exists.${NC}"
fi

# Ask for the path to the service account key file
echo -e "${YELLOW}Please enter the path to your Google service account key file (.json):${NC}"
read -p "> " credentials_path

# Check if the file exists
if [ ! -f "$credentials_path" ]; then
    echo -e "${RED}Error: File not found at $credentials_path${NC}"
    exit 1
fi

# Copy the file to the credentials directory
echo -e "${YELLOW}Copying credentials file to ./credentials/service-account.json...${NC}"
cp "$credentials_path" ./credentials/service-account.json

# Check if the copy was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Successfully copied credentials file.${NC}"
else
    echo -e "${RED}Error: Failed to copy credentials file.${NC}"
    exit 1
fi

# Set permissions
echo -e "${YELLOW}Setting secure permissions on credentials file...${NC}"
chmod 600 ./credentials/service-account.json

# Create or update .env file with GOOGLE_CREDENTIALS_PATH
echo -e "${YELLOW}Updating .env file with GOOGLE_CREDENTIALS_PATH...${NC}"

# Check if .env file exists
if [ -f ".env" ]; then
    # Check if GOOGLE_CREDENTIALS_PATH already exists in .env
    if grep -q "GOOGLE_CREDENTIALS_PATH" .env; then
        # Update existing entry
        sed -i.bak "s|GOOGLE_CREDENTIALS_PATH=.*|GOOGLE_CREDENTIALS_PATH=$(pwd)/credentials/service-account.json|g" .env
        rm .env.bak
    else
        # Add new entry
        echo "GOOGLE_CREDENTIALS_PATH=$(pwd)/credentials/service-account.json" >> .env
    fi
else
    # Create new .env file
    echo "GOOGLE_CREDENTIALS_PATH=$(pwd)/credentials/service-account.json" > .env
fi

echo -e "${GREEN}Updated .env file with GOOGLE_CREDENTIALS_PATH.${NC}"

# Add credentials directory to .gitignore if it's not already there
if [ -f ".gitignore" ]; then
    if ! grep -q "credentials/" .gitignore; then
        echo -e "${YELLOW}Adding credentials/ to .gitignore...${NC}"
        echo "# Google credentials" >> .gitignore
        echo "credentials/" >> .gitignore
        echo -e "${GREEN}Added credentials/ to .gitignore.${NC}"
    else
        echo -e "${GREEN}credentials/ is already in .gitignore.${NC}"
    fi
else
    echo -e "${YELLOW}Creating .gitignore file...${NC}"
    echo "# Google credentials" > .gitignore
    echo "credentials/" >> .gitignore
    echo -e "${GREEN}Created .gitignore file with credentials/ entry.${NC}"
fi

echo -e "${GREEN}Setup complete!${NC}"
echo -e "Your Google Application Credentials are now set up for Docker deployment."
echo -e "The credentials file is stored at: ${YELLOW}./credentials/service-account.json${NC}"
echo -e "The path is configured in your .env file as: ${YELLOW}GOOGLE_CREDENTIALS_PATH=$(pwd)/credentials/service-account.json${NC}"
echo -e "${YELLOW}IMPORTANT: Never commit the credentials directory to version control!${NC}"
echo -e "You can now run: ${GREEN}docker-compose up -d${NC}"
