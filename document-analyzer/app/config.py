"""
Configuration management for the Document Analyzer API.

This module handles loading configuration settings based on the current environment.
"""

import os
from pydantic_settings import BaseSettings
from enum import Enum
from typing import Optional
import logging


class Environment(str, Enum):
    """Supported environments."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class DatabaseSettings(BaseSettings):
    """Database connection settings."""
    username: str
    password: Optional[str] = ""
    host: str
    port: int
    database: str

    @property
    def connection_string(self) -> str:
        """
        Generate a PostgreSQL connection string.

        Returns:
            str: PostgreSQL connection string
        """
        # Handle empty password case
        if self.password:
            auth = f"{self.username}:{self.password}"
        else:
            auth = self.username

        return f"postgresql://{auth}@{self.host}:{self.port}/{self.database}"

    model_config = {
        "env_prefix": "DB_"
    }


class Settings(BaseSettings):
    """Application settings."""
    # Environment settings
    environment: Environment = Environment.DEVELOPMENT

    # Logging settings
    log_level: str = os.getenv("LOG_LEVEL", "INFO")

    # Database settings
    db: DatabaseSettings = DatabaseSettings(
        username="robby",
        password="",
        host="localhost",
        port=5432,
        database="document_analyzer"
    )

    # Sentry settings
    sentry_dsn: Optional[str] = os.getenv("SENTRY_DSN")
    sentry_traces_sample_rate: float = float(os.getenv(
        "SENTRY_TRACES_SAMPLE_RATE",
        "0.1" if Environment.PRODUCTION else "1.0"
    ))
    sentry_profiles_sample_rate: float = float(os.getenv(
        "SENTRY_PROFILES_SAMPLE_RATE",
        "0.1" if Environment.PRODUCTION else "1.0"
    ))

    # Swagger UI authentication
    swagger_username: str = os.getenv("SWAGGER_USERNAME", "apidocs")
    swagger_password: str = os.getenv("SWAGGER_PASSWORD", "pass1234")

    model_config = {
        "env_prefix": "APP_"
    }


def get_database_settings() -> DatabaseSettings:
    """
    Get database settings based on the current environment.

    Returns:
        DatabaseSettings: Database connection settings
    """
    # Get environment from APP_ENVIRONMENT or default to development
    env = os.getenv("APP_ENVIRONMENT", Environment.DEVELOPMENT)

    if env == Environment.PRODUCTION:
        # Production database settings
        return DatabaseSettings(
            username=os.getenv("DB_USERNAME", "postgres"),
            password=os.getenv("DB_PASSWORD", ""),
            host=os.getenv("DB_HOST", "db"),
            port=int(os.getenv("DB_PORT", "5432")),
            database=os.getenv("DB_DATABASE", "document_analyzer_prod")
        )
    elif env == Environment.STAGING:
        # Staging database settings
        return DatabaseSettings(
            username=os.getenv("DB_USERNAME", "postgres"),
            password=os.getenv("DB_PASSWORD", ""),
            host=os.getenv("DB_HOST", "db"),
            port=int(os.getenv("DB_PORT", "5432")),
            database=os.getenv("DB_DATABASE", "document_analyzer_staging")
        )
    else:
        # Development database settings (default)
        return DatabaseSettings(
            username=os.getenv("DB_USERNAME", "robby"),
            password=os.getenv("DB_PASSWORD", ""),
            host=os.getenv("DB_HOST", "localhost"),
            port=int(os.getenv("DB_PORT", "5432")),
            database=os.getenv("DB_DATABASE", "document_analyzer")
        )


def get_settings() -> Settings:
    """
    Get application settings.

    Returns:
        Settings: Application settings
    """
    # Create settings with default values
    settings = Settings()

    # Override database settings based on environment
    env = os.getenv("APP_ENVIRONMENT", Environment.DEVELOPMENT)
    if env:
        # Only override if environment is explicitly set
        settings.environment = env
        settings.db = get_database_settings()

    return settings


# Create a global settings instance
settings = get_settings()
