"""
API key models for the Document Analyzer API.

This module contains Pydantic models for API key-related data structures.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field


class APIKeyBase(BaseModel):
    """Base model for API key."""
    name: str
    description: Optional[str] = None
    is_active: bool = True


class APIKeyCreate(APIKeyBase):
    """Model for creating a new API key."""
    pass


class APIKeyUpdate(BaseModel):
    """Model for updating an existing API key."""
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class APIKey(APIKeyBase):
    """Model for an API key with database fields."""
    id: UUID
    created_at: datetime
    last_used_at: Optional[datetime] = None

    model_config = {
        "from_attributes": True
    }


class APIKeyWithValue(APIKey):
    """Model for an API key with the actual key value (only returned when creating a new key)."""
    key_value: str


class APIKeyUsageBase(BaseModel):
    """Base model for API key usage."""
    endpoint: str
    status_code: int
    processing_time: float


class APIKeyUsage(APIKeyUsageBase):
    """Model for API key usage with database fields."""
    id: int
    api_key_id: UUID
    timestamp: datetime

    model_config = {
        "from_attributes": True
    }


class APIKeyUsageCreate(APIKeyUsageBase):
    """Model for creating a new API key usage record."""
    api_key_id: UUID


class APIKeyUsageStats(BaseModel):
    """Model for API key usage statistics."""
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_processing_time: float
    endpoints: Dict[str, int]


class PaginatedAPIKey(BaseModel):
    """Model for paginated API keys."""
    items: List[APIKey]
    total: int
    page: int
    page_size: int
    pages: int

    @classmethod
    def create(cls, items: List[APIKey], total: int, page: int, page_size: int):
        """Create a paginated response."""
        pages = (total + page_size - 1) // page_size if page_size > 0 else 0
        return cls(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )


class PaginatedAPIKeyUsage(BaseModel):
    """Model for paginated API key usage records."""
    items: List[APIKeyUsage]
    total: int
    page: int
    page_size: int
    pages: int

    @classmethod
    def create(cls, items: List[APIKeyUsage], total: int, page: int, page_size: int):
        """Create a paginated response."""
        pages = (total + page_size - 1) // page_size if page_size > 0 else 0
        return cls(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )
