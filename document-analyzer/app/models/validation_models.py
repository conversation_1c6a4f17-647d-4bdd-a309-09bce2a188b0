"""
Validation models for the Document Analyzer API.

This module contains Pydantic models for validation-related data structures.
"""

from typing import List, Optional
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field


class ValidationResultCreate(BaseModel):
    """Model for creating a validation result."""
    field_key: str
    is_valid: bool


class ValidationResult(BaseModel):
    """Model for a validation result with database fields."""
    id: int
    document_processing_log_id: UUID
    field_key: str
    is_valid: bool
    validated_at: datetime

    model_config = {
        "from_attributes": True
    }


class ValidationResultsResponse(BaseModel):
    """Model for a response containing multiple validation results."""
    document_processing_log_id: UUID
    results: List[ValidationResult]