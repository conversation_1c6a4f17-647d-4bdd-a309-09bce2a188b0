"""
Document models for the Document Analyzer API.

This module contains Pydantic models for document-related data structures.
"""

from typing import List, Dict, Optional
from pydantic import BaseModel


class PageColorAnalysis(BaseModel):
    """Model for color analysis results of a single page."""
    page: int
    is_color: bool
    color_percentage: float
    pixels_analyzed: int


class DocumentColorAnalysis(BaseModel):
    """Model for color analysis results of a document."""
    filename: str
    has_color: bool
    total_pages: int
    color_pages: List[int]
    color_page_count: int
    detailed_results: Optional[List[PageColorAnalysis]] = None
