"""
Google Generative AI Service for Document Analysis.

This module provides functionality to analyze documents using Google's Generative AI.
It supports both standard Google AI API and Google Vertex AI.
It uses the latest LLM configuration from the database to configure the model.
"""

import os
import tempfile
from typing import Dict, Any, Optional, BinaryIO, List
import json
import logging
from collections import Counter

from google import genai
from google.genai.types import Content, GenerateContentConfig, HttpOptions, Part
from sqlalchemy.orm import Session
from fastapi import UploadFile, HTTPException

from app.repositories import llm_configuration_repository
from app.models.database_models import LLMConfiguration
from app.models.llm_configuration_models import LLMConfiguration as LLMConfigPydantic
from app.services.google_genai.schemas import InvoiceSchema
from app.utils.error_sanitizer import sanitize_error_message


def get_latest_llm_configuration(db: Session, document_type: str = "standard_invoice") -> Optional[LLMConfiguration]:
    """
    Get the latest LLM configuration from the database for a specific document type.

    Args:
        db (Session): Database session
        document_type (str): Document type to get configuration for (e.g., standard_invoice, tax_invoice)

    Returns:
        Optional[LLMConfiguration]: Latest LLM configuration for the specified document type if any exists, None otherwise
    """
    return llm_configuration_repository.get_latest_llm_configuration_by_document_type(db, document_type)


def configure_genai_client() -> genai.Client:
    """
    Configure and return a Google Generative AI client.

    Returns:
        genai.Client: Configured Google Generative AI client

    Raises:
        HTTPException: If there's an error configuring the client
    """
    # Check if we're using Vertex AI
    use_vertex = os.getenv("GOOGLE_GENAI_USE_VERTEXAI", "False").lower() == "true"

    if use_vertex:
        # Vertex AI configuration
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        if not project_id:
            raise HTTPException(
                status_code=500,
                detail="Cloud project ID environment variable is required for document processing."
            )

        # Configure client with Vertex AI settings
        return genai.Client(
            vertexai=True,
            project=project_id,
            location=os.getenv("GOOGLE_CLOUD_LOCATION", "us-west1"),
            http_options=HttpOptions(api_version="v1")
        )

    else:
        # Standard Google AI configuration
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise HTTPException(
                status_code=500,
                detail="API key not found. Please set the required API key environment variable."
            )

        # Configure client with API key
        return genai.Client(api_key=api_key)


async def _process_document_with_parts(
    parts: List[Part],
    user_prompt: str,
    db: Session,
    document_type: str = "standard_invoice",
    api_key: Optional[str] = None
) -> Dict[str, Any]:
    """
    Process a document using Google's Generative AI with provided parts.

    Args:
        parts (List[Part]): List of parts to include in the content
        user_prompt (str): User prompt to guide the LLM
        db (Session): Database session
        document_type (str): Type of document to process (standard_invoice or tax_invoice)
        api_key (Optional[str]): Google API key (if not provided, will use environment variable)

    Returns:
        Dict[str, Any]: Dictionary containing the extracted data and raw LLM response
                        with keys 'extracted_data' and 'raw_response'

    Raises:
        HTTPException: If there's an error processing the document
    """
    logger = logging.getLogger(__name__)
    logger.info(f"Starting Google API document processing - Document type: {document_type}")
    
    # Get the latest LLM configuration for the specified document type
    config = get_latest_llm_configuration(db, document_type)
    if not config:
        raise HTTPException(
            status_code=400,
            detail=f"No LLM configuration found for document type: {document_type}. Please create a configuration first."
        )

    # Check if the provider is supported
    allowed_providers = ["google", "vertex-ai"]
    if config.provider.lower() not in allowed_providers:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid provider: {config.provider}. This service only supports the configured providers."
        )

    try:
        # Initialize the client
        client = configure_genai_client()

        # Select the appropriate schema based on document type
        response_schema = None
        if document_type == "standard_invoice":
            response_schema = InvoiceSchema.standard_invoice_with_confidence_schema()
        elif document_type == "tax_invoice":
            response_schema = InvoiceSchema.tax_invoice_with_confidence_schema()
        else:
            # Fallback to legacy schema if document type is not recognized
            response_schema = InvoiceSchema.invoice_with_confidence_schema()

        generate_content_config = GenerateContentConfig(
            temperature=config.temperature,
            top_p=config.top_p,
            top_k=config.top_k,
            response_mime_type="application/json",
            system_instruction=[
                Part.from_text(text=config.system_instruction)
            ],
            response_schema=response_schema
        )

        # Create content with user prompt and document parts
        content_parts = [Part.from_text(text=user_prompt)]
        content_parts.extend(parts)

        # Log document parts information
        logger = logging.getLogger(__name__)
        logger.info(f"Document processing - User prompt length: {len(user_prompt)} characters")
        logger.info(f"Document processing - Number of document parts: {len(parts)}")
        
        # Log details about each part (without exposing sensitive content)
        for i, part in enumerate(parts):
            part_info = {
                "part_index": i,
                "part_type": type(part).__name__,
                "has_text": hasattr(part, 'text') and part.text is not None,
                "text_length": len(part.text) if hasattr(part, 'text') and part.text else 0,
                "has_inline_data": hasattr(part, 'inline_data') and part.inline_data is not None,
                "mime_type": getattr(part.inline_data, 'mime_type', None) if hasattr(part, 'inline_data') else None,
                "data_size": len(getattr(part.inline_data, 'data', b'')) if hasattr(part, 'inline_data') and hasattr(part.inline_data, 'data') else 0
            }
            logger.info(f"Document part {i}: {json.dumps(part_info, indent=2)}")

        contents = [
            Content(
                role="user",
                parts=content_parts
            )
        ]

        # Log the request body for debugging
        logger = logging.getLogger(__name__)
        try:
            # Create a serializable representation of the request
            request_body = {
                "model": config.model_id,
                "contents": [
                    {
                        "role": "user",
                        "parts": [
                            {
                                "text": part.text if hasattr(part, 'text') and part.text else f"<{type(part).__name__}>"
                            } for part in content_parts
                        ]
                    }
                ],
                "config": {
                    "temperature": config.temperature,
                    "top_p": config.top_p,
                    "top_k": config.top_k,
                    "response_mime_type": "application/json",
                    "system_instruction": config.system_instruction,
                    "response_schema": "InvoiceSchema" if response_schema else None
                }
            }
            
            logger.info(f"Google API Request Body: {json.dumps(request_body, indent=2)}")
            
        except Exception as e:
            logger.warning(f"Failed to log request body: {str(e)}")

        result = client.models.generate_content(
            model=config.model_id,
            contents=contents,
            config=generate_content_config,
        )

        # Log the response for debugging
        try:
            response_summary = {
                "candidates_count": len(result.candidates) if result.candidates else 0,
                "usage_metadata": {
                    "prompt_token_count": getattr(result.usage_metadata, 'prompt_token_count', 0) if hasattr(result, 'usage_metadata') else 0,
                    "candidates_token_count": getattr(result.usage_metadata, 'candidates_token_count', 0) if hasattr(result, 'usage_metadata') else 0,
                    "total_token_count": getattr(result.usage_metadata, 'total_token_count', 0) if hasattr(result, 'usage_metadata') else 0,
                },
                "first_candidate_text_length": len(result.candidates[0].content.parts[0].text) if result.candidates and result.candidates[0].content and result.candidates[0].content.parts else 0
            }
            logger.info(f"Google API Response Summary: {json.dumps(response_summary, indent=2)}")
        except Exception as e:
            logger.warning(f"Failed to log response summary: {str(e)}")

        # Extract the text content from the response
        response_dict = {}
        if result.candidates and result.candidates[0].content and result.candidates[0].content.parts:
            try:
                # Try to parse the response as JSON
                response_text = result.candidates[0].content.parts[0].text
                response_dict = json.loads(response_text)

            except (json.JSONDecodeError, AttributeError, IndexError) as e:
                # If parsing fails, just use the text as is
                response_dict = {"raw_text": response_text if 'response_text' in locals() else "No text found"}

        # Convert the GenerateContentResponse to a serializable dictionary
        raw_response_dict = {}
        if result.candidates:
            raw_response_dict["candidates"] = []
            for candidate in result.candidates:
                candidate_dict = {
                    "finish_reason": getattr(candidate, "finish_reason", None),
                    "safety_ratings": getattr(candidate, "safety_ratings", None),
                }

                if candidate.content and candidate.content.parts:
                    candidate_dict["content"] = {
                        "role": getattr(candidate.content, "role", None),
                        "parts": [{"text": part.text} for part in candidate.content.parts if hasattr(part, "text")]
                    }

                raw_response_dict["candidates"].append(candidate_dict)

        # Add any other relevant fields from the response
        if hasattr(result, "usage_metadata"):
            raw_response_dict["usage_metadata"] = {
                "prompt_token_count": getattr(result.usage_metadata, "prompt_token_count", None),
                "candidates_token_count": getattr(result.usage_metadata, "candidates_token_count", None),
                "total_token_count": getattr(result.usage_metadata, "total_token_count", None),
            }
        if hasattr(result, "model_version"):
            raw_response_dict["model_version"] = result.model_version

        # Return both the extracted data and the serializable raw response
        result_dict = {
            "extracted_data": response_dict,
            "raw_response": raw_response_dict
        }
        
        logger.info(f"Google API document processing completed successfully - Document type: {document_type}")
        return result_dict

    except Exception as e:
        # Log the error for debugging
        logger.error(f"Google API document processing failed - Document type: {document_type}, Error: {str(e)}")
        
        # Sanitize the error message to remove references to underlying technologies
        sanitized_error = sanitize_error_message(str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Error processing document: {sanitized_error}"
        )


async def process_document(
    gs_uri: str,
    user_prompt: str,
    db: Session,
    document_type: str = "standard_invoice",
    api_key: Optional[str] = None
) -> Dict[str, Any]:
    """
    Process a document from Google Cloud Storage using Google's Generative AI.

    Args:
        gs_uri (str): Google Cloud Storage URI of the document
        user_prompt (str): User prompt to guide the LLM
        db (Session): Database session
        document_type (str): Type of document to process (standard_invoice or tax_invoice)
        api_key (Optional[str]): Google API key (if not provided, will use environment variable)

    Returns:
        Dict[str, Any]: Dictionary containing the extracted data and raw LLM response
                        with keys 'extracted_data' and 'raw_response'

    Raises:
        HTTPException: If there's an error processing the document
    """
    # Create a part from the GCS URI
    document_part = Part.from_uri(
        file_uri=gs_uri,
        mime_type="application/pdf"
    )

    # Process the document with the part
    return await _process_document_with_parts(
        parts=[document_part],
        user_prompt=user_prompt,
        db=db,
        document_type=document_type,
        api_key=api_key
    )


async def process_document_from_url(
    file_url: str,
    user_prompt: str,
    db: Session,
    document_type: str = "standard_invoice",
    api_key: Optional[str] = None
) -> Dict[str, Any]:
    """
    Process a document from a public URL using Google's Generative AI.

    Args:
        file_url (str): Public URL of the PDF document
        user_prompt (str): User prompt to guide the LLM
        db (Session): Database session
        document_type (str): Type of document to process (standard_invoice or tax_invoice)
        api_key (Optional[str]): Google API key (if not provided, will use environment variable)

    Returns:
        Dict[str, Any]: Dictionary containing the extracted data and raw LLM response
                        with keys 'extracted_data' and 'raw_response'

    Raises:
        HTTPException: If there's an error processing the document
    """
    # Create a part from the URL
    document_part = Part.from_uri(
        file_uri=file_url,
        mime_type="application/pdf"
    )

    # Process the document with the part
    return await _process_document_with_parts(
        parts=[document_part],
        user_prompt=user_prompt,
        db=db,
        document_type=document_type,
        api_key=api_key
    )
