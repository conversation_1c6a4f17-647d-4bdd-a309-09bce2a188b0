"""
Repository for API key operations.

This module contains functions for database operations related to API keys.
"""

from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc
from uuid import UUID
import secrets
import string
import hashlib
from datetime import datetime

from app.models.database_models import <PERSON><PERSON>ey


def generate_api_key() -> str:
    """
    Generate a random API key.

    Returns:
        str: A random API key
    """
    # Generate a random 32-character string
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(32))


def hash_api_key(key: str) -> str:
    """
    Hash an API key for storage.

    Args:
        key (str): The API key to hash

    Returns:
        str: The hashed API key
    """
    return hashlib.sha256(key.encode()).hexdigest()


def create_api_key(
    db: Session,
    name: str,
    description: Optional[str] = None,
    is_active: bool = True
) -> Tuple[APIKey, str]:
    """
    Create a new API key.

    Args:
        db (Session): Database session
        name (str): A friendly name for the API key
        description (Optional[str], optional): A description of what the API key is used for. Defaults to None.
        is_active (bool, optional): Whether the API key is active. Defaults to True.

    Returns:
        Tuple[APIKey, str]: The created API key and the raw API key value
    """
    # Generate a random API key
    key_value = generate_api_key()

    # Hash the API key for storage
    hashed_key = hash_api_key(key_value)

    # Create the API key
    db_api_key = APIKey(
        key=hashed_key,
        name=name,
        description=description,
        is_active=is_active
    )

    db.add(db_api_key)
    db.commit()
    db.refresh(db_api_key)

    return db_api_key, key_value


def get_api_key(db: Session, api_key_id: UUID) -> Optional[APIKey]:
    """
    Get an API key by ID.

    Args:
        db (Session): Database session
        api_key_id (UUID): API key ID

    Returns:
        Optional[APIKey]: API key if found, None otherwise
    """
    return db.query(APIKey).filter(APIKey.id == api_key_id).first()


def get_api_key_by_key(db: Session, key: str) -> Optional[APIKey]:
    """
    Get an API key by key value.

    Args:
        db (Session): Database session
        key (str): API key value

    Returns:
        Optional[APIKey]: API key if found, None otherwise
    """
    # Hash the key for lookup
    hashed_key = hash_api_key(key)
    return db.query(APIKey).filter(APIKey.key == hashed_key).first()


def get_all_api_keys(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    include_inactive: bool = False
) -> Tuple[List[APIKey], int]:
    """
    Get all API keys.

    Args:
        db (Session): Database session
        skip (int, optional): Number of records to skip. Defaults to 0.
        limit (int, optional): Maximum number of records to return. Defaults to 100.
        include_inactive (bool, optional): Whether to include inactive API keys. Defaults to False.

    Returns:
        Tuple[List[APIKey], int]: List of API keys and total count
    """
    query = db.query(APIKey)

    if not include_inactive:
        query = query.filter(APIKey.is_active == True)

    total = query.count()
    items = query.order_by(desc(APIKey.created_at)).offset(skip).limit(limit).all()

    return items, total


def update_api_key(
    db: Session,
    api_key_id: UUID,
    name: Optional[str] = None,
    description: Optional[str] = None,
    is_active: Optional[bool] = None
) -> Optional[APIKey]:
    """
    Update an API key.

    Args:
        db (Session): Database session
        api_key_id (UUID): API key ID
        name (Optional[str], optional): New name for the API key. Defaults to None.
        description (Optional[str], optional): New description for the API key. Defaults to None.
        is_active (Optional[bool], optional): New active status for the API key. Defaults to None.

    Returns:
        Optional[APIKey]: Updated API key if found, None otherwise
    """
    db_api_key = db.query(APIKey).filter(APIKey.id == api_key_id).first()

    if not db_api_key:
        return None

    if name is not None:
        db_api_key.name = name

    if description is not None:
        db_api_key.description = description

    if is_active is not None:
        db_api_key.is_active = is_active

    db.commit()
    db.refresh(db_api_key)

    return db_api_key


def delete_api_key(db: Session, api_key_id: UUID) -> bool:
    """
    Delete an API key.

    Args:
        db (Session): Database session
        api_key_id (UUID): API key ID

    Returns:
        bool: True if deleted, False if not found
    """
    db_api_key = db.query(APIKey).filter(APIKey.id == api_key_id).first()

    if not db_api_key:
        return False

    # Delete the API key
    db.delete(db_api_key)
    db.commit()

    return True
