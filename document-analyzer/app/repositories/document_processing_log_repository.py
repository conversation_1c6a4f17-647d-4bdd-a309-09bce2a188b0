"""
Repository for document processing log operations.

This module contains functions for database operations related to document processing logs.
"""

from typing import Optional, Dict, Any, List, Tuple, Union
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc
import time
from uuid import UUID

from app.models.database_models import Document<PERSON>ro<PERSON>ing<PERSON><PERSON>


def create_document_processing_log(
    db: Session,
    blob_name: str,
    document_size: int,
    llm_configuration_id: int,
    raw_llm_response: Optional[Dict[str, Any]] = None,
    extracted_data: Optional[Dict[str, Any]] = None,
    color_analysis_results: Optional[Dict[str, Any]] = None,
    processing_time: Optional[float] = None,
    status: str = "success"
) -> DocumentProcessingLog:
    """
    Create a new document processing log entry.

    Args:
        db (Session): Database session
        blob_name (str): Path to the document within the bucket
        document_size (int): Size of the document in bytes
        llm_configuration_id (int): ID of the LLM configuration used
        raw_llm_response (Optional[Dict[str, Any]]): Raw response from the LLM
        extracted_data (Optional[Dict[str, Any]]): Structured data extracted from the document
        color_analysis_results (Optional[Dict[str, Any]]): Results from the PDF color analysis
        processing_time (Optional[float]): Time taken to process the document in seconds
        status (str): Status of the document processing (success or error)

    Returns:
        DocumentProcessingLog: Created document processing log
    """
    db_log = DocumentProcessingLog(
        blob_name=blob_name,
        document_size=document_size,
        llm_configuration_id=llm_configuration_id,
        raw_llm_response=raw_llm_response,
        extracted_data=extracted_data,
        color_analysis_results=color_analysis_results,
        processing_time=processing_time,
        status=status
    )
    db.add(db_log)
    db.commit()
    db.refresh(db_log)
    return db_log


def get_document_processing_log(db: Session, log_id: UUID) -> Optional[DocumentProcessingLog]:
    """
    Get a document processing log by ID.

    Args:
        db (Session): Database session
        log_id (UUID): Document processing log ID

    Returns:
        Optional[DocumentProcessingLog]: Document processing log if found, None otherwise
    """
    return db.query(DocumentProcessingLog).options(
        joinedload(DocumentProcessingLog.validation_results)
    ).filter(DocumentProcessingLog.id == log_id).first()


def get_document_processing_logs_by_blob_name(db: Session, blob_name: str) -> List[DocumentProcessingLog]:
    """
    Get document processing logs by blob name.

    Args:
        db (Session): Database session
        blob_name (str): Path to the document within the bucket

    Returns:
        List[DocumentProcessingLog]: List of document processing logs
    """
    return db.query(DocumentProcessingLog).options(
        joinedload(DocumentProcessingLog.validation_results)
    ).filter(DocumentProcessingLog.blob_name == blob_name).all()


def get_paginated_document_processing_logs(
    db: Session,
    skip: int = 0,
    limit: int = 100
) -> Tuple[List[DocumentProcessingLog], int]:
    """
    Get paginated document processing logs ordered by recently added (created_at desc).

    Args:
        db (Session): Database session
        skip (int, optional): Number of records to skip. Defaults to 0.
        limit (int, optional): Maximum number of records to return. Defaults to 100.

    Returns:
        Tuple[List[DocumentProcessingLog], int]: Tuple containing list of document processing logs and total count
    """
    # Get total count
    total = db.query(func.count(DocumentProcessingLog.id)).scalar()

    # Get paginated results ordered by created_at desc (most recent first)
    # Include validation_results in the query using joinedload
    items = db.query(DocumentProcessingLog) \
        .options(joinedload(DocumentProcessingLog.validation_results)) \
        .order_by(desc(DocumentProcessingLog.created_at)) \
        .offset(skip) \
        .limit(limit) \
        .all()

    return items, total


def delete_document_processing_log(db: Session, log_id: UUID) -> bool:
    """
    Delete a document processing log by ID.

    This will also delete all associated validation results due to the foreign key relationship.

    Args:
        db (Session): Database session
        log_id (UUID): Document processing log ID

    Returns:
        bool: True if deleted, False if not found
    """
    # Load the log with its validation results to ensure they're loaded for deletion
    db_log = db.query(DocumentProcessingLog).options(
        joinedload(DocumentProcessingLog.validation_results)
    ).filter(DocumentProcessingLog.id == log_id).first()

    if not db_log:
        return False

    # Delete all validation results first
    if db_log.validation_results:
        for validation_result in db_log.validation_results:
            db.delete(validation_result)

    # Then delete the log itself
    db.delete(db_log)
    db.commit()
    return True
