"""
Repository for validation result operations.

This module contains functions for database operations related to validation results.
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
from uuid import UUID

from app.models.database_models import ValidationResult


def create_validation_result(
    db: Session,
    document_processing_log_id: UUID,
    field_key: str,
    is_valid: bool
) -> ValidationResult:
    """
    Create a new validation result.

    Args:
        db: Database session
        document_processing_log_id: ID of the document processing log
        field_key: The field that was validated
        is_valid: Whether the extracted value is valid

    Returns:
        ValidationResult: Created validation result
    """
    db_validation = ValidationResult(
        document_processing_log_id=document_processing_log_id,
        field_key=field_key,
        is_valid=is_valid
    )
    db.add(db_validation)
    db.commit()
    db.refresh(db_validation)
    return db_validation


def get_validation_results_by_log_id(
    db: Session,
    document_processing_log_id: UUID
) -> List[ValidationResult]:
    """
    Get all validation results for a document processing log.

    Args:
        db: Database session
        document_processing_log_id: ID of the document processing log

    Returns:
        List[ValidationResult]: List of validation results
    """
    return db.query(ValidationResult).filter(
        ValidationResult.document_processing_log_id == document_processing_log_id
    ).all()


def get_validation_result(
    db: Session,
    document_processing_log_id: UUID,
    field_key: str
) -> Optional[ValidationResult]:
    """
    Get a specific validation result.

    Args:
        db: Database session
        document_processing_log_id: ID of the document processing log
        field_key: The field to get validation for

    Returns:
        Optional[ValidationResult]: The validation result if found
    """
    return db.query(ValidationResult).filter(
        and_(
            ValidationResult.document_processing_log_id == document_processing_log_id,
            ValidationResult.field_key == field_key
        )
    ).first()


def update_validation_result(
    db: Session,
    validation_id: int,
    is_valid: bool
) -> ValidationResult:
    """
    Update an existing validation result.

    Args:
        db: Database session
        validation_id: ID of the validation result to update
        is_valid: New validation status

    Returns:
        ValidationResult: Updated validation result
    """
    db_validation = db.query(ValidationResult).filter(ValidationResult.id == validation_id).first()
    db_validation.is_valid = is_valid
    db.commit()
    db.refresh(db_validation)
    return db_validation
