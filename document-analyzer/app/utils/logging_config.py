import logging
import os
from pythonjsonlogger.json import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from contextvars import ContextVar

# Define ContextVars to store request context for logging
api_key_id_context = ContextVar("api_key_id", default=None)
request_id_context = ContextVar("request_id", default=None)
endpoint_context = ContextVar("endpoint", default=None)
method_context = ContextVar("method", default=None)
user_agent_context = ContextVar("user_agent", default=None)

class RequestContextFilter(logging.Filter):
    """
    A logging filter that retrieves request context from ContextVars
    and adds them as attributes to log records.
    """
    def filter(self, record):
        # Get context variables for the current execution context
        record.api_key_id = api_key_id_context.get()
        record.request_id = request_id_context.get()
        record.endpoint = endpoint_context.get()
        record.method = method_context.get()
        record.user_agent = user_agent_context.get()
        return True

def configure_logging(use_json_formatter=True):
    """
    Configures the application's logging to output structured logs to the console,
    automatically including request context when available.

    Args:
        use_json_formatter (bool): Whether to use JSON formatting for logs.
            Default is True. Set to False for testing to make logs more readable.
    """
    root_logger = logging.getLogger()
    # Set the logging level from environment variable (e.g., LOG_LEVEL=INFO) or default to INFO
    root_logger.setLevel(os.environ.get("LOG_LEVEL", "INFO").upper())

    # Clear existing handlers and filters
    if root_logger.handlers:
        for handler in root_logger.handlers:
            root_logger.removeHandler(handler)
    root_logger.filters.clear()

    # Configure the Console Handler
    console_handler = logging.StreamHandler()

    if use_json_formatter:
        # Define the format with standard fields and custom request context fields
        format_str = '%(asctime)s %(levelname)s %(name)s %(message)s %(api_key_id)s %(request_id)s %(endpoint)s %(method)s %(user_agent)s'
        formatter = JsonFormatter(
            format_str,
            rename_fields={
                'asctime': 'timestamp',
                'levelname': 'level',
                'name': 'logger'
            },
            timestamp=True
        )
    else:
        # Use a standard formatter for testing/development
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s - [API Key: %(api_key_id)s] [Request: %(request_id)s] [%(method)s %(endpoint)s]'
        )

    # Create and add the request context filter
    context_filter = RequestContextFilter()

    # Add filter to both the handler and root logger
    console_handler.addFilter(context_filter)
    root_logger.addFilter(context_filter)

    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # Add filter to all existing loggers
    for name in logging.root.manager.loggerDict:
        logger = logging.getLogger(name)
        logger.addFilter(context_filter)

    mode = "JSON" if use_json_formatter else "standard"
    root_logger.info(f"{mode} structured logging configured with request context support.")

def get_logger(name):
    """
    Returns a logger instance for a given module name.
    """
    return logging.getLogger(name)