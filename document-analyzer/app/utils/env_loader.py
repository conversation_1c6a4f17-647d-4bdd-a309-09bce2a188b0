"""
Environment variable loader.

This module loads environment variables from a .env file if it exists.
"""

import os
from pathlib import Path
from dotenv import load_dotenv


def load_env_file():
    """
    Load environment variables from .env file if it exists.
    """
    # Get the project root directory (parent of app directory)
    project_root = Path(__file__).parent.parent.parent

    # Path to .env file
    env_file = project_root / ".env"

    # Load .env file if it exists
    if env_file.exists():
        load_dotenv(dotenv_path=env_file)
        print(f"Loaded environment variables from {env_file}")
    else:
        print("No .env file found. Using environment variables from the system.")
