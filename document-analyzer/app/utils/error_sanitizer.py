"""
Error message sanitization utilities.

This module provides functions to sanitize error messages by removing references to
underlying technologies, platforms, or services.
"""

import re
from typing import Dict, List, Union, Any


def sanitize_error_message(error_message: str) -> str:
    """
    Sanitize an error message by removing references to underlying technologies.

    Args:
        error_message (str): The original error message

    Returns:
        str: The sanitized error message
    """
    if not error_message:
        return error_message

    # List of patterns to replace
    replacements = [
        # Google Generative AI related
        (r'(?i)Google\s+Generative\s+AI', 'the document processing service'),
        (r'(?i)Google\s+AI', 'the document processing service'),
        (r'(?i)Vertex\s+AI', 'the document processing service'),
        (r'(?i)Google\s+Cloud', 'the cloud service'),

        # Error codes and specific error messages
        (r'400\s+INVALID_ARGUMENT', 'Invalid request'),
        (r'500\s+INTERNAL', 'Internal server error'),
        (r'404\s+NOT_FOUND', 'Resource not found'),
        (r'403\s+PERMISSION_DENIED', 'Permission denied'),

        # JSON error structure from Google APIs
        (r"{'error':\s*{'code':\s*\d+,\s*'message':\s*'([^']+)'[^}]*}}", r'"\1"'),

        # URL errors from Vertex AI
        (r'(?i)URL is allowed to be crawled by Vertex AI', 'URL is accessible by our service'),
        (r'(?i)Cannot fetch content from the provided URL. Please ensure the URL is valid and accessible by Vertex AI',
         'Cannot fetch content from the provided URL. Please ensure the URL is valid and publicly accessible'),
        (r'(?i)URL_ERROR-ERROR_OTHER', 'URL access error'),

        # Status codes with technology names
        (r'\d{3}:\s*Error processing document:\s*\d{3}\s+[A-Z_]+\.\s*', 'Error processing document: '),

        # Specific complex error pattern from test case
        (r'500: Error processing document: 400 INVALID_ARGUMENT\. {\'error\': {\'code\': 400, \'message\': \'Cannot fetch content from the provided URL\. Please ensure the URL is valid and accessible by Vertex AI\. Vertex AI respects robots\.txt rules, so confirm the URL is allowed to be crawled\. Status: URL_ERROR-ERROR_OTHER\', \'status\': \'INVALID_ARGUMENT\'}}',
         'Error processing document: Cannot fetch content from the provided URL. Please ensure the URL is valid and publicly accessible.')
    ]

    # Apply all replacements
    sanitized_message = error_message
    for pattern, replacement in replacements:
        sanitized_message = re.sub(pattern, replacement, sanitized_message)

    return sanitized_message


def sanitize_error_response(error_response: Union[Dict[str, Any], str]) -> Union[Dict[str, Any], str]:
    """
    Sanitize an error response object or string.

    Args:
        error_response: The original error response (dict or string)

    Returns:
        The sanitized error response (same type as input)
    """
    if isinstance(error_response, str):
        return sanitize_error_message(error_response)

    if isinstance(error_response, dict):
        sanitized_response = {}
        for key, value in error_response.items():
            if isinstance(value, str):
                # Sanitize all string values, not just 'detail'
                sanitized_response[key] = sanitize_error_message(value)
            elif isinstance(value, (dict, list)):
                sanitized_response[key] = sanitize_error_response(value)
            else:
                sanitized_response[key] = value
        return sanitized_response

    if isinstance(error_response, list):
        return [sanitize_error_response(item) for item in error_response]

    return error_response
