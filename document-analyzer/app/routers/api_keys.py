"""
Router for API key endpoints.

This module contains API endpoints for managing API keys.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

from app.database import get_db
from app.models.api_key_models import (
    APIKey, APIKeyCreate, APIKeyUpdate, APIKeyWithValue,
    APIKeyUsage, APIKeyUsageStats, PaginatedAPIKey, PaginatedAPIKeyUsage
)
from app.repositories import api_key_repository

router = APIRouter(
    prefix="/api-keys",
    tags=["api-keys"],
    responses={404: {"description": "Not found"}},
)


@router.post("", response_model=APIKeyWithValue)
async def create_api_key(
    api_key: APIKeyCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new API key.

    Args:
        api_key (APIKeyCreate): API key data
        db (Session, optional): Database session. Defaults to Depends(get_db).

    Returns:
        APIKeyWithValue: Created API key with the actual key value
    """
    db_api_key, key_value = api_key_repository.create_api_key(
        db=db,
        name=api_key.name,
        description=api_key.description,
        is_active=api_key.is_active
    )

    # Return the API key with the actual key value
    return {
        **db_api_key.__dict__,
        "key_value": key_value
    }


@router.get("", response_model=PaginatedAPIKey)
async def get_api_keys(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    include_inactive: bool = Query(False),
    db: Session = Depends(get_db)
):
    """
    Get all API keys.

    Args:
        skip (int, optional): Number of records to skip. Defaults to 0.
        limit (int, optional): Maximum number of records to return. Defaults to 100.
        include_inactive (bool, optional): Whether to include inactive API keys. Defaults to False.
        db (Session, optional): Database session. Defaults to Depends(get_db).

    Returns:
        PaginatedAPIKey: Paginated list of API keys
    """
    items, total = api_key_repository.get_all_api_keys(
        db=db,
        skip=skip,
        limit=limit,
        include_inactive=include_inactive
    )

    return PaginatedAPIKey.create(
        items=items,
        total=total,
        page=skip // limit + 1,
        page_size=limit
    )


@router.get("/{api_key_id}", response_model=APIKey)
async def get_api_key(
    api_key_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Get an API key by ID.

    Args:
        api_key_id (UUID): API key ID
        db (Session, optional): Database session. Defaults to Depends(get_db).

    Raises:
        HTTPException: If the API key is not found

    Returns:
        APIKey: API key
    """
    db_api_key = api_key_repository.get_api_key(db, api_key_id)

    if not db_api_key:
        raise HTTPException(
            status_code=404,
            detail=f"API key with ID {api_key_id} not found"
        )

    return db_api_key


@router.put("/{api_key_id}", response_model=APIKey)
async def update_api_key(
    api_key_id: UUID,
    api_key: APIKeyUpdate,
    db: Session = Depends(get_db)
):
    """
    Update an API key.

    Args:
        api_key_id (UUID): API key ID
        api_key (APIKeyUpdate): Updated API key data
        db (Session, optional): Database session. Defaults to Depends(get_db).

    Raises:
        HTTPException: If the API key is not found

    Returns:
        APIKey: Updated API key
    """
    update_data = api_key.model_dump(exclude_unset=True)

    db_api_key = api_key_repository.update_api_key(
        db=db,
        api_key_id=api_key_id,
        **update_data
    )

    if not db_api_key:
        raise HTTPException(
            status_code=404,
            detail=f"API key with ID {api_key_id} not found"
        )

    return db_api_key


@router.delete("/{api_key_id}", response_model=dict)
async def delete_api_key(
    api_key_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Delete an API key.

    Args:
        api_key_id (UUID): API key ID
        db (Session, optional): Database session. Defaults to Depends(get_db).

    Raises:
        HTTPException: If the API key is not found

    Returns:
        dict: Success message
    """
    success = api_key_repository.delete_api_key(db, api_key_id)

    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"API key with ID {api_key_id} not found"
        )

    return {"message": "API key deleted successfully"}


@router.get("/{api_key_id}/usage", response_model=PaginatedAPIKeyUsage)
async def get_api_key_usage(
    api_key_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    Get usage records for an API key.

    Args:
        api_key_id (UUID): API key ID
        skip (int, optional): Number of records to skip. Defaults to 0.
        limit (int, optional): Maximum number of records to return. Defaults to 100.
        db (Session, optional): Database session. Defaults to Depends(get_db).

    Raises:
        HTTPException: If the API key is not found

    Returns:
        PaginatedAPIKeyUsage: Paginated list of API key usage records
    """
    # Check if the API key exists
    db_api_key = api_key_repository.get_api_key(db, api_key_id)

    if not db_api_key:
        raise HTTPException(
            status_code=404,
            detail=f"API key with ID {api_key_id} not found"
        )

    items, total = api_key_repository.get_api_key_usage(
        db=db,
        api_key_id=api_key_id,
        skip=skip,
        limit=limit
    )

    return PaginatedAPIKeyUsage.create(
        items=items,
        total=total,
        page=skip // limit + 1,
        page_size=limit
    )


@router.get("/{api_key_id}/stats", response_model=APIKeyUsageStats)
async def get_api_key_stats(
    api_key_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Get usage statistics for an API key.

    Args:
        api_key_id (UUID): API key ID
        db (Session, optional): Database session. Defaults to Depends(get_db).

    Raises:
        HTTPException: If the API key is not found

    Returns:
        APIKeyUsageStats: Usage statistics
    """
    # Check if the API key exists
    db_api_key = api_key_repository.get_api_key(db, api_key_id)

    if not db_api_key:
        raise HTTPException(
            status_code=404,
            detail=f"API key with ID {api_key_id} not found"
        )

    stats = api_key_repository.get_api_key_usage_stats(db, api_key_id)

    return stats
