import asyncio
import time
import copy
import logging
import tempfile
import os
import traceback
from datetime import datetime, timedelta, timezone  # Make sure timedelta and timezone are imported
from typing import Dict, Any, Optional
from uuid import UUID
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Query, Depends, Response, Body
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from app.utils.logging_config import api_key_id_context

# Set up logger
logger = logging.getLogger(__name__)

from app.services.google_genai_service import process_document, process_document_from_url, get_latest_llm_configuration
from app.services.google_cloud_storage_service import upload_file
from app.models.document_processing_log_models import DocumentProcessingLog, PaginatedDocumentProcessingLog
from app.repositories.document_processing_log_repository import (
    create_document_processing_log,
    get_paginated_document_processing_logs,
    get_document_processing_log,
    delete_document_processing_log
)
from app.models.validation_models import ValidationResult<PERSON>reate, ValidationResult, ValidationResultsResponse
from app.repositories.validation_repository import (
    create_validation_result,
    get_validation_results_by_log_id,
    get_validation_result,
    update_validation_result
)
from app.utils.error_sanitizer import sanitize_error_message
from app.database import get_db

router = APIRouter(
    prefix="/documents",
    tags=["documents"],
    responses={404: {"description": "Not found"}},
)


def remove_confidence_scores(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Remove confidence scores from the response data.

    This function transforms fields with confidence scores from:
    {"field": {"confidence": 0.9, "value": "some value"}}

    To:
    {"field": "some value"}

    Args:
        data: The response data containing confidence scores

    Returns:
        Dict with confidence scores removed
    """
    # Create a deep copy to avoid modifying the original data
    result = copy.deepcopy(data)

    # Fields that might contain confidence scores
    fields_to_check = [
        "vendor_name", "invoice_number", "invoice_date",
        "tax_invoice_number", "tax_invoice_date", "invoice_amount",
        "vat_amount", "total_ppn", "document_type"
    ]

    # Process each field
    for field in fields_to_check:
        if field in result and isinstance(result[field], dict) and "value" in result[field]:
            # Replace the field with just its value
            result[field] = result[field]["value"]

    return result


@router.post("/upload")
async def upload_document(file: UploadFile = File(...)):
    """
    Upload a new document to Google Cloud Storage.

    Args:
        file: The file to upload

    Returns:
        Dict containing the file information and storage URLs
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="File has no filename")

    try:
        # Upload the file to Google Cloud Storage
        # For documents, store them in a 'documents' folder
        gs_uri, public_url = await upload_file(file, folder="documents")

        return {
            "filename": file.filename,
            "content_type": file.content_type,
            "gs_uri": gs_uri,
            "public_url": public_url,
            "status": "uploaded"
        }
    except Exception as e:
        # Sanitize the error message to remove references to underlying technologies
        sanitized_error = sanitize_error_message(str(e))
        raise HTTPException(status_code=500, detail=f"Error uploading file: {sanitized_error}")


@router.get("/processing-logs", response_model=PaginatedDocumentProcessingLog)
async def get_document_processing_logs(
    page: int = Query(1, ge=1, description="Page number, starting from 1"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    db: Session = Depends(get_db)
):
    """
    Get paginated document processing logs ordered by recently added (created_at desc).

    Args:
        page: Page number, starting from 1
        page_size: Number of items per page (1-100)
        db: Database session

    Returns:
        PaginatedDocumentProcessingLog: Paginated list of document processing logs
    """
    # Convert page to skip/limit for the database query
    skip = (page - 1) * page_size

    # Get paginated results and total count
    items, total = get_paginated_document_processing_logs(db, skip=skip, limit=page_size)

    # Create and return the paginated response
    return PaginatedDocumentProcessingLog.create(
        items=items,
        total=total,
        page=page,
        page_size=page_size
    )


@router.get("/processing-logs/signed-url")
async def get_blob_signed_url(
    blob_name: str = Query(..., description="The path to the blob in the bucket (e.g., 'documents/file.pdf')"),
    expiration: int = Query(3600, description="URL expiration time in seconds"),
    content_type: Optional[str] = Query(None, description="Content type for the signed URL"),
    download: bool = Query(False, description="Whether to force download instead of viewing in browser")
):
    """
    Generate a signed URL for accessing a file in Google Cloud Storage by blob name.

    Args:
        blob_name: The path to the blob in the bucket (e.g., 'documents/file.pdf')
        expiration: URL expiration time in seconds (default: 1 hour)
        content_type: Optional content type for the signed URL
        download: Whether to force download instead of viewing in browser

    Returns:
        Dict containing the signed URL and expiration information
    """
    try:
        from app.services.google_cloud_storage_service import generate_signed_url

        # Generate the signed URL
        signed_url = generate_signed_url(
            blob_name=blob_name,
            expiration=expiration,
            content_type=content_type,
            download=download
        )

        # Calculate expiration timestamp
        expiration_time = datetime.now(timezone.utc) + timedelta(seconds=expiration)

        return {
            "blob_name": blob_name,
            "signed_url": signed_url,
            "expires_at": expiration_time.isoformat(),
            "expiration_seconds": expiration,
            "download": download
        }
    except Exception as e:
        # Sanitize the error message to remove references to underlying technologies
        sanitized_error = sanitize_error_message(str(e))
        raise HTTPException(status_code=500, detail=f"Error generating signed URL: {sanitized_error}")


@router.get("/processing-logs/view-file")
async def view_file(
    blob_name: str = Query(..., description="The path to the blob in the bucket (e.g., 'documents/file.pdf')"),
    download: bool = Query(False, description="Whether to force download instead of viewing in browser"),
    expiration: int = Query(3600, description="URL expiration time in seconds")
):
    """
    Generate a signed URL and redirect the browser to view or download the file directly.

    Args:
        blob_name: The path to the blob in the bucket (e.g., 'documents/file.pdf')
        download: Whether to force download instead of viewing in browser
        expiration: URL expiration time in seconds (default: 1 hour)

    Returns:
        Redirects to the signed URL for direct viewing or downloading
    """
    try:
        from app.services.google_cloud_storage_service import generate_signed_url

        # For PDFs, we can specify the response type
        content_type = None
        if blob_name.lower().endswith('.pdf'):
            content_type = "application/pdf"

        # Generate the signed URL with all parameters included in the signature
        signed_url = generate_signed_url(
            blob_name=blob_name,
            expiration=expiration,
            content_type=content_type,
            download=download
        )

        # Redirect to the signed URL
        return RedirectResponse(url=signed_url)

    except Exception as e:
        # Sanitize the error message to remove references to underlying technologies
        sanitized_error = sanitize_error_message(str(e))
        raise HTTPException(status_code=500, detail=f"Error generating signed URL: {sanitized_error}")


@router.get("/processing-logs/download-file")
async def download_file(
    blob_name: str = Query(..., description="The path to the blob in the bucket (e.g., 'documents/file.pdf')"),
    expiration: int = Query(3600, description="URL expiration time in seconds")
):
    """
    Generate a pre-signed URL specifically for downloading a file and redirect to it.

    Args:
        blob_name: The path to the blob in the bucket (e.g., 'documents/file.pdf')
        expiration: URL expiration time in seconds (default: 1 hour)

    Returns:
        Redirects to the pre-signed URL for downloading the file
    """
    try:
        from app.services.google_cloud_storage_service import generate_signed_url

        # For PDFs, we can specify the content type
        content_type = None
        if blob_name.lower().endswith('.pdf'):
            content_type = "application/pdf"

        # Generate a signed URL with download=True to force download
        signed_url = generate_signed_url(
            blob_name=blob_name,
            expiration=expiration,
            content_type=content_type,
            download=True
        )

        # Redirect to the signed URL
        return RedirectResponse(url=signed_url)

    except Exception as e:
        # Sanitize the error message to remove references to underlying technologies
        sanitized_error = sanitize_error_message(str(e))
        raise HTTPException(status_code=500, detail=f"Error generating download URL: {sanitized_error}")


@router.get("/processing-logs/{log_id}", response_model=DocumentProcessingLog)
async def get_document_processing_log_by_id(
    log_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Get a specific document processing log by ID.

    Args:
        log_id: The ID of the document processing log to retrieve
        db: Database session

    Returns:
        DocumentProcessingLog: The requested document processing log

    Raises:
        HTTPException: If the document processing log is not found
    """
    log = get_document_processing_log(db, log_id)
    if not log:
        raise HTTPException(
            status_code=404,
            detail=f"Document processing log with ID {log_id} not found"
        )
    return log


async def analyze_file_content(
    file_content: bytes,
    filename: str,
    document_type: str,
    show_confidence: bool = False,
    db: Session = None,
    debug_timing: bool = False,
    api_key_id: Optional[UUID] = None
) -> Dict[str, Any]:
    """
    Core function to analyze document content using Google's Generative AI.

    This function:
    1. Uploads the file content to Google Cloud Storage
    2. Gets the latest LLM configuration from the database
    3. Processes the uploaded PDF file with Generative AI
    4. Logs the document processing activity to the database
    5. Returns the results

    Args:
        file_content: The PDF file content as bytes
        filename: The name of the file
        document_type: Type of invoice document (standard_invoice or tax_invoice)
        vendor_name: Name of the vendor
        invoice_amount: Total amount of the invoice
        vat_amount: Total VAT amount of the invoice
        invoice_date: Date of the invoice (for standard_invoice)
        invoice_number: Number of the invoice (for standard_invoice)
        tax_invoice_date: Date of the tax invoice (for tax_invoice)
        tax_invoice_number: Number of the tax invoice (for tax_invoice)
        show_confidence: Whether to include confidence scores in the response
        db: Database session
        debug_timing: Show timing information for debugging

    Returns:
        JSON response with LLM results and document processing log ID
    """
    logger.debug(f"Analyzing document content: {document_type}, filename: {filename}")

    if not filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="File must be a PDF")

    start_time = time.time()
    timings = {}

    try:
        # Get document size
        document_size = len(file_content)  # Get the document size in bytes

        # Get the latest LLM configuration
        config_start = time.time()
        llm_config = get_latest_llm_configuration(db, document_type)
        timings["get_llm_config"] = time.time() - config_start

        if not llm_config:
            raise HTTPException(
                status_code=400,
                detail=f"No LLM configuration found for document type: {document_type}. Please create a configuration first."
            )

        # Create a temporary file to upload to GCS
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # Create an UploadFile object from the temporary file
            with open(temp_file_path, 'rb') as f:
                upload_file_obj = UploadFile(
                    file=f,
                    filename=filename
                )

                # Upload file to GCS
                upload_start = time.time()
                gs_uri, _ = await upload_file(upload_file_obj, folder="documents")
                timings["upload_file"] = time.time() - upload_start
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

        # Extract blob_name from gs_uri (format: gs://bucket-name/path/to/file)
        blob_name = gs_uri.split('/', 3)[3] if len(gs_uri.split('/', 3)) > 3 else gs_uri

        # Process document with LLM
        process_start = time.time()

        user_prompt = f"Extract this {document_type}"

        process_task = asyncio.create_task(process_document(
            gs_uri=gs_uri,
            user_prompt=user_prompt,
            db=db,
            document_type=document_type
        ))

        # Wait for the processing task to complete
        process_result = await process_task
        timings["process_document"] = time.time() - process_start

        # Extract the data from the process result
        extracted_data = process_result["extracted_data"]
        raw_llm_response = process_result["raw_response"]

        logger.debug(f"Raw LLM response: {raw_llm_response}")

        # Add the GCS URI to the extracted data
        extracted_data["gs_uri"] = gs_uri

        # Log the document processing activity
        log_start = time.time()
        log = create_document_processing_log(
            db=db,
            blob_name=blob_name,
            api_key_id=api_key_id,
            document_size=document_size,
            llm_configuration_id=llm_config.id,
            raw_llm_response=raw_llm_response,  # Store the raw LLM response
            extracted_data=extracted_data,      # Store the extracted data
            color_analysis_results=None,        # Color analysis has been removed
            processing_time=time.time() - start_time,
            status="success"  # Explicitly set status to success
        )
        timings["create_log"] = time.time() - log_start

        # Add the document processing log ID to the result
        extracted_data["document_processing_log_id"] = log.id

        # Remove confidence scores if show_confidence is False
        result = extracted_data
        if not show_confidence:
            result = remove_confidence_scores(result)

        # Add timing information if debug_timing is True
        if debug_timing:
            result["_debug_timings"] = timings
            result["_total_time"] = time.time() - start_time

        return result
    except Exception as e:
        # Log the error to the database
        error_time = time.time() - start_time
        error_details = {
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": traceback.format_exc()
        }

        try:
            # Only create a log if we have the necessary information
            if 'blob_name' in locals() and 'llm_config' in locals() and db is not None:
                # Create a document processing log with error status
                create_document_processing_log(
                    db=db,
                    blob_name=blob_name,
                    api_key_id=api_key_id,
                    document_size=document_size,
                    llm_configuration_id=llm_config.id,
                    raw_llm_response=error_details,  # Store error details in raw_llm_response
                    extracted_data={},  # Empty extracted data
                    color_analysis_results=None,
                    processing_time=error_time,
                    status="error"  # Set status to error
                )
                logger.error(f"Document processing error logged: {str(e)}")
            else:
                logger.error(f"Could not log document processing error: {str(e)}")
        except Exception as log_error:
            # If logging the error fails, log to console but don't fail the request
            logger.error(f"Failed to log document processing error: {str(log_error)}")

        # Sanitize the error message to remove references to underlying technologies
        sanitized_error = sanitize_error_message(str(e))
        raise HTTPException(status_code=500, detail=f"Error analyzing document: {sanitized_error}")


async def analyze(
    file: UploadFile,
    document_type: str,
    show_confidence: bool = False,
    db: Session = None,
    debug_timing: bool = False,
    api_key_id: Optional[UUID] = None
) -> Dict[str, Any]:
    """
    Analyze an invoice document file using Google's Generative AI.

    This function:
    1. Reads the file content
    2. Passes it to analyze_file_content for processing

    Args:
        file: The PDF file to analyze
        document_type: Type of invoice document (standard_invoice or tax_invoice)
        vendor_name: Name of the vendor
        invoice_amount: Total amount of the invoice
        vat_amount: Total VAT amount of the invoice
        invoice_date: Date of the invoice (for standard_invoice)
        invoice_number: Number of the invoice (for standard_invoice)
        tax_invoice_date: Date of the tax invoice (for tax_invoice)
        tax_invoice_number: Number of the tax invoice (for tax_invoice)
        show_confidence: Whether to include confidence scores in the response
        db: Database session
        debug_timing: Show timing information for debugging

    Returns:
        JSON response with LLM results and document processing log ID
    """
    logger.debug(f"Analyzing document file: {document_type}")

    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="File must be a PDF")

    try:
        # Read file content
        content = await file.read()

        # Reset file position
        await file.seek(0)

        # Process the file content
        return await analyze_file_content(
            file_content=content,
            filename=file.filename,
            document_type=document_type,
            show_confidence=show_confidence,
            db=db,
            debug_timing=debug_timing,
            api_key_id=api_key_id
        )
    except Exception as e:
        # Sanitize the error message to remove references to underlying technologies
        sanitized_error = sanitize_error_message(str(e))
        raise HTTPException(status_code=500, detail=f"Error analyzing document: {sanitized_error}")


async def analyze_url(
    file_url: str,
    document_type: str,
    show_confidence: bool = False,
    db: Session = None,
    debug_timing: bool = False,
    api_key_id: Optional[UUID] = None
) -> Dict[str, Any]:
    """
    Analyze an invoice document from a URL using Google's Generative AI.

    This function:
    1. Creates a user prompt from the input parameters
    2. Processes the document directly from the URL using Google's Generative AI
    3. Logs the document processing activity to the database
    4. Returns the results

    Args:
        file_url: URL of the PDF file to analyze
        document_type: Type of invoice document (standard_invoice or tax_invoice)
        vendor_name: Name of the vendor
        invoice_amount: Total amount of the invoice
        vat_amount: Total VAT amount of the invoice
        invoice_date: Date of the invoice (for standard_invoice)
        invoice_number: Number of the invoice (for standard_invoice)
        tax_invoice_date: Date of the tax invoice (for tax_invoice)
        tax_invoice_number: Number of the tax invoice (for tax_invoice)
        show_confidence: Whether to include confidence scores in the response
        db: Database session
        debug_timing: Show timing information for debugging

    Returns:
        JSON response with LLM results and document processing log ID
    """
    logger.debug(f"Analyzing document from URL: {document_type}, URL: {file_url}")

    start_time = time.time()
    timings = {}

    try:
        # Get the latest LLM configuration
        config_start = time.time()
        llm_config = get_latest_llm_configuration(db, document_type)
        timings["get_llm_config"] = time.time() - config_start

        if not llm_config:
            raise HTTPException(
                status_code=400,
                detail=f"No LLM configuration found for document type: {document_type}. Please create a configuration first."
            )

        user_prompt = f"Extract this {document_type}"

        # Process document with LLM directly from URL
        process_start = time.time()
        process_result = await process_document_from_url(
            file_url=file_url,
            user_prompt=user_prompt,
            db=db,
            document_type=document_type
        )
        timings["process_document"] = time.time() - process_start

        # Extract the data from the process result
        extracted_data = process_result["extracted_data"]
        raw_llm_response = process_result["raw_response"]

        logger.debug(f"Raw LLM response: {raw_llm_response}")

        # Add the URL to the extracted data
        extracted_data["file_url"] = file_url

        # Extract filename from URL for blob_name
        from urllib.parse import urlparse
        path = urlparse(file_url).path
        filename = os.path.basename(path)
        if not filename:
            filename = f"url_document_{int(time.time())}.pdf"

        blob_name = f"documents/{filename}"

        # Estimate document size (we don't have the actual size)
        document_size = 0  # We don't know the size without downloading

        # Log the document processing activity
        log_start = time.time()
        log = create_document_processing_log(
            db=db,
            blob_name=blob_name,
            api_key_id=api_key_id,
            document_size=document_size,
            llm_configuration_id=llm_config.id,
            raw_llm_response=raw_llm_response,  # Store the raw LLM response
            extracted_data=extracted_data,      # Store the extracted data
            color_analysis_results=None,        # Color analysis has been removed
            processing_time=time.time() - start_time,
            status="success"  # Explicitly set status to success
        )
        timings["create_log"] = time.time() - log_start

        # Add the document processing log ID to the result
        extracted_data["document_processing_log_id"] = log.id

        # Remove confidence scores if show_confidence is False
        result = extracted_data
        if not show_confidence:
            result = remove_confidence_scores(result)

        # Add timing information if debug_timing is True
        if debug_timing:
            result["_debug_timings"] = timings
            result["_total_time"] = time.time() - start_time

        return result
    except Exception as e:
        # Log the error to the database
        error_time = time.time() - start_time
        error_details = {
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": traceback.format_exc()
        }

        try:
            # Only create a log if we have the necessary information
            if 'file_url' in locals() and 'llm_config' in locals() and db is not None:
                # Extract filename from URL for blob_name if not already done
                if 'blob_name' not in locals():
                    from urllib.parse import urlparse
                    path = urlparse(file_url).path
                    filename = os.path.basename(path)
                    if not filename:
                        filename = f"url_document_{int(time.time())}.pdf"
                    blob_name = f"documents/{filename}"

                # Create a document processing log with error status
                create_document_processing_log(
                    db=db,
                    blob_name=blob_name,
                    api_key_id=api_key_id,
                    document_size=0,  # We don't know the size for URL documents
                    llm_configuration_id=llm_config.id,
                    raw_llm_response=error_details,  # Store error details in raw_llm_response
                    extracted_data={},  # Empty extracted data
                    color_analysis_results=None,
                    processing_time=error_time,
                    status="error"  # Set status to error
                )
                logger.error(f"Document processing error logged for URL: {file_url}")
            else:
                logger.error(f"Could not log document processing error for URL: {str(e)}")
        except Exception as log_error:
            # If logging the error fails, log to console but don't fail the request
            logger.error(f"Failed to log document processing error: {str(log_error)}")

        # Sanitize the error message to remove references to underlying technologies
        sanitized_error = sanitize_error_message(str(e))
        raise HTTPException(status_code=500, detail=f"Error analyzing document from URL: {sanitized_error}")


@router.post("/invoice-file")
async def analyze_invoice_file(
    file: UploadFile = File(...),
    show_confidence: bool = Form(False, description="Whether to include confidence scores in the response"),
    db: Session = Depends(get_db),
    debug_timing: bool = Form(False, description="Show timing information for debugging")
):
    """
    Analyze a standard invoice document file using Google's Generative AI.

    This endpoint:
    1. Uploads the file to Google Cloud Storage
    2. Gets the latest LLM configuration from the database
    3. Processes the uploaded PDF file with Generative AI
    4. Logs the document processing activity to the database
    5. Returns the results

    Args:
        file: The PDF file to analyze
        show_confidence: Whether to include confidence scores in the response (default: False)
        db: Database session
        debug_timing: Show timing information for debugging

    Returns:
        JSON response with LLM results and document processing log ID
    """
    logger.debug("Analyzing standard invoice document file")

    return await analyze(
        file=file,
        document_type="standard_invoice",
        show_confidence=show_confidence,
        db=db,
        debug_timing=debug_timing,
        api_key_id=api_key_id_context.get()
    )


@router.post("/invoice")
async def analyze_invoice_url(
    file_url: str = Body(..., description="URL of the PDF file to analyze"),
    show_confidence: bool = Body(False, description="Whether to include confidence scores in the response"),
    db: Session = Depends(get_db),
    debug_timing: bool = Body(False, description="Show timing information for debugging")
):
    """
    Analyze a standard invoice document from a URL using Google's Generative AI.

    This endpoint:
    1. Downloads the PDF file from the provided URL
    2. Uploads the file to Google Cloud Storage
    3. Gets the latest LLM configuration from the database
    4. Processes the PDF file with Generative AI
    5. Logs the document processing activity to the database
    6. Returns the results

    Args:
        file_url: URL of the PDF file to analyze
        show_confidence: Whether to include confidence scores in the response (default: False)
        db: Database session
        debug_timing: Show timing information for debugging

    Returns:
        JSON response with LLM results and document processing log ID
    """
    logger.debug("Analyzing standard invoice document from URL")

    return await analyze_url(
        file_url=file_url,
        document_type="standard_invoice",
        show_confidence=show_confidence,
        db=db,
        debug_timing=debug_timing,
        api_key_id=api_key_id_context.get()
    )


@router.post("/tax-invoice-file")
async def analyze_tax_invoice_file(
    file: UploadFile = File(...),
    show_confidence: bool = Form(False, description="Whether to include confidence scores in the response"),
    db: Session = Depends(get_db),
    debug_timing: bool = Form(False, description="Show timing information for debugging")
):
    """
    Analyze a tax invoice document file using Google's Generative AI.

    This endpoint:
    1. Uploads the file to Google Cloud Storage
    2. Gets the latest LLM configuration from the database
    3. Processes the uploaded PDF file with Generative AI
    4. Logs the document processing activity to the database
    5. Returns the results

    Required fields:
    - file: The PDF file to analyze
    - show_confidence: Whether to include confidence scores in the response (default: False)
    - db: Database session
    - debug_timing: Show timing information for debugging

    Returns:
        JSON response with LLM results and document processing log ID
    """
    logger.debug("Analyzing tax invoice document file")

    return await analyze(
        file=file,
        document_type="tax_invoice",
        show_confidence=show_confidence,
        db=db,
        debug_timing=debug_timing,
        api_key_id=api_key_id_context.get()
    )


@router.post("/tax-invoice")
async def analyze_tax_invoice_url(
    file_url: str = Body(..., description="URL of the PDF file to analyze"),
    show_confidence: bool = Body(False, description="Whether to include confidence scores in the response"),
    db: Session = Depends(get_db),
    debug_timing: bool = Body(False, description="Show timing information for debugging")
):
    """
    Analyze a tax invoice document from a URL using Google's Generative AI.

    This endpoint:
    1. Downloads the PDF file from the provided URL
    2. Uploads the file to Google Cloud Storage
    3. Gets the latest LLM configuration from the database
    4. Processes the PDF file with Generative AI
    5. Logs the document processing activity to the database
    6. Returns the results

    Required fields:
    - file_url: URL of the PDF file to analyze
    - show_confidence: Whether to include confidence scores in the response (default: False)
    - db: Database session
    - debug_timing: Show timing information for debugging

    Returns:
        JSON response with LLM results and document processing log ID
    """
    logger.debug("Analyzing tax invoice document from URL")

    return await analyze_url(
        file_url=file_url,
        document_type="tax_invoice",
        show_confidence=show_confidence,
        db=db,
        debug_timing=debug_timing,
        api_key_id=api_key_id_context.get()
    )


@router.post("/processing-logs/{log_id}/validations", response_model=ValidationResult)
async def create_or_update_validation_result(
    log_id: UUID,
    validation: ValidationResultCreate,
    db: Session = Depends(get_db)
):
    """
    Create or update a validation result for an extracted field.

    Args:
        log_id: The ID of the document processing log
        validation: The validation data (field_key and is_valid)
        db: Database session

    Returns:
        ValidationResult: The created or updated validation result

    Raises:
        HTTPException: If the document processing log is not found
    """
    # Check if the document processing log exists
    log = get_document_processing_log(db, log_id)
    if not log:
        raise HTTPException(
            status_code=404,
            detail=f"Document processing log with ID {log_id} not found"
        )

    # Check if the field exists in the extracted data
    if not log.extracted_data or validation.field_key not in log.extracted_data:
        raise HTTPException(
            status_code=400,
            detail=f"Field '{validation.field_key}' not found in extracted data"
        )

    # Check if validation already exists for this field
    existing_validation = get_validation_result(db, log_id, validation.field_key)

    if existing_validation:
        # Update existing validation
        return update_validation_result(
            db=db,
            validation_id=existing_validation.id,
            is_valid=validation.is_valid
        )
    else:
        # Create new validation
        return create_validation_result(
            db=db,
            document_processing_log_id=log_id,
            field_key=validation.field_key,
            is_valid=validation.is_valid
        )


@router.get("/processing-logs/{log_id}/validations", response_model=ValidationResultsResponse)
async def get_validation_results_for_log(
    log_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Get all validation results for a document processing log.

    Args:
        log_id: The ID of the document processing log
        db: Database session

    Returns:
        ValidationResultsResponse: The validation results

    Raises:
        HTTPException: If the document processing log is not found
    """
    # Check if the document processing log exists
    log = get_document_processing_log(db, log_id)
    if not log:
        raise HTTPException(
            status_code=404,
            detail=f"Document processing log with ID {log_id} not found"
        )

    # Get the validation results
    results = get_validation_results_by_log_id(db, log_id)

    return ValidationResultsResponse(
        document_processing_log_id=log_id,
        results=results
    )


@router.delete("/processing-logs/{log_id}", status_code=204)
async def delete_document_processing_log_by_id(
    log_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Delete a document processing log by ID.

    Args:
        log_id: The ID of the document processing log to delete
        db: Database session

    Returns:
        No content on successful deletion

    Raises:
        HTTPException: If the document processing log is not found
    """
    # Try to delete the document processing log
    deleted = delete_document_processing_log(db, log_id)

    if not deleted:
        raise HTTPException(
            status_code=404,
            detail=f"Document processing log with ID {log_id} not found"
        )

    # Return 204 No Content (successful deletion with no response body)
    return Response(status_code=204)




