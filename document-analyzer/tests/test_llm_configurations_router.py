"""
Tests for the LLM Configurations Router.

This module contains unit tests for the LLM configurations router endpoints.
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi import HTTPException
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from app.database import get_db
from app.models.database_models import LLMConfiguration
from app.repositories import llm_configuration_repository
from tests.utils import configure_test_client


# Override the database dependency
def override_get_db():
    """Override the database dependency for testing."""
    return MagicMock(spec=Session)


# Configure the test client with API key authentication
app.dependency_overrides[get_db] = override_get_db
client, _, patchers = configure_test_client(app)


# Tests for get_latest_config endpoint
@patch('app.repositories.llm_configuration_repository.get_latest_llm_configuration_by_document_type')
def test_get_latest_config_success(mock_get_latest):
    """Test getting the latest LLM configuration successfully."""
    # Create a mock configuration
    mock_config = MagicMock(spec=LLMConfiguration)
    mock_config.id = 42
    mock_config.model_id = "gemini-pro"
    mock_config.provider = "google"
    mock_config.system_instruction = "Test prompt"
    mock_config.document_type = "standard_invoice"
    mock_config.temperature = 0.7
    mock_config.top_p = 1.0
    mock_config.top_k = 5

    # Mock the repository function to return the configuration
    mock_get_latest.return_value = mock_config

    # Make the request with document_type parameter
    response = client.get("/llm-configurations/latest?document_type=standard_invoice")

    # Assert the response
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == 42
    assert data["model_id"] == "gemini-pro"
    assert data["provider"] == "google"
    assert data["system_instruction"] == "Test prompt"
    assert data["document_type"] == "standard_invoice"
    assert data["temperature"] == 0.7
    assert data["top_p"] == 1.0
    assert data["top_k"] == 5

    # Assert the repository function was called with the correct document_type
    mock_get_latest.assert_called_once_with(mock_get_latest.call_args[0][0], "standard_invoice")


@patch('app.repositories.llm_configuration_repository.get_latest_llm_configuration_by_document_type')
def test_get_latest_config_not_found(mock_get_latest):
    """Test getting the latest LLM configuration when none exists."""
    # Mock the repository function to return None
    mock_get_latest.return_value = None

    # Make the request with document_type parameter
    response = client.get("/llm-configurations/latest?document_type=tax_invoice")

    # Assert the response
    assert response.status_code == 404
    assert response.json()["detail"] == "No LLM configuration found for document type: tax_invoice"

    # Assert the repository function was called with the correct document_type
    mock_get_latest.assert_called_once_with(mock_get_latest.call_args[0][0], "tax_invoice")


def test_get_latest_config_missing_document_type():
    """Test getting the latest LLM configuration without providing document_type."""
    # Make the request without document_type parameter
    response = client.get("/llm-configurations/latest")

    # Assert the response
    assert response.status_code == 422  # Unprocessable Entity
    assert "detail" in response.json()
    assert "document_type" in response.json()["detail"][0]["loc"]


# Tests for get_document_types endpoint
@patch('app.repositories.llm_configuration_repository.get_all_document_types')
def test_get_document_types_success(mock_get_all_document_types):
    """Test getting all document types successfully."""
    # Mock the repository function to return document types
    mock_get_all_document_types.return_value = ["standard_invoice", "tax_invoice"]

    # Make the request
    response = client.get("/llm-configurations/document-types")

    # Assert the response
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 2
    assert "standard_invoice" in data
    assert "tax_invoice" in data

    # Assert the repository function was called
    mock_get_all_document_types.assert_called_once()


@patch('app.repositories.llm_configuration_repository.get_all_document_types')
def test_get_document_types_empty(mock_get_all_document_types):
    """Test getting all document types when none exist."""
    # Mock the repository function to return an empty list
    mock_get_all_document_types.return_value = []

    # Make the request
    response = client.get("/llm-configurations/document-types")

    # Assert the response
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 0

    # Assert the repository function was called
    mock_get_all_document_types.assert_called_once()
