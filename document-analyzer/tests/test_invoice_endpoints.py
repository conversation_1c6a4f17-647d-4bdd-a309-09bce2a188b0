"""
Tests for the Invoice and Tax Invoice endpoints.

This module contains unit tests for the invoice and tax invoice endpoints.
"""

import os
import pytest
from unittest.mock import patch, MagicMock, AsyncMock, mock_open
from fastapi import UploadFile, HTTPException
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from app.database import get_db
# PDF color analysis service has been removed
from app.services.google_genai_service import process_document, get_latest_llm_configuration
from app.services.google_cloud_storage_service import upload_file
from app.repositories.document_processing_log_repository import create_document_processing_log


# Override the database dependency
def override_get_db():
    """Override the database dependency for testing."""
    return MagicMock(spec=Session)


app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


@pytest.fixture
def mock_pdf_file():
    """Create a mock PDF file for testing."""
    mock_file = AsyncMock(spec=UploadFile)
    mock_file.filename = "test.pdf"
    mock_file.content_type = "application/pdf"

    # Mock the read method to return some content
    mock_file.read.return_value = b"%PDF-1.5\nsome mock pdf content"

    # Mock the seek method
    mock_file.seek.return_value = None

    return mock_file


@patch('builtins.open', new_callable=mock_open, read_data=b"%PDF-1.5\nsome mock pdf content")
@patch('tempfile.NamedTemporaryFile')
@patch('app.routers.documents.create_document_processing_log')
@patch('app.routers.documents.get_latest_llm_configuration')
@patch('app.routers.documents.process_document')
@patch('app.routers.documents.upload_file')
@pytest.mark.asyncio
async def test_analyze_invoice_success(
    mock_upload_file,
    mock_process_document,
    mock_get_llm_config,
    mock_create_log,
    mock_temp_file,
    mock_open,
    mock_pdf_file
):
    """Test successful invoice analysis."""
    # Mock the temporary file
    mock_temp_file_instance = MagicMock()
    mock_temp_file_instance.name = "/tmp/test.pdf"
    mock_temp_file.return_value.__enter__.return_value = mock_temp_file_instance

    # Mock the LLM configuration
    mock_llm_config = MagicMock()
    mock_llm_config.id = 1
    mock_get_llm_config.return_value = mock_llm_config

    # Mock the upload_file function
    mock_upload_file.return_value = ("gs://bucket/documents/test.pdf", "https://storage.googleapis.com/bucket/documents/test.pdf")

    # Mock the process_document function with the new response format
    mock_process_document.return_value = {
        "extracted_data": {
            "document_type": "standard_invoice",
            "page_count": 1,
            "results": {
                "fields": {
                    "vendor_name": {
                        "ocr_value": "Test Vendor Corp",
                        "ocr_value_normalized": "Test Vendor Corp",
                    },
                    "invoice_number": {
                        "ocr_value": "INV-123",
                        "ocr_value_normalized": "INV-123"
                    },
                    "invoice_date": {
                        "ocr_value": "2023-01-01",
                        "ocr_value_normalized": "2023-01-01"
                    },
                    "invoice_amount": {
                        "ocr_value": "100.00",
                        "ocr_value_normalized": "100.00"
                    },
                    "vat_amount": {
                        "ocr_value": "10.00",
                        "ocr_value_normalized": "10.00"
                    }
                }
            }
        },
        "raw_response": {"candidates": [{"content": {"parts": [{"text": "test"}]}}]}
    }

    # PDF color analysis has been removed

    # Mock the create_document_processing_log function to return a log with ID
    mock_log = MagicMock()
    mock_log.id = 123
    mock_create_log.return_value = mock_log

    # Create a test client using FastAPI's TestClient
    from app.routers.documents import analyze_invoice_file

    # Call the function directly
    result = await analyze_invoice_file(
        file=mock_pdf_file,
        db=override_get_db()
    )

    # Assert the result contains the expected top-level fields
    assert "document_type" in result
    assert "page_count" in result
    assert "results" in result
    assert "gs_uri" in result
    assert "document_processing_log_id" in result

    # Assert the top-level values
    assert result["document_type"] == "standard_invoice"
    assert result["page_count"] == 1
    assert result["gs_uri"] == "gs://bucket/documents/test.pdf"
    assert result["document_processing_log_id"] == 123

    # Assert the results structure
    assert "fields" in result["results"]

    # Assert the fields structure
    fields = result["results"]["fields"]
    assert "vendor_name" in fields
    assert "invoice_number" in fields
    assert "invoice_date" in fields
    assert "invoice_amount" in fields
    assert "vat_amount" in fields

    # Assert the field values
    assert fields["vendor_name"]["ocr_value"] == "Test Vendor Corp"
    assert fields["vendor_name"]["ocr_value_normalized"] == "Test Vendor Corp"
    assert fields["invoice_number"]["ocr_value"] == "INV-123"
    assert fields["invoice_number"]["ocr_value_normalized"] == "INV-123"
    assert fields["invoice_date"]["ocr_value"] == "2023-01-01"
    assert fields["invoice_date"]["ocr_value_normalized"] == "2023-01-01"
    assert fields["invoice_amount"]["ocr_value"] == "100.00"
    assert fields["invoice_amount"]["ocr_value_normalized"] == "100.00"
    assert fields["vat_amount"]["ocr_value"] == "10.00"
    assert fields["vat_amount"]["ocr_value_normalized"] == "10.00"

    # Assert the mocks were called correctly
    mock_pdf_file.read.assert_called_once()
    mock_pdf_file.seek.assert_called_once_with(0)
    mock_upload_file.assert_called_once()
    mock_process_document.assert_called_once()
    mock_get_llm_config.assert_called_once()

    # Assert the document processing log was created
    mock_create_log.assert_called_once()


@patch('builtins.open', new_callable=mock_open, read_data=b"%PDF-1.5\nsome mock pdf content")
@patch('tempfile.NamedTemporaryFile')
@patch('app.routers.documents.create_document_processing_log')
@patch('app.routers.documents.get_latest_llm_configuration')
@patch('app.routers.documents.process_document')
@patch('app.routers.documents.upload_file')
@pytest.mark.asyncio
async def test_analyze_invoice_without_vat_amount(
    mock_upload_file,
    mock_process_document,
    mock_get_llm_config,
    mock_create_log,
    mock_temp_file,
    mock_open,
    mock_pdf_file
):
    """Test successful invoice analysis without vat_amount."""
    # Mock the temporary file
    mock_temp_file_instance = MagicMock()
    mock_temp_file_instance.name = "/tmp/test.pdf"
    mock_temp_file.return_value.__enter__.return_value = mock_temp_file_instance

    # Mock the LLM configuration
    mock_llm_config = MagicMock()
    mock_llm_config.id = 1
    mock_get_llm_config.return_value = mock_llm_config

    # Mock the upload_file function
    mock_upload_file.return_value = ("gs://bucket/documents/test.pdf", "https://storage.googleapis.com/bucket/documents/test.pdf")

    # Mock the process_document function with the new response format
    mock_process_document.return_value = {
        "extracted_data": {
            "document_type": "standard_invoice",
            "page_count": 1,
            "results": {
                "fields": {
                    "vendor_name": {
                        "ocr_value": "Test Vendor Corp",
                        "ocr_value_normalized": "Test Vendor Corp",
                    },
                    "invoice_number": {
                        "ocr_value": "INV-123",
                        "ocr_value_normalized": "INV-123",
                    },
                    "invoice_date": {
                        "ocr_value": "2023-01-01",
                        "ocr_value_normalized": "2023-01-01",
                    },
                    "invoice_amount": {
                        "ocr_value": "100.00",
                        "ocr_value_normalized": "100.00",
                    }
                }
            }
        },
        "raw_response": {"candidates": [{"content": {"parts": [{"text": "test"}]}}]}
    }

    # Mock the create_document_processing_log function to return a log with ID
    mock_log = MagicMock()
    mock_log.id = 123
    mock_create_log.return_value = mock_log

    # Create a test client using FastAPI's TestClient
    from app.routers.documents import analyze_invoice_file

    # Call the function directly without vat_amount
    result = await analyze_invoice_file(
        file=mock_pdf_file,
        db=override_get_db()
    )

    # Assert the result contains the expected top-level fields
    assert "document_type" in result
    assert "page_count" in result
    assert "results" in result
    assert "gs_uri" in result
    assert "document_processing_log_id" in result

    # Assert the top-level values
    assert result["document_type"] == "standard_invoice"
    assert result["page_count"] == 1
    assert result["gs_uri"] == "gs://bucket/documents/test.pdf"
    assert result["document_processing_log_id"] == 123

    # Assert the results structure
    assert "fields" in result["results"]

    # Assert the fields structure
    fields = result["results"]["fields"]
    assert "vendor_name" in fields
    assert "invoice_number" in fields
    assert "invoice_date" in fields
    assert "invoice_amount" in fields

    # vat_amount should not be in the fields since it wasn't provided
    assert "vat_amount" not in fields

    # Assert the mocks were called correctly
    mock_pdf_file.read.assert_called_once()
    mock_pdf_file.seek.assert_called_once_with(0)
    mock_upload_file.assert_called_once()
    mock_process_document.assert_called_once()
    mock_get_llm_config.assert_called_once()

    # Assert the document processing log was created
    mock_create_log.assert_called_once()


@patch('builtins.open', new_callable=mock_open, read_data=b"%PDF-1.5\nsome mock pdf content")
@patch('tempfile.NamedTemporaryFile')
@patch('app.routers.documents.create_document_processing_log')
@patch('app.routers.documents.get_latest_llm_configuration')
@patch('app.routers.documents.process_document')
@patch('app.routers.documents.upload_file')
@pytest.mark.asyncio
async def test_analyze_tax_invoice_success(
    mock_upload_file,
    mock_process_document,
    mock_get_llm_config,
    mock_create_log,
    mock_temp_file,
    mock_open,
    mock_pdf_file
):
    """Test successful tax invoice analysis."""
    # Mock the temporary file
    mock_temp_file_instance = MagicMock()
    mock_temp_file_instance.name = "/tmp/test.pdf"
    mock_temp_file.return_value.__enter__.return_value = mock_temp_file_instance

    # Mock the LLM configuration
    mock_llm_config = MagicMock()
    mock_llm_config.id = 1
    mock_get_llm_config.return_value = mock_llm_config

    # Mock the upload_file function
    mock_upload_file.return_value = ("gs://bucket/documents/test.pdf", "https://storage.googleapis.com/bucket/documents/test.pdf")

    # Mock the process_document function with the new response format
    mock_process_document.return_value = {
        "extracted_data": {
            "document_type": "tax_invoice",
            "page_count": 1,
            "results": {
                "fields": {
                    "vendor_name": {
                        "ocr_value": "Test Vendor Ltd",
                        "ocr_value_normalized": "Test Vendor Ltd",
                    },
                    "tax_invoice_number": {
                        "ocr_value": "TAX-123",
                        "ocr_value_normalized": "TAX-123",
                    },
                    "tax_invoice_date": {
                        "ocr_value": "2023-01-01",
                        "ocr_value_normalized": "2023-01-01"
                    },
                    "invoice_amount": {
                        "ocr_value": "100.00",
                        "ocr_value_normalized": "100.00",
                    },
                    "vat_amount": {
                        "ocr_value": "10.00",
                        "ocr_value_normalized": "10.00"
                    }
                }
            }
        },
        "raw_response": {"candidates": [{"content": {"parts": [{"text": "test"}]}}]}
    }

    # PDF color analysis has been removed

    # Mock the create_document_processing_log function to return a log with ID
    mock_log = MagicMock()
    mock_log.id = 123
    mock_create_log.return_value = mock_log

    # Create a test client using FastAPI's TestClient
    from app.routers.documents import analyze_tax_invoice_file

    # Call the function directly
    result = await analyze_tax_invoice_file(
        file=mock_pdf_file,
        db=override_get_db()
    )

    # Assert the result contains the expected top-level fields
    assert "document_type" in result
    assert "page_count" in result
    assert "results" in result
    assert "gs_uri" in result
    assert "document_processing_log_id" in result

    # Assert the top-level values
    assert result["document_type"] == "tax_invoice"
    assert result["page_count"] == 1
    assert result["gs_uri"] == "gs://bucket/documents/test.pdf"
    assert result["document_processing_log_id"] == 123

    # Assert the results structure
    assert "fields" in result["results"]

    # Assert the fields structure
    fields = result["results"]["fields"]
    assert "vendor_name" in fields
    assert "tax_invoice_number" in fields
    assert "tax_invoice_date" in fields
    assert "invoice_amount" in fields
    assert "vat_amount" in fields

    # Assert the field values
    assert fields["vendor_name"]["ocr_value"] == "Test Vendor Ltd"
    assert fields["vendor_name"]["ocr_value_normalized"] == "Test Vendor Ltd"
    assert fields["tax_invoice_number"]["ocr_value"] == "TAX-123"
    assert fields["tax_invoice_number"]["ocr_value_normalized"] == "TAX-123"
    assert fields["tax_invoice_date"]["ocr_value"] == "2023-01-01"
    assert fields["tax_invoice_date"]["ocr_value_normalized"] == "2023-01-01"
    assert fields["invoice_amount"]["ocr_value"] == "100.00"
    assert fields["invoice_amount"]["ocr_value_normalized"] == "100.00"
    assert fields["vat_amount"]["ocr_value"] == "10.00"
    assert fields["vat_amount"]["ocr_value_normalized"] == "10.00"

    # Assert the mocks were called correctly
    mock_pdf_file.read.assert_called_once()
    mock_pdf_file.seek.assert_called_once_with(0)
    mock_upload_file.assert_called_once()
    mock_process_document.assert_called_once()
    mock_get_llm_config.assert_called_once()

    # Assert the document processing log was created
    mock_create_log.assert_called_once()
