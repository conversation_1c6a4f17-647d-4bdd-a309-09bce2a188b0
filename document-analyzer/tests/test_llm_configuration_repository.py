"""
Tests for the LLM Configuration Repository.

This module contains unit tests for the LLM configuration repository functions.
"""

import pytest
from unittest.mock import patch, MagicMock
from sqlalchemy.orm import Session

from app.repositories.llm_configuration_repository import (
    get_latest_llm_configuration,
    get_latest_llm_configuration_by_document_type,
    get_all_document_types
)
from app.models.database_models import LLMConfiguration


def test_get_latest_llm_configuration():
    """Test getting the latest LLM configuration by ID."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock query builder
    query_mock = MagicMock()
    db.query.return_value = query_mock
    order_mock = MagicMock()
    query_mock.order_by.return_value = order_mock

    # Mock configuration
    config = MagicMock(spec=LLMConfiguration)
    order_mock.first.return_value = config

    # Call the function
    result = get_latest_llm_configuration(db)

    # Assert the result
    assert result == config

    # Assert the query was built correctly
    db.query.assert_called_once_with(LLMConfiguration)
    query_mock.order_by.assert_called_once()
    order_mock.first.assert_called_once()


def test_get_latest_llm_configuration_none():
    """Test getting the latest LLM configuration when none exists."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock query builder
    query_mock = MagicMock()
    db.query.return_value = query_mock
    order_mock = MagicMock()
    query_mock.order_by.return_value = order_mock

    # Mock no configuration found
    order_mock.first.return_value = None

    # Call the function
    result = get_latest_llm_configuration(db)

    # Assert the result
    assert result is None

    # Assert the query was built correctly
    db.query.assert_called_once_with(LLMConfiguration)
    query_mock.order_by.assert_called_once()
    order_mock.first.assert_called_once()


def test_get_latest_llm_configuration_by_document_type():
    """Test getting the latest LLM configuration by document type."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock query builder
    query_mock = MagicMock()
    db.query.return_value = query_mock
    filter_mock = MagicMock()
    query_mock.filter.return_value = filter_mock
    order_mock = MagicMock()
    filter_mock.order_by.return_value = order_mock

    # Mock configuration
    config = MagicMock(spec=LLMConfiguration)
    order_mock.first.return_value = config

    # Call the function
    result = get_latest_llm_configuration_by_document_type(db, "standard_invoice")

    # Assert the result
    assert result == config

    # Assert the query was built correctly
    db.query.assert_called_once_with(LLMConfiguration)
    query_mock.filter.assert_called_once()
    filter_mock.order_by.assert_called_once()
    order_mock.first.assert_called_once()


def test_get_latest_llm_configuration_by_document_type_none():
    """Test getting the latest LLM configuration by document type when none exists."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock query builder
    query_mock = MagicMock()
    db.query.return_value = query_mock
    filter_mock = MagicMock()
    query_mock.filter.return_value = filter_mock
    order_mock = MagicMock()
    filter_mock.order_by.return_value = order_mock

    # Mock no configuration found
    order_mock.first.return_value = None

    # Call the function
    result = get_latest_llm_configuration_by_document_type(db, "tax_invoice")

    # Assert the result
    assert result is None

    # Assert the query was built correctly
    db.query.assert_called_once_with(LLMConfiguration)
    query_mock.filter.assert_called_once()
    filter_mock.order_by.assert_called_once()
    order_mock.first.assert_called_once()


def test_get_all_document_types():
    """Test getting all unique document types."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock query builder
    query_mock = MagicMock()
    db.query.return_value = query_mock
    distinct_mock = MagicMock()
    query_mock.distinct.return_value = distinct_mock

    # Mock result with document types
    document_types = [("standard_invoice",), ("tax_invoice",)]
    distinct_mock.all.return_value = document_types

    # Call the function
    result = get_all_document_types(db)

    # Assert the result
    assert result == ["standard_invoice", "tax_invoice"]

    # Assert the query was built correctly
    db.query.assert_called_once()
    query_mock.distinct.assert_called_once()
    distinct_mock.all.assert_called_once()


def test_get_all_document_types_empty():
    """Test getting all unique document types when none exist."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock query builder
    query_mock = MagicMock()
    db.query.return_value = query_mock
    distinct_mock = MagicMock()
    query_mock.distinct.return_value = distinct_mock

    # Mock empty result
    distinct_mock.all.return_value = []

    # Call the function
    result = get_all_document_types(db)

    # Assert the result
    assert result == []

    # Assert the query was built correctly
    db.query.assert_called_once()
    query_mock.distinct.assert_called_once()
    distinct_mock.all.assert_called_once()
