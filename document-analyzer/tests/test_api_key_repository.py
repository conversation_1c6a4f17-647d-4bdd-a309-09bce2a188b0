"""
Tests for the API Key Repository.

This module contains unit tests for the API key repository.
"""

import pytest
from unittest.mock import MagicMock, patch
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_
import uuid
from datetime import datetime

from app.repositories.api_key_repository import (
    generate_api_key,
    hash_api_key,
    create_api_key,
    get_api_key,
    get_api_key_by_key,
    get_all_api_keys,
    update_api_key,
    delete_api_key
)
from app.models.database_models import APIKey


def test_generate_api_key():
    """Test generating an API key."""
    key = generate_api_key()

    # Check that the key is a string
    assert isinstance(key, str)

    # Check that the key is 32 characters long
    assert len(key) == 32

    # Check that the key only contains alphanumeric characters
    assert key.isalnum()


def test_hash_api_key():
    """Test hashing an API key."""
    key = "test_key"
    hashed_key = hash_api_key(key)

    # Check that the hashed key is a string
    assert isinstance(hashed_key, str)

    # Check that the hashed key is 64 characters long (SHA-256 hash)
    assert len(hashed_key) == 64

    # Check that the same key always hashes to the same value
    assert hash_api_key(key) == hashed_key

    # Check that different keys hash to different values
    assert hash_api_key("different_key") != hashed_key


def test_create_api_key():
    """Test creating an API key."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create an API key
    db_api_key, key_value = create_api_key(
        db=db,
        name="Test API Key",
        description="Test API key for unit tests",
        is_active=True
    )

    # Assert the API key was created with the correct values
    assert db_api_key.name == "Test API Key"
    assert db_api_key.description == "Test API key for unit tests"
    assert db_api_key.is_active is True

    # Assert the key value is a string
    assert isinstance(key_value, str)

    # Assert the key value is 32 characters long
    assert len(key_value) == 32

    # Assert the database operations were called
    db.add.assert_called_once_with(db_api_key)
    db.commit.assert_called_once()
    db.refresh.assert_called_once_with(db_api_key)


def test_get_api_key():
    """Test getting an API key by ID."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create a UUID for the API key ID
    api_key_id = uuid.uuid4()

    # Mock the query result
    mock_api_key = MagicMock(spec=APIKey)
    db.query.return_value.filter.return_value.first.return_value = mock_api_key

    # Get the API key
    result = get_api_key(db, api_key_id)

    # Assert the result is the mock API key
    assert result == mock_api_key

    # Assert the query was called with the correct filter
    db.query.assert_called_once_with(APIKey)
    db.query.return_value.filter.assert_called_once()


def test_get_api_key_by_key():
    """Test getting an API key by key value."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock the query result
    mock_api_key = MagicMock(spec=APIKey)
    db.query.return_value.filter.return_value.first.return_value = mock_api_key

    # Get the API key
    result = get_api_key_by_key(db, "test_key")

    # Assert the result is the mock API key
    assert result == mock_api_key

    # Assert the query was called with the correct filter
    db.query.assert_called_once_with(APIKey)
    db.query.return_value.filter.assert_called_once()


def test_get_all_api_keys():
    """Test getting all API keys."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock the query result
    mock_api_keys = [MagicMock(spec=APIKey) for _ in range(3)]
    db.query.return_value.filter.return_value.count.return_value = len(mock_api_keys)
    db.query.return_value.filter.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = mock_api_keys

    # Get all API keys
    items, total = get_all_api_keys(
        db=db,
        skip=0,
        limit=100,
        include_inactive=False
    )

    # Assert the results are correct
    assert items == mock_api_keys
    assert total == len(mock_api_keys)

    # Assert the query was called with the correct filter
    db.query.assert_called_with(APIKey)


def test_update_api_key():
    """Test updating an API key."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create a UUID for the API key ID
    api_key_id = uuid.uuid4()

    # Mock the query result
    mock_api_key = MagicMock(spec=APIKey)
    db.query.return_value.filter.return_value.first.return_value = mock_api_key

    # Update the API key
    result = update_api_key(
        db=db,
        api_key_id=api_key_id,
        name="Updated API Key",
        description="Updated description",
        is_active=False
    )

    # Assert the result is the mock API key
    assert result == mock_api_key

    # Assert the API key was updated with the correct values
    assert mock_api_key.name == "Updated API Key"
    assert mock_api_key.description == "Updated description"
    assert mock_api_key.is_active is False

    # Assert the database operations were called
    db.commit.assert_called_once()
    db.refresh.assert_called_once_with(mock_api_key)


def test_delete_api_key():
    """Test deleting an API key."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create a UUID for the API key ID
    api_key_id = uuid.uuid4()

    # Mock the query result
    mock_api_key = MagicMock(spec=APIKey)
    db.query.return_value.filter.return_value.first.return_value = mock_api_key

    # Delete the API key
    result = delete_api_key(db, api_key_id)

    # Assert the result is True
    assert result is True

    # Assert the database operations were called
    db.delete.assert_called_once_with(mock_api_key)
    db.commit.assert_called_once()
