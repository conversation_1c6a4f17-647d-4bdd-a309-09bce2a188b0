"""
Tests for the error sanitizer utility.

This module contains unit tests for the error sanitizer utility.
"""

import pytest
from app.utils.error_sanitizer import sanitize_error_message, sanitize_error_response


def test_sanitize_error_message_with_vertex_ai():
    """Test sanitizing error messages containing Vertex AI references."""
    # Original error message with Vertex AI references
    original_error = "500: Error processing document: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'Cannot fetch content from the provided URL. Please ensure the URL is valid and accessible by Vertex AI. Vertex AI respects robots.txt rules, so confirm the URL is allowed to be crawled. Status: URL_ERROR-ERROR_OTHER', 'status': 'INVALID_ARGUMENT'}}"

    # Sanitize the error message
    sanitized = sanitize_error_message(original_error)

    # Assert the sanitized error message
    assert "Vertex AI" not in sanitized
    assert "URL_ERROR-ERROR_OTHER" not in sanitized
    assert "INVALID_ARGUMENT" not in sanitized

    # Check that the sanitized message contains the expected content
    assert "Error processing document" in sanitized
    assert "Cannot fetch content from the provided URL" in sanitized
    assert "Please ensure the URL is valid" in sanitized


def test_sanitize_error_message_with_google_ai():
    """Test sanitizing error messages containing Google AI references."""
    # Original error message with Google AI references
    original_error = "Error processing document with Google Generative AI: Google AI API returned an error: 403 PERMISSION_DENIED"

    # Sanitize the error message
    sanitized = sanitize_error_message(original_error)

    # Assert the sanitized error message
    assert "Google Generative AI" not in sanitized
    assert "Google AI" not in sanitized
    assert "403 PERMISSION_DENIED" not in sanitized
    assert "Permission denied" in sanitized


def test_sanitize_error_message_with_google_cloud():
    """Test sanitizing error messages containing Google Cloud references."""
    # Original error message with Google Cloud references
    original_error = "Error uploading file to Google Cloud Storage: Access denied to bucket"

    # Sanitize the error message
    sanitized = sanitize_error_message(original_error)

    # Assert the sanitized error message
    assert "Google Cloud" not in sanitized
    assert "the cloud service" in sanitized


def test_sanitize_error_message_with_no_sensitive_info():
    """Test sanitizing error messages with no sensitive information."""
    # Original error message with no sensitive information
    original_error = "Error processing document: Invalid file format"

    # Sanitize the error message
    sanitized = sanitize_error_message(original_error)

    # Assert the sanitized error message is unchanged
    assert sanitized == original_error


def test_sanitize_error_message_with_empty_string():
    """Test sanitizing an empty error message."""
    # Original error message is an empty string
    original_error = ""

    # Sanitize the error message
    sanitized = sanitize_error_message(original_error)

    # Assert the sanitized error message is still an empty string
    assert sanitized == original_error


def test_sanitize_error_message_with_none():
    """Test sanitizing a None error message."""
    # Original error message is None
    original_error = None

    # Sanitize the error message
    sanitized = sanitize_error_message(original_error)

    # Assert the sanitized error message is still None
    assert sanitized == original_error


def test_sanitize_error_response_with_dict():
    """Test sanitizing an error response dictionary."""
    # Original error response dictionary
    original_response = {
        "detail": "Error processing document: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'Cannot fetch content from the provided URL. Please ensure the URL is valid and accessible by Vertex AI.', 'status': 'INVALID_ARGUMENT'}}",
        "status_code": 500,
        "headers": {"content-type": "application/json"}
    }

    # Sanitize the error response
    sanitized = sanitize_error_response(original_response)

    # Assert the sanitized error response
    assert "Vertex AI" not in sanitized["detail"]
    assert "INVALID_ARGUMENT" not in sanitized["detail"]
    assert sanitized["status_code"] == 500
    assert sanitized["headers"] == {"content-type": "application/json"}


def test_sanitize_error_response_with_nested_dict():
    """Test sanitizing an error response with nested dictionaries."""
    # Original error response with nested dictionaries
    original_response = {
        "error": {
            "message": "Error processing document with Google Generative AI",
            "details": [
                {"type": "Google AI error", "reason": "API key not found"},
                {"type": "Vertex AI error", "reason": "Model not found"}
            ]
        },
        "status_code": 500
    }

    # Sanitize the error response
    sanitized = sanitize_error_response(original_response)

    # Assert the sanitized error response
    assert "Google Generative AI" not in sanitized["error"]["message"]
    assert "Google AI" not in sanitized["error"]["details"][0]["type"]
    assert "Vertex AI" not in sanitized["error"]["details"][1]["type"]
    assert sanitized["status_code"] == 500


def test_sanitize_error_response_with_string():
    """Test sanitizing an error response string."""
    # Original error response string
    original_response = "Error processing document with Google Generative AI: API key not found"

    # Sanitize the error response
    sanitized = sanitize_error_response(original_response)

    # Assert the sanitized error response
    assert "Google Generative AI" not in sanitized
    assert "API key not found" in sanitized
