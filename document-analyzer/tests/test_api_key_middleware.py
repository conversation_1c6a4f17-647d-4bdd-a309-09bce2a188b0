"""
Tests for the API Key Middleware.

This module contains unit tests for the API key middleware.
"""

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, patch, AsyncMock
from fastapi import Fast<PERSON><PERSON>, Request, Response, HTTPException
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
import uuid

from app.middleware.api_key_auth import APIKeyMiddleware, get_api_key_from_header
from app.models.database_models import APIKey
from app.repositories.api_key_repository import get_api_key_by_key


@pytest.fixture
def mock_app():
    """Create a mock FastAPI application."""
    app = FastAPI()

    @app.get("/test")
    async def test_endpoint():
        return {"message": "Test endpoint"}

    @app.get("/excluded")
    async def excluded_endpoint():
        return {"message": "Excluded endpoint"}

    return app


@pytest.fixture
def mock_db():
    """Create a mock database session."""
    return MagicMock(spec=Session)


@pytest.fixture
def mock_api_key():
    """Create a mock API key."""
    api_key = MagicMock(spec=APIKey)
    api_key.id = uuid.uuid4()
    api_key.is_active = True
    return api_key


@pytest.mark.asyncio
@patch("app.middleware.api_key_auth.get_db")
async def test_get_api_key_from_header_valid(mock_get_db, mock_db, mock_api_key):
    """Test getting a valid API key from the header."""
    # Mock the database session
    mock_get_db.return_value.__next__.return_value = mock_db

    # Mock the get_api_key_by_key function
    with patch("app.middleware.api_key_auth.get_api_key_by_key", return_value=mock_api_key):
        # Call the function
        result = await get_api_key_from_header(api_key="test_key", db=mock_db)

        # Assert the result is the mock API key
        assert result == mock_api_key


@pytest.mark.asyncio
@patch("app.middleware.api_key_auth.get_db")
async def test_get_api_key_from_header_missing(mock_get_db, mock_db):
    """Test getting a missing API key from the header."""
    # Mock the database session
    mock_get_db.return_value.__next__.return_value = mock_db

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        await get_api_key_from_header(api_key=None, db=mock_db)

    # Assert the exception details
    assert excinfo.value.status_code == 401
    assert "API key missing" in excinfo.value.detail


@pytest.mark.asyncio
@patch("app.middleware.api_key_auth.get_db")
async def test_get_api_key_from_header_invalid(mock_get_db, mock_db):
    """Test getting an invalid API key from the header."""
    # Mock the database session
    mock_get_db.return_value.__next__.return_value = mock_db

    # Mock the get_api_key_by_key function to return None
    with patch("app.middleware.api_key_auth.get_api_key_by_key", return_value=None):
        # Call the function and expect an HTTPException
        with pytest.raises(HTTPException) as excinfo:
            await get_api_key_from_header(api_key="invalid_key", db=mock_db)

        # Assert the exception details
        assert excinfo.value.status_code == 401
        assert "Invalid API key" in excinfo.value.detail


@pytest.mark.asyncio
@patch("app.middleware.api_key_auth.get_db")
async def test_get_api_key_from_header_inactive(mock_get_db, mock_db):
    """Test getting an inactive API key from the header."""
    # Mock the database session
    mock_get_db.return_value.__next__.return_value = mock_db

    # Create an inactive API key
    inactive_api_key = MagicMock(spec=APIKey)
    inactive_api_key.is_active = False

    # Mock the get_api_key_by_key function to return the inactive API key
    with patch("app.middleware.api_key_auth.get_api_key_by_key", return_value=inactive_api_key):
        # Call the function and expect an HTTPException
        with pytest.raises(HTTPException) as excinfo:
            await get_api_key_from_header(api_key="inactive_key", db=mock_db)

        # Assert the exception details
        assert excinfo.value.status_code == 401
        assert "API key is inactive" in excinfo.value.detail


@patch("app.middleware.api_key_auth.get_db")
def test_api_key_middleware_excluded_path(mock_get_db, mock_app, mock_db):
    """Test the API key middleware with an excluded path."""
    # Add the middleware to the app
    app = mock_app
    app.add_middleware(
        APIKeyMiddleware,
        exclude_paths=["/excluded"],
        exclude_methods=["OPTIONS"]
    )

    # Create a test client
    client = TestClient(app)

    # Mock the database session
    mock_get_db.return_value.__next__.return_value = mock_db

    # Make a request to the excluded endpoint
    response = client.get("/excluded")

    # Assert the response is successful
    assert response.status_code == 200
    assert response.json() == {"message": "Excluded endpoint"}


@patch("app.middleware.api_key_auth.get_db")
@patch("app.middleware.api_key_auth.get_api_key_by_key")
def test_api_key_middleware_valid_key(
    mock_get_api_key_by_key, mock_get_db, mock_app, mock_db, mock_api_key
):
    """Test the API key middleware with a valid API key."""
    # Add the middleware to the app
    app = mock_app
    app.add_middleware(
        APIKeyMiddleware,
        exclude_paths=["/excluded"],
        exclude_methods=["OPTIONS"]
    )

    # Create a test client
    client = TestClient(app)

    # Mock the database session
    mock_get_db.return_value.__next__.return_value = mock_db

    # Mock the get_api_key_by_key function to return the mock API key
    mock_get_api_key_by_key.return_value = mock_api_key

    # Make a request to the test endpoint with a valid API key
    response = client.get("/test", headers={"X-API-Key": "valid_key"})

    # Assert the response is successful
    assert response.status_code == 200
    assert response.json() == {"message": "Test endpoint"}


@patch("app.middleware.api_key_auth.get_db")
@patch("app.middleware.api_key_auth.get_api_key_by_key")
def test_api_key_middleware_missing_key(
    mock_get_api_key_by_key, mock_get_db, mock_app, mock_db
):
    """Test the API key middleware with a missing API key."""
    # Add the middleware to the app
    app = mock_app
    app.add_middleware(
        APIKeyMiddleware,
        exclude_paths=["/excluded"],
        exclude_methods=["OPTIONS"]
    )

    # Create a test client
    client = TestClient(app)

    # Mock the database session
    mock_get_db.return_value.__next__.return_value = mock_db

    # Make a request to the test endpoint without an API key
    response = client.get("/test")

    # Assert the response is unauthorized
    assert response.status_code == 401
    assert "Unauthorized: Invalid or missing API key" in response.text
