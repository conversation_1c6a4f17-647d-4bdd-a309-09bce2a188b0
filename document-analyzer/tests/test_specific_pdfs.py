"""
Tests for the PDF Color Analysis Service with specific PDF files.

This module tests the PDF color analysis service with real PDF files
to verify that it correctly identifies colored pages.
"""

import os
import pytest

pytestmark = pytest.mark.skip(reason="Skipping all tests in test_specific_pdfs.py as requested.")

from app.services.pdf_color_analysis_service import analyze_pdf_file

# Define the fixtures directory
FIXTURES_DIR = os.path.join('tests', 'fixtures')

# Define test cases
TEST_CASES = [
    {
        'filename': 'pg_1_3.pdf',
        'expected_color_pages': [1, 3],
        'total_pages': 3,
        'description': '3 pages with color on pages 1 and 3'
    },
    {
        'filename': 'bw.pdf',
        'expected_color_pages': [],
        'total_pages': 1,
        'description': 'Black and white PDF with no color pages'
    },
    {
        'filename': 'binapertiwi.pdf',
        'expected_color_pages': [1, 2, 3, 5],
        'total_pages': 5,
        'description': '5 pages with color on pages 1, 2, 3, and 5'
    }
]


@pytest.mark.parametrize("test_case", TEST_CASES, ids=[case['filename'] for case in TEST_CASES])
def test_pdf_color_analysis(test_case):
    """Test the PDF color analysis service with specific PDF files."""
    # Get the full path to the PDF file
    pdf_path = os.path.join(FIXTURES_DIR, test_case['filename'])

    # Skip the test if the file doesn't exist
    if not os.path.exists(pdf_path):
        pytest.skip(f"PDF file not found: {pdf_path}")

    # Analyze the PDF
    result = analyze_pdf_file(pdf_path)

    # Check the results
    assert result['total_pages'] == test_case['total_pages'], \
        f"Expected {test_case['total_pages']} pages, but found {result['total_pages']}"

    expected_has_color = len(test_case['expected_color_pages']) > 0
    assert result['has_color'] == expected_has_color, \
        f"Expected has_color={expected_has_color}, but got {result['has_color']}"

    assert set(result['color_pages']) == set(test_case['expected_color_pages']), \
        f"Expected color pages {test_case['expected_color_pages']}, but found {result['color_pages']}"

    assert result['color_page_count'] == len(test_case['expected_color_pages']), \
        f"Expected {len(test_case['expected_color_pages'])} color pages, but found {result['color_page_count']}"
