"""
Test utilities for the document analyzer API.

This module contains utility functions for testing the document analyzer API.
"""

import uuid
from unittest.mock import patch, MagicMock
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from typing import Tuple, List

from app.models.database_models import APIKey


def configure_test_client(app, mock_db=None):
    """
    Configure a test client with API key authentication.

    Args:
        app: The FastAPI application
        mock_db: Optional mock database session

    Returns:
        tuple: (client, mock_db)
    """
    # Create a fresh mock DB session if not provided
    if mock_db is None:
        mock_db = MagicMock(spec=Session)

    # Create a test client
    client = TestClient(app)

    # Mock the API key validation
    mock_api_key = MagicMock(spec=APIKey)
    mock_api_key.id = uuid.uuid4()
    mock_api_key.is_active = True

    # Add default headers with API key to the client
    client.headers["X-API-Key"] = "test-api-key"

    # Patch the get_api_key_by_key function to return our mock API key
    # Also patch create_api_key_usage to prevent database errors
    patcher1 = patch('app.middleware.api_key_auth.get_api_key_by_key', return_value=mock_api_key)
    patcher2 = patch('app.middleware.api_key_auth.get_api_key_by_key')  # Remove create_api_key_usage patch

    patcher1.start()
    patcher2.start()

    return client, mock_db, [patcher1, patcher2]
