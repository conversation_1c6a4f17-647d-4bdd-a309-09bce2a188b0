"""
Tests for the API Key Endpoints.

This module contains unit tests for the API key endpoints.
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi import HTT<PERSON>Exception
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
import uuid

from fastapi import <PERSON><PERSON><PERSON>
from app.database import get_db
from app.models.database_models import APIKey, APIKeyUsage
from app.repositories import api_key_repository
from app.middleware.api_key_auth import get_api_key_from_header
from app.routers import api_keys


# Create a test app without middleware
test_app = FastAPI()
test_app.include_router(api_keys.router)


# Override the database dependency
def override_get_db():
    """Override the database dependency for testing."""
    return MagicMock(spec=Session)


# Override the API key dependency
async def override_get_api_key_from_header():
    """Override the API key dependency for testing."""
    mock_api_key = MagicMock(spec=APIKey)
    mock_api_key.id = uuid.uuid4()
    mock_api_key.name = "Test API Key"
    mock_api_key.description = "Test API key for unit tests"
    mock_api_key.is_active = True
    return mock_api_key


# Apply the overrides
test_app.dependency_overrides[get_db] = override_get_db
test_app.dependency_overrides[get_api_key_from_header] = override_get_api_key_from_header

# Create a test client
client = TestClient(test_app)


@patch("app.repositories.api_key_repository.create_api_key")
def test_create_api_key(mock_create_api_key):
    """Test creating an API key."""
    # Mock the create_api_key function
    mock_api_key = MagicMock(spec=APIKey)
    mock_api_key.id = uuid.uuid4()
    mock_api_key.name = "New API Key"
    mock_api_key.description = "New API key for testing"
    mock_api_key.is_active = True
    mock_api_key.__dict__ = {
        "id": mock_api_key.id,
        "name": mock_api_key.name,
        "description": mock_api_key.description,
        "is_active": mock_api_key.is_active,
        "created_at": "2023-06-01T12:00:00Z",
        "last_used_at": None
    }

    mock_create_api_key.return_value = (mock_api_key, "test_key_value")

    # Make a request to create an API key
    response = client.post(
        "/api-keys",
        json={
            "name": "New API Key",
            "description": "New API key for testing",
            "is_active": True
        }
    )

    # Assert the response is successful
    assert response.status_code == 200

    # Assert the response contains the API key with the key value
    data = response.json()
    assert data["name"] == "New API Key"
    assert data["description"] == "New API key for testing"
    assert data["is_active"] is True
    assert "key_value" in data
    assert data["key_value"] == "test_key_value"

    # Assert the create_api_key function was called with the correct parameters
    mock_create_api_key.assert_called_once()
    assert mock_create_api_key.call_args[1]["name"] == "New API Key"
    assert mock_create_api_key.call_args[1]["description"] == "New API key for testing"
    assert mock_create_api_key.call_args[1]["is_active"] is True


@patch("app.repositories.api_key_repository.get_all_api_keys")
def test_get_api_keys(mock_get_all_api_keys):
    """Test getting all API keys."""
    # Mock the get_all_api_keys function
    mock_api_keys = [MagicMock(spec=APIKey) for _ in range(3)]
    for i, mock_api_key in enumerate(mock_api_keys):
        mock_api_key.id = uuid.uuid4()
        mock_api_key.name = f"API Key {i+1}"
        mock_api_key.description = f"API key {i+1} for testing"
        mock_api_key.is_active = True
        mock_api_key.created_at = "2023-06-01T12:00:00Z"
        mock_api_key.last_used_at = None

    mock_get_all_api_keys.return_value = (mock_api_keys, len(mock_api_keys))

    # Make a request to get all API keys
    response = client.get("/api-keys")

    # Assert the response is successful
    assert response.status_code == 200

    # Assert the response contains the API keys
    data = response.json()
    assert data["total"] == 3
    assert len(data["items"]) == 3
    assert data["page"] == 1
    assert data["page_size"] == 100
    assert data["pages"] == 1

    # Assert the get_all_api_keys function was called with the correct parameters
    mock_get_all_api_keys.assert_called_once()
    assert mock_get_all_api_keys.call_args[1]["skip"] == 0
    assert mock_get_all_api_keys.call_args[1]["limit"] == 100
    assert mock_get_all_api_keys.call_args[1]["include_inactive"] is False


@patch("app.repositories.api_key_repository.get_api_key")
def test_get_api_key(mock_get_api_key):
    """Test getting an API key by ID."""
    # Create a UUID for the API key ID
    api_key_id = uuid.uuid4()

    # Mock the get_api_key function
    mock_api_key = MagicMock(spec=APIKey)
    mock_api_key.id = api_key_id
    mock_api_key.name = "Test API Key"
    mock_api_key.description = "Test API key for unit tests"
    mock_api_key.is_active = True
    mock_api_key.created_at = "2023-06-01T12:00:00Z"
    mock_api_key.last_used_at = None

    mock_get_api_key.return_value = mock_api_key

    # Make a request to get the API key
    response = client.get(f"/api-keys/{api_key_id}")

    # Assert the response is successful
    assert response.status_code == 200

    # Assert the response contains the API key
    data = response.json()
    assert data["id"] == str(api_key_id)
    assert data["name"] == "Test API Key"
    assert data["description"] == "Test API key for unit tests"
    assert data["is_active"] is True

    # Assert the get_api_key function was called
    mock_get_api_key.assert_called_once()
    # Check that the second argument is the API key ID
    assert mock_get_api_key.call_args[0][1] == api_key_id


@patch("app.repositories.api_key_repository.get_api_key")
def test_get_api_key_not_found(mock_get_api_key):
    """Test getting a non-existent API key."""
    # Create a UUID for the API key ID
    api_key_id = uuid.uuid4()

    # Mock the get_api_key function to return None
    mock_get_api_key.return_value = None

    # Make a request to get the API key
    response = client.get(f"/api-keys/{api_key_id}")

    # Assert the response is not found
    assert response.status_code == 404
    assert f"API key with ID {api_key_id} not found" in response.text

    # Assert the get_api_key function was called
    mock_get_api_key.assert_called_once()
    # Check that the second argument is the API key ID
    assert mock_get_api_key.call_args[0][1] == api_key_id


@patch("app.repositories.api_key_repository.update_api_key")
def test_update_api_key(mock_update_api_key):
    """Test updating an API key."""
    # Create a UUID for the API key ID
    api_key_id = uuid.uuid4()

    # Mock the update_api_key function
    mock_api_key = MagicMock(spec=APIKey)
    mock_api_key.id = api_key_id
    mock_api_key.name = "Updated API Key"
    mock_api_key.description = "Updated API key for testing"
    mock_api_key.is_active = False
    mock_api_key.created_at = "2023-06-01T12:00:00Z"
    mock_api_key.last_used_at = None

    mock_update_api_key.return_value = mock_api_key

    # Make a request to update the API key
    response = client.put(
        f"/api-keys/{api_key_id}",
        json={
            "name": "Updated API Key",
            "description": "Updated API key for testing",
            "is_active": False
        }
    )

    # Assert the response is successful
    assert response.status_code == 200

    # Assert the response contains the updated API key
    data = response.json()
    assert data["id"] == str(api_key_id)
    assert data["name"] == "Updated API Key"
    assert data["description"] == "Updated API key for testing"
    assert data["is_active"] is False

    # Assert the update_api_key function was called with the correct parameters
    mock_update_api_key.assert_called_once()
    assert mock_update_api_key.call_args[1]["api_key_id"] == api_key_id
    assert mock_update_api_key.call_args[1]["name"] == "Updated API Key"
    assert mock_update_api_key.call_args[1]["description"] == "Updated API key for testing"
    assert mock_update_api_key.call_args[1]["is_active"] is False


@patch("app.repositories.api_key_repository.update_api_key")
def test_update_api_key_not_found(mock_update_api_key):
    """Test updating a non-existent API key."""
    # Create a UUID for the API key ID
    api_key_id = uuid.uuid4()

    # Mock the update_api_key function to return None
    mock_update_api_key.return_value = None

    # Make a request to update the API key
    response = client.put(
        f"/api-keys/{api_key_id}",
        json={
            "name": "Updated API Key",
            "description": "Updated API key for testing",
            "is_active": False
        }
    )

    # Assert the response is not found
    assert response.status_code == 404
    assert f"API key with ID {api_key_id} not found" in response.text

    # Assert the update_api_key function was called with the correct parameters
    mock_update_api_key.assert_called_once()
    assert mock_update_api_key.call_args[1]["api_key_id"] == api_key_id


@patch("app.repositories.api_key_repository.delete_api_key")
def test_delete_api_key(mock_delete_api_key):
    """Test deleting an API key."""
    # Create a UUID for the API key ID
    api_key_id = uuid.uuid4()

    # Mock the delete_api_key function to return True
    mock_delete_api_key.return_value = True

    # Make a request to delete the API key
    response = client.delete(f"/api-keys/{api_key_id}")

    # Assert the response is successful
    assert response.status_code == 200
    assert response.json() == {"message": "API key deleted successfully"}

    # Assert the delete_api_key function was called
    mock_delete_api_key.assert_called_once()
    # Check that the second argument is the API key ID
    assert mock_delete_api_key.call_args[0][1] == api_key_id


@patch("app.repositories.api_key_repository.delete_api_key")
def test_delete_api_key_not_found(mock_delete_api_key):
    """Test deleting a non-existent API key."""
    # Create a UUID for the API key ID
    api_key_id = uuid.uuid4()

    # Mock the delete_api_key function to return False
    mock_delete_api_key.return_value = False

    # Make a request to delete the API key
    response = client.delete(f"/api-keys/{api_key_id}")

    # Assert the response is not found
    assert response.status_code == 404
    assert f"API key with ID {api_key_id} not found" in response.text

    # Assert the delete_api_key function was called
    mock_delete_api_key.assert_called_once()
    # Check that the second argument is the API key ID
    assert mock_delete_api_key.call_args[0][1] == api_key_id


@patch("app.repositories.api_key_repository.get_api_key")
@patch("app.repositories.api_key_repository.get_api_key_usage")
def test_get_api_key_usage(mock_get_api_key_usage, mock_get_api_key):
    """Test getting usage records for an API key."""
    # Create a UUID for the API key ID
    api_key_id = uuid.uuid4()

    # Mock the get_api_key function
    mock_api_key = MagicMock(spec=APIKey)
    mock_api_key.id = api_key_id
    mock_get_api_key.return_value = mock_api_key

    # Mock the get_api_key_usage function
    mock_usage_records = [MagicMock(spec=APIKeyUsage) for _ in range(3)]
    for i, mock_usage in enumerate(mock_usage_records):
        mock_usage.id = i + 1
        mock_usage.api_key_id = api_key_id
        mock_usage.endpoint = f"/test{i+1}"
        mock_usage.status_code = 200
        mock_usage.processing_time = 0.1
        mock_usage.timestamp = "2023-06-01T12:00:00Z"

    mock_get_api_key_usage.return_value = (mock_usage_records, len(mock_usage_records))

    # Make a request to get the API key usage
    response = client.get(f"/api-keys/{api_key_id}/usage")

    # Assert the response is successful
    assert response.status_code == 200

    # Assert the response contains the usage records
    data = response.json()
    assert data["total"] == 3
    assert len(data["items"]) == 3
    assert data["page"] == 1
    assert data["page_size"] == 100
    assert data["pages"] == 1

    # Assert the get_api_key and get_api_key_usage functions were called
    mock_get_api_key.assert_called_once()
    # Check that the second argument is the API key ID
    assert mock_get_api_key.call_args[0][1] == api_key_id

    mock_get_api_key_usage.assert_called_once()
    assert mock_get_api_key_usage.call_args[1]["api_key_id"] == api_key_id
    assert mock_get_api_key_usage.call_args[1]["skip"] == 0
    assert mock_get_api_key_usage.call_args[1]["limit"] == 100


@patch("app.repositories.api_key_repository.get_api_key")
@patch("app.repositories.api_key_repository.get_api_key_usage_stats")
def test_get_api_key_stats(mock_get_api_key_usage_stats, mock_get_api_key):
    """Test getting usage statistics for an API key."""
    # Create a UUID for the API key ID
    api_key_id = uuid.uuid4()

    # Mock the get_api_key function
    mock_api_key = MagicMock(spec=APIKey)
    mock_api_key.id = api_key_id
    mock_get_api_key.return_value = mock_api_key

    # Mock the get_api_key_usage_stats function
    mock_stats = {
        "total_requests": 10,
        "successful_requests": 8,
        "failed_requests": 2,
        "average_processing_time": 0.15,
        "endpoints": {"/test1": 5, "/test2": 3, "/test3": 2}
    }

    mock_get_api_key_usage_stats.return_value = mock_stats

    # Make a request to get the API key stats
    response = client.get(f"/api-keys/{api_key_id}/stats")

    # Assert the response is successful
    assert response.status_code == 200

    # Assert the response contains the stats
    data = response.json()
    assert data["total_requests"] == 10
    assert data["successful_requests"] == 8
    assert data["failed_requests"] == 2
    assert data["average_processing_time"] == 0.15
    assert data["endpoints"] == {"/test1": 5, "/test2": 3, "/test3": 2}

    # Assert the get_api_key and get_api_key_usage_stats functions were called
    mock_get_api_key.assert_called_once()
    # Check that the second argument is the API key ID
    assert mock_get_api_key.call_args[0][1] == api_key_id

    mock_get_api_key_usage_stats.assert_called_once()
    # Check that the second argument is the API key ID
    assert mock_get_api_key_usage_stats.call_args[0][1] == api_key_id
