# Document Analyzer

A FastAPI application for analyzing documents.

## Setup

1. Clone the repository
2. Create a virtual environment:
   ```
   python -m venv venv
   ```
3. Activate the virtual environment:
   - On Windows:
     ```
     venv\Scripts\activate
     ```
   - On macOS/Linux:
     ```
     source venv/bin/activate
     ```
4. Install dependencies:
   ```
   pip install -r requirements.txt
   pip install "fastapi[standard]"
   ```
5. Configure environment variables:
   - Copy the example environment file:
     ```
     cp .env.example .env
     ```
   - Edit the `.env` file with your database settings

## Environment Configuration

The application supports three environments:
- `development` (default)
- `staging`
- `production`

You can set the environment using the `APP_ENVIRONMENT` variable in your `.env` file or as an environment variable.

### Database Configuration

Database connection settings can be configured using the following environment variables:
- `DB_USERNAME`: Database username
- `DB_PASSWORD`: Database password
- `DB_HOST`: Database host
- `DB_PORT`: Database port
- `DB_DATABASE`: Database name

### Google Generative AI Configuration

For Google Generative AI integration, you need to set one of the following:

For standard Google AI API:
- `GOOGLE_API_KEY`: Your Google AI API key (get one from https://ai.google.dev/)

For Google Vertex AI:
- `GOOGLE_GENAI_USE_VERTEXAI`: Set to "True" to use Vertex AI
- `GOOGLE_CLOUD_PROJECT`: Your Google Cloud project ID
- `GOOGLE_CLOUD_LOCATION`: Google Cloud region (default: us-central1)

### Google Cloud Storage Configuration

For Google Cloud Storage integration:
- `GOOGLE_CLOUD_STORAGE_BUCKET`: Your Google Cloud Storage bucket name for storing documents

## Database Setup

The application uses PostgreSQL with SQLAlchemy for database management:

1. Create a PostgreSQL database:
   ```
   createdb document_analyzer
   ```

2. The application will automatically create all necessary tables when it starts for the first time.
   No additional migration steps are required.

> **Note:** While the project includes Alembic migration files, we've switched to using SQLAlchemy's `Base.metadata.create_all()` for table creation to avoid migration issues. The tables are created automatically when the application starts.

## Running the Application

Start the application with:

```
fastapi dev main.py
```

Or using uvicorn directly:

```
uvicorn main:app --reload
```

The API will be available at http://localhost:8000

## API Documentation

Once the application is running, you can access:
- Interactive API documentation: http://localhost:8000/docs
- Alternative API documentation: http://localhost:8000/redoc

### Using the Swagger UI with API Key Authentication

When using the Swagger UI (http://localhost:8000/docs), you need to authenticate to access protected endpoints:

1. Click the "Authorize" button at the top right of the page
2. Enter your API key in the "ApiKeyAuth (apiKey)" field
3. Click "Authorize" and then "Close"

After authenticating, you'll be able to use all protected endpoints in the Swagger UI.

## API Authentication

The API uses API key authentication. To access protected endpoints, you need to include an API key in the `X-API-Key` header of your requests.

### Creating an Initial API Key

To create an initial API key, run the following script:

```
python scripts/create__api_key.py
```

This will generate an API key and display it in the console. Save this key in a secure location, as it will not be shown again.

### Managing API Keys

Once you have an initial API key, you can use it to manage other API keys through the `/api-keys` endpoints:

- `POST /api-keys`: Create a new API key
- `GET /api-keys`: List all API keys
- `GET /api-keys/{api_key_id}`: Get details of a specific API key
- `PUT /api-keys/{api_key_id}`: Update an API key
- `DELETE /api-keys/{api_key_id}`: Delete an API key
- `GET /api-keys/{api_key_id}/usage`: Get usage records for an API key
- `GET /api-keys/{api_key_id}/stats`: Get usage statistics for an API key

### Using API Keys in Requests

Include the API key in the `X-API-Key` header of your requests:

```
curl -X GET "http://localhost:8000/documents/processing-logs" \
     -H "X-API-Key: your_api_key_here"
```

Or using Python requests:

```python
import requests

headers = {
    "X-API-Key": "your_api_key_here"
}

response = requests.get("http://localhost:8000/documents/processing-logs", headers=headers)
```

## Troubleshooting

### Database Issues

If you encounter database-related errors:

1. **Table does not exist errors**: The application should automatically create all tables on startup. If you see errors about missing tables, try creating a new database and updating your `.env` file to use it.

2. **Migration errors**: If you're trying to use Alembic migrations and encounter issues, we recommend letting SQLAlchemy create the tables automatically instead. The application is configured to create all necessary tables on startup.

## Features

### API Authentication
- API key-based authentication using X-API-Key header
- API key management (create, read, update, delete)
- API key usage tracking and metrics
- Secure API key storage with hashing
- Automatic API key validation middleware

### Document Analysis
- PDF color analysis to identify pages containing color
- Document analysis using Google's Generative AI
- Structured output support for AI responses
- Detailed color analysis with page-by-page breakdown
- Configurable color detection thresholds and sampling rates

### Document Storage and Management
- Google Cloud Storage integration for document storage
- Secure file uploads with original filename preservation
- Signed URL generation for secure document access
- Support for document download with content type specification

### Document Processing Logs
- Comprehensive logging of document processing activities
- Storage of raw LLM responses and extracted structured data
- Color analysis results tracking
- Processing time metrics
- Support for human validation of extracted data

### LLM Configuration Management
- Create, read, update, and delete LLM configurations
- Store model settings including provider, prompt templates, and generation parameters
- Support for Google's Generative AI models
- Integration with Google Vertex AI for enterprise deployments

## Project Structure

```
document-analyzer/
├── alembic/            # Legacy migration scripts (not actively used)
├── app/
│   ├── database.py     # Database connection and session management
│   ├── middleware/     # Middleware components
│   │   └── api_key_auth.py           # API key authentication middleware
│   ├── models/         # Pydantic and database models
│   │   ├── api_key_models.py         # API key-related models
│   │   ├── database_models.py        # SQLAlchemy database models (tables auto-created from these)
│   │   ├── document_models.py        # Document-related models
│   │   ├── document_processing_log_models.py  # Processing log models
│   │   ├── llm_configuration_models.py  # LLM configuration models
│   │   └── validation_models.py      # Validation-related models
│   ├── repositories/   # Database operations
│   │   ├── api_key_repository.py     # API key database operations
│   │   ├── document_processing_log_repository.py  # Document processing log operations
│   │   ├── llm_configuration_repository.py  # LLM configuration operations
│   │   └── validation_repository.py  # Validation operations
│   ├── routers/        # API routes
│   │   ├── api_keys.py               # API key endpoints
│   │   ├── documents.py              # Document processing endpoints
│   │   └── llm_configurations.py     # LLM configuration endpoints
│   ├── services/       # Business logic
│   │   ├── google_cloud_storage_service.py  # GCS integration
│   │   └── google_genai_service.py          # Google AI integration
│   └── utils/          # Utility functions
│       └── env_loader.py              # Environment variable loader
├── scripts/            # Utility scripts
│   └── create_initial_api_key.py     # Script to create an initial API key
├── tests/              # Test cases
│   ├── fixtures/       # Test fixtures including sample PDFs
│   └── test_*.py       # Test modules
├── main.py             # Application entry point (creates database tables on startup)
├── requirements.txt    # Project dependencies
├── .env.example        # Example environment configuration
└── README.md           # This file
```
