from app.utils.env_loader import load_env_file
# Load environment variables from .env file if it exists
load_env_file()

import logging
import uvicorn
import sentry_sdk
from sentry_sdk.integrations.starlette import StarletteIntegration
from sentry_sdk.integrations.fastapi import FastApiIntegration
from fastapi import FastAP<PERSON>, Security, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.models import SecurityScheme
from fastapi.security import APIKeyHeader
from app.routers import documents, llm_configurations, api_keys
from app.database import engine, Base
from app.middleware.api_key_auth import APIKeyMiddleware, X_API_KEY, get_api_key_from_header
from app.config import settings

# Configure logging
logging.basicConfig(
    level=settings.log_level.upper(),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Sentry
sentry_sdk.init(
    dsn=settings.sentry_dsn,  # Get from environment variables
    environment=settings.environment,
    send_default_pii=True,
    # Reduce sampling rates in production
    traces_sample_rate=0.1 if settings.environment == "production" else 1.0,
    profile_session_sample_rate=0.1 if settings.environment == "production" else 1.0,
    profile_lifecycle="trace",
    integrations=[
        StarletteIntegration(
            transaction_style="endpoint",
            failed_request_status_codes={403, *range(500, 599)},
            http_methods_to_capture=("GET", "POST"),
        ),
        FastApiIntegration(
            transaction_style="endpoint",
            failed_request_status_codes={403, *range(500, 599)},
            http_methods_to_capture=("GET", "POST"),
        ),
    ],
)

# Define API key security scheme for OpenAPI (Swagger UI)
app = FastAPI(
    title="Document Analyzer API",
    description="API for analyzing documents",
    version="0.1.0",
    swagger_ui_init_oauth={},
    swagger_ui_parameters={"persistAuthorization": True}
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Add API key middleware (runtime security)
app.add_middleware(
    APIKeyMiddleware,
    exclude_paths=["/", "/docs", "/redoc", "/openapi.json", "/health", "/config"],
    exclude_methods=["OPTIONS"]
)

@app.get("/health", dependencies=[])
async def health_check():
    return {"status": "healthy"}

@app.get("/config", dependencies=[])
async def get_config():
    """Get the current environment configuration (safe values only)."""
    return {
        "environment": settings.environment,
        "database": {
            "host": settings.db.host,
            "port": settings.db.port,
            "database": settings.db.database,
            "username": settings.db.username,
            # Don't include password for security reasons
        }
    }

# Protected routers
app.include_router(documents.router, dependencies=[Depends(get_api_key_from_header)])
app.include_router(llm_configurations.router, dependencies=[Depends(get_api_key_from_header)])
app.include_router(api_keys.router, dependencies=[Depends(get_api_key_from_header)])

if __name__ == "__main__":
    logger.info("Starting Document Analyzer API server")
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
