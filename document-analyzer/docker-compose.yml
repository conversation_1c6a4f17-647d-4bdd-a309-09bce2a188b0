version: '3.8'

services:
  # Default app service without PDF processing capabilities
  app:
    # build:
    #   context: .
    #   dockerfile: Dockerfile
    image: registry-gitlab.happyfresh.net/hf/tpd/rainmakers/document-analyzer:latest
    ports:
      - "8000:8000"
    environment:
      - APP_ENVIRONMENT=production
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres
      # For Colima, use the host machine's IP address from the container's perspective
      - DB_HOST=localhost
      - DB_PORT=5432
      - DB_DATABASE=document_analyzer
      - GUNICORN_WORKERS=4  # Set the number of Gunicorn workers
      - GOOGLE_GENAI_USE_VERTEXAI=True
      - GOOGLE_CLOUD_PROJECT=project-id
      - GOOGLE_CLOUD_LOCATION=us-west1
      # For production, use a mounted volume for credentials instead of a local path
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account.json
      - GOOGLE_CLOUD_STORAGE_BUCKET=bucket-name
    volumes:
      - ./credentials:/app/credentials
      # Mount credentials from host to container
      # In production, replace with the actual path to your credentials file
      - ${GOOGLE_CREDENTIALS_PATH:-./credentials/service-account.json}:/app/credentials/service-account.json:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 5s

#   db:
#     image: postgres:16-alpine
#     volumes:
#       - postgres_data:/var/lib/postgresql/data/
#     environment:
#       - POSTGRES_USER=postgres
#       - POSTGRES_PASSWORD=postgres
#       - POSTGRES_DB=document_analyzer_prod
#     ports:
#       - "5432:5432"
#     restart: unless-stopped
#     healthcheck:
#       test: ["CMD-SHELL", "pg_isready -U postgres"]
#       interval: 10s
#       timeout: 5s
#       retries: 5

# volumes:
#   postgres_data:
