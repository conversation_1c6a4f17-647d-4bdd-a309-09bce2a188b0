hoistPattern:
  - '*'
hoistedDependencies:
  '@prisma/config@6.9.0':
    '@prisma/config': private
  '@prisma/debug@6.9.0':
    '@prisma/debug': private
  '@prisma/engines-version@6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e':
    '@prisma/engines-version': private
  '@prisma/engines@6.9.0':
    '@prisma/engines': private
  '@prisma/fetch-engine@6.9.0':
    '@prisma/fetch-engine': private
  '@prisma/get-platform@6.9.0':
    '@prisma/get-platform': private
  jiti@2.4.2:
    jiti: private
ignoredBuilds:
  - '@prisma/engines'
  - prisma
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.10.0
pendingBuilds: []
prunedAt: Wed, 04 Jun 2025 10:08:26 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
