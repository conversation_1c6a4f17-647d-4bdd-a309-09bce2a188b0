{"name": "@prisma/debug", "version": "6.9.0", "description": "This package is intended for Prisma's internal use", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}}, "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "homepage": "https://www.prisma.io", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/debug"}, "bugs": "https://github.com/prisma/prisma/issues", "devDependencies": {"@types/jest": "29.5.14", "@types/node": "18.19.76", "esbuild": "0.25.1", "jest": "29.7.0", "jest-junit": "16.0.0", "strip-ansi": "6.0.1", "kleur": "4.1.5", "typescript": "5.4.5"}, "files": ["README.md", "dist"], "dependencies": {}, "sideEffects": false, "scripts": {"dev": "DEV=true tsx helpers/build.ts", "build": "tsx helpers/build.ts", "test": "jest"}}