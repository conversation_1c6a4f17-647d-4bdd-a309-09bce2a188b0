using Azure.Storage;
using Azure.Storage.Files.Shares;
using Azure.Storage.Sas;
using Microsoft.Extensions.Configuration;
using System;
using System.IO;

namespace AzureFileSharing
{
    class Program
    {
        // Hard-coded values
        private const string DirectoryPath = "kidemo_dir"; // Change this to your directory path
        private const string FileName = "download-format-template-contoh-invoice-pdf.pdf";    // Change this to your file name
        private const int ExpirationSeconds = 120;      // URL expiration time in seconds (86400 = 24 hours)

        static void Main(string[] args)
        {
            Console.WriteLine("Azure File Sharing - Temporary URL Generator");
            Console.WriteLine("===========================================");

            try
            {
                // Load configuration
                IConfiguration configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                string connectionString = configuration["AzureStorage:ConnectionString"];
                string shareName = configuration["AzureStorage:ShareName"];

                if (string.IsNullOrEmpty(connectionString) || string.IsNullOrEmpty(shareName))
                {
                    Console.WriteLine("Error: Azure Storage connection string or share name is missing in appsettings.json");
                    return;
                }

                // Generate the temporary URL
                string fileUrl = GenerateTemporaryUrl(connectionString, shareName);

                if (!string.IsNullOrEmpty(fileUrl))
                {
                    // Display the URL in the console
                    Console.WriteLine("\nTemporary URL generated successfully!");
                    Console.WriteLine($"Expiration: {ExpirationSeconds} seconds from now");
                    Console.WriteLine($"Expires at: {DateTimeOffset.UtcNow.AddSeconds(ExpirationSeconds).ToLocalTime()} (local time)");
                    Console.WriteLine("\nURL:");
                    Console.WriteLine(new string('-', 80));
                    Console.WriteLine(fileUrl);
                    Console.WriteLine(new string('-', 80));
                }

                Console.WriteLine("\nPress any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
        }

        static string GenerateTemporaryUrl(string connectionString, string shareName)
        {
            try
            {
                Console.WriteLine($"\nGenerating temporary URL for file: {FileName}");
                Console.WriteLine($"Directory path: {(string.IsNullOrEmpty(DirectoryPath) ? "root" : DirectoryPath)}");
                Console.WriteLine($"Expiration time: {ExpirationSeconds} seconds ({TimeSpan.FromSeconds(ExpirationSeconds).TotalHours:0.##} hours)");

                // Create a ShareClient
                ShareClient shareClient = new ShareClient(connectionString, shareName);

                // Check if the share exists
                try
                {
                    var shareExists = shareClient.Exists();
                    if (!shareExists)
                    {
                        Console.WriteLine($"Error: The share '{shareName}' does not exist in the storage account.");
                        return null;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error checking if share exists: {ex.Message}");
                    return null;
                }

                // Get a reference to the file
                ShareDirectoryClient directoryClient = shareClient.GetDirectoryClient(DirectoryPath);
                ShareFileClient fileClient = directoryClient.GetFileClient(FileName);

                // Check if the file exists
                try
                {
                    // This will throw an exception if the file doesn't exist
                    var properties = fileClient.GetProperties();
                    Console.WriteLine($"File found: {FileName} ({properties.Value.ContentLength} bytes)");
                }
                catch (Exception)
                {
                    Console.WriteLine($"Warning: File '{FileName}' may not exist in the specified directory.");
                    Console.WriteLine("The URL will still be generated, but it won't work unless the file exists.");
                }

                // Create a SAS token that's valid for the specified number of seconds
                ShareSasBuilder sasBuilder = new ShareSasBuilder()
                {
                    ShareName = shareName,
                    Resource = "f", // f for file
                    ExpiresOn = DateTimeOffset.UtcNow.AddSeconds(ExpirationSeconds)
                };

                // If the file is in a directory, include the file path
                if (!string.IsNullOrEmpty(DirectoryPath))
                {
                    sasBuilder.FilePath = $"{DirectoryPath}/{FileName}";
                }
                else
                {
                    sasBuilder.FilePath = FileName;
                }

                // Set permissions for the SAS token (read-only)
                sasBuilder.SetPermissions(ShareFileSasPermissions.Read);

                // Get the account name and key from the connection string
                string accountName = GetAccountName(connectionString);
                string accountKey = GetAccountKey(connectionString);

                // Create a StorageSharedKeyCredential
                StorageSharedKeyCredential credential = new StorageSharedKeyCredential(accountName, accountKey);

                // Generate the SAS token
                string sasToken = sasBuilder.ToSasQueryParameters(credential).ToString();

                // Construct the full URL
                string fileUrl = $"{fileClient.Uri}?{sasToken}";

                // Return the URL
                return fileUrl;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating temporary URL: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return null; // Return null if an error occurs
            }
        }

        // Helper methods to extract account name and key from connection string
        static string GetAccountName(string connectionString)
        {
            var parts = connectionString.Split(';');
            foreach (var part in parts)
            {
                if (part.StartsWith("AccountName="))
                {
                    return part.Substring("AccountName=".Length);
                }
            }
            throw new ArgumentException("AccountName not found in connection string");
        }

        static string GetAccountKey(string connectionString)
        {
            var parts = connectionString.Split(';');
            foreach (var part in parts)
            {
                if (part.StartsWith("AccountKey="))
                {
                    return part.Substring("AccountKey=".Length);
                }
            }
            throw new ArgumentException("AccountKey not found in connection string");
        }
    }
}
