# Azure File Sharing - Temporary URL Generator

A simple console application that demonstrates file sharing by generating temporary, signed URLs for files stored in Azure File Storage.

## Features

- Generate temporary, signed URLs for files stored in Azure File Storage
- Configurable expiration time in seconds
- Automatic validation of file existence
- Simple console interface with clear output formatting

## Prerequisites

- .NET 9.0 SDK or later
- An Azure Storage account with a File Share
- Files already uploaded to your Azure File Share

## Setup

1. Clone or download this repository
2. Create an `appsettings.json` file based on the provided `appsettings.json.example` template:
   ```
   cp appsettings.json.example appsettings.json
   ```
3. Open the `appsettings.json` file and update the following settings:
   - `AzureStorage:ConnectionString`: Your Azure Storage account connection string
   - `AzureStorage:ShareName`: The name of your Azure File Share

> **Note:** The `appsettings.json` file is excluded from version control in the `.gitignore` file to prevent accidentally committing sensitive information like connection strings.

## How to Run

1. Navigate to the project directory in a terminal
2. Run the application using:
   ```
   dotnet run
   ```

## Usage

The application is configured with hard-coded values that you can modify in the `Program.cs` file:

```csharp
// Hard-coded values
private const string DirectoryPath = "documents"; // Change this to your directory path
private const string FileName = "example.pdf";    // Change this to your file name
private const int ExpirationSeconds = 86400;      // URL expiration time in seconds (86400 = 24 hours)
```

When you run the application:

1. It automatically connects to your Azure File Share
2. Checks if the specified file exists
3. Generates a temporary, signed URL with the specified expiration time
4. Displays the URL in the console with expiration information
5. Waits for you to press a key to exit

## How It Works

The application uses Azure Storage SAS (Shared Access Signature) tokens to generate temporary URLs. These URLs:

- Provide read-only access to the specified file
- Expire after the specified time period (configurable in seconds)
- Can be shared with anyone, even without Azure credentials
- Work in any browser or with tools like `curl` or `wget`

## Security Considerations

- Keep your Azure Storage connection string secure
- Set appropriate expiration times for sensitive files
- The generated URLs provide direct access to the files without authentication
- Consider using HTTPS endpoints for secure file transfers

## License

MIT
