# Example Usage

This document provides an example of how to use the Azure File Sharing application with its current implementation.

## Setup

Assume we have the following:
- An Azure Storage account named `mycompanyfiles`
- A File Share named `documents`
- A directory structure with some files:
  ```
  /
  ├── contracts/
  │   ├── contract-2023.pdf
  │   └── agreement.docx
  ├── presentations/
  │   ├── quarterly-update.pptx
  │   └── product-launch.pptx
  └── readme.txt
  ```

## Configuration

1. Create an `appsettings.json` file with your Azure Storage information:
   ```json
   {
     "AzureStorage": {
       "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=mycompanyfiles;AccountKey=yourkey;EndpointSuffix=core.windows.net",
       "ShareName": "documents"
     }
   }
   ```

2. Modify the hard-coded values in `Program.cs` to point to the file you want to share:
   ```csharp
   // Hard-coded values
   private const string DirectoryPath = "presentations"; // Directory containing the file
   private const string FileName = "quarterly-update.pptx"; // File to generate URL for
   private const int ExpirationSeconds = 172800; // 48 hours in seconds
   ```

## Generating a Temporary URL

1. Build and run the application:
   ```
   dotnet run
   ```

2. The application will automatically:
   - Connect to your Azure Storage account
   - Check if the specified file exists
   - Generate a temporary URL with the specified expiration time

3. The output will look similar to:
   ```
   Azure File Sharing - Temporary URL Generator
   ===========================================

   Generating temporary URL for file: quarterly-update.pptx
   Directory path: presentations
   Expiration time: 172800 seconds (48.00 hours)
   File found: quarterly-update.pptx (2456789 bytes)

   Temporary URL generated successfully!
   Expiration: 172800 seconds from now
   Expires at: 2023-07-15 14:30:00 (local time)

   URL:
   --------------------------------------------------------------------------------
   https://mycompanyfiles.file.core.windows.net/documents/presentations/quarterly-update.pptx?sv=2021-06-08&ss=f&srt=sco&sp=r&se=2023-07-15T14%3A30%3A00Z&st=2023-07-13T14%3A30%3A00Z&spr=https&sig=AbCdEfGhIjKlMnOpQrStUvWxYz%3D
   --------------------------------------------------------------------------------

   Press any key to exit...
   ```

4. Copy the URL and share it with anyone who needs access to the file
5. The recipient can access the file directly without Azure credentials
6. The URL will automatically expire after the specified time period

## Sharing with External Partners

Scenario: You need to share a contract with an external partner who doesn't have access to your Azure Storage.

1. Update the hard-coded values in `Program.cs`:
   ```csharp
   private const string DirectoryPath = "contracts";
   private const string FileName = "contract-2023.pdf";
   private const int ExpirationSeconds = 86400; // 24 hours
   ```

2. Run the application to generate the URL
3. Copy the URL and send it to your partner via email
4. They can click the link to download the file directly
5. After 24 hours, the link will no longer work, providing security for your sensitive documents

## Tips

- For sensitive files, use shorter expiration times (e.g., 3600 seconds = 1 hour)
- You can generate multiple URLs for the same file with different expiration times by running the application multiple times with different settings
- The URLs are read-only, so recipients cannot modify the original files
- If you need to revoke access before the expiration time, you would need to change your storage account keys (which would invalidate all SAS tokens)
