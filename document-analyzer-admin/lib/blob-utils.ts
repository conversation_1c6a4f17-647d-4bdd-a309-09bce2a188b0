/**
 * Utility functions for handling blob names and paths
 */

/**
 * Gets the blob name to use for API requests
 *
 * Priority:
 * 1. Use the provided blob_name directly if it exists (from log history data)
 * 2. If no blob_name is provided, use the path parameter
 *
 * @param path The file path or blob name to process
 * @param blob_name Optional direct blob_name from log data
 * @returns A properly formatted blob name
 */
export function getBlobName(path: string, blob_name?: string): string {
  // If blob_name is directly provided (from log history data), use it
  if (blob_name) {
    return blob_name;
  }

  if (!path) return "documents/unknown.pdf";

  // If it's a GCS URI (gs://bucket/path/to/file.pdf), extract the full path after bucket
  if (path.startsWith('gs://')) {
    const parts = path.replace('gs://', '').split('/')
    // Remove bucket name and join the rest
    return parts.slice(1).join('/')
  }

  // If the path already has the documents/ prefix, use it as is
  if (path.includes('documents/')) {
    const documentsIndex = path.indexOf('documents/')
    return path.substring(documentsIndex)
  }

  // If it's just a filename, add the documents/ prefix
  if (!path.includes('/')) {
    return `documents/${path}`
  }

  // For other paths, use as is
  return path
}

/**
 * Extracts just the filename part from a blob name or path
 *
 * @param blobName The blob name or path
 * @returns Just the filename part
 */
export function getFilenameFromBlobName(blobName: string): string {
  if (!blobName) return "unknown.pdf";
  return blobName.split('/').pop() || blobName;
}
