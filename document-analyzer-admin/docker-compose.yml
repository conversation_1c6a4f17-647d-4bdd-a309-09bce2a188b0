version: '3.8'

services:
  app:
    # build:
    #   context: .
    #   dockerfile: Dockerfile
    #   platforms:
    #     - linux/amd64
    #     - linux/arm64
    image: registry-gitlab.happyfresh.net/hf/tpd/rainmakers/document-analyzer-admin:latest
    ports:
      - "3000:3000"
    # env_file:
    #   - .env
    #   - .env.production
    environment:
      - NODE_ENV=production
      - BACKEND_HOST=http://localhost:8000
      - BACKEND_API_KEY=17EEyZKtUSfCspcVISPqAneIO5kdQrXV
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s 