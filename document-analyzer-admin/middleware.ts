import { NextRequest, NextResponse } from 'next/server';

export async function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;

  // Public paths that don't require authentication
  const isPublicPath = path === '/login' ||
                       path.startsWith('/auth/') ||
                       path === '/debug' ||
                       path === '/auth-debug';

  // Skip middleware for public paths
  if (isPublicPath) {
    return NextResponse.next();
  }

  // Since we're using localStorage for auth and not cookies,
  // we can't check auth in the middleware (which runs on the server)
  // We'll rely on the client-side ProtectedRoute component to handle auth
  return NextResponse.next();
}

// Specify which paths this middleware should run on
export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
