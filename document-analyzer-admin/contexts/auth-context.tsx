"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { AuthContextType, AuthState, AuthUser } from '@/types/auth';
import getSupabase from '@/lib/supabase';
import { useToast } from '@/components/ui/use-toast';

// Create the auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Initial auth state
const initialState: AuthState = {
  user: null,
  isLoading: true,
  error: null,
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [state, setState] = useState<AuthState>(initialState);
  const router = useRouter();
  const { toast } = useToast();
  const supabase = getSupabase();

  // Check for existing session on mount
  useEffect(() => {
    const checkSession = async () => {
      try {
        console.log('AuthContext: Checking for existing session');
        setState((prev) => ({ ...prev, isLoading: true }));

        // Get current session
        const { data: { session }, error } = await supabase.auth.getSession();

        console.log('AuthContext: Session check result:', {
          hasSession: !!session,
          hasUser: !!session?.user,
          error: error ? error.message : null
        });

        if (error) {
          throw error;
        }

        if (session?.user) {
          console.log('AuthContext: User found in session:', {
            id: session.user.id,
            email: session.user.email,
            metadata: session.user.user_metadata
          });

          setState({
            user: session.user as AuthUser,
            isLoading: false,
            error: null,
          });
        } else {
          console.log('AuthContext: No user in session');
          setState({
            user: null,
            isLoading: false,
            error: null,
          });
        }
      } catch (error) {
        console.error('AuthContext: Error checking auth session:', error);
        setState({
          user: null,
          isLoading: false,
          error: error instanceof Error ? error : new Error('Unknown authentication error'),
        });
      }
    };

    checkSession();

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('AuthContext: Auth state changed:', { event, hasSession: !!session, hasUser: !!session?.user });

        if (session?.user) {
          console.log('AuthContext: User in auth state change:', {
            id: session.user.id,
            email: session.user.email,
            metadata: session.user.user_metadata
          });

          setState({
            user: session.user as AuthUser,
            isLoading: false,
            error: null,
          });
        } else {
          console.log('AuthContext: No user in auth state change');
          setState({
            user: null,
            isLoading: false,
            error: null,
          });
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase.auth]);

  // Sign in with Google
  const signInWithGoogle = async () => {
    try {
      setState((prev) => ({ ...prev, isLoading: true }));

      // Get the configured redirect URL from window.ENV or environment or use a default
      // This should match what you've set in Google OAuth console
      const redirectUrl = typeof window !== 'undefined' && window.ENV?.NEXT_PUBLIC_AUTH_REDIRECT_URL
                         ? window.ENV.NEXT_PUBLIC_AUTH_REDIRECT_URL
                         : process.env.NEXT_PUBLIC_AUTH_REDIRECT_URL ||
                           'https://axyujusllpfbbgewabta.supabase.co/auth/v1/callback';

      console.log('Using redirect URL:', redirectUrl);

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          // You can also specify scopes if needed
          scopes: 'email profile',
        },
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error signing in with Google:', error);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error : new Error('Failed to sign in with Google'),
      }));

      toast({
        title: 'Authentication Error',
        description: 'Failed to sign in with Google. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      setState((prev) => ({ ...prev, isLoading: true }));

      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      setState({
        user: null,
        isLoading: false,
        error: null,
      });

      router.push('/login');
    } catch (error) {
      console.error('Error signing out:', error);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error : new Error('Failed to sign out'),
      }));

      toast({
        title: 'Error',
        description: 'Failed to sign out. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const value = {
    ...state,
    signInWithGoogle,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};
