export interface DocumentType {
  id: string
  name: string
  description?: string
}

export interface DocumentTypesResponse {
  types: DocumentType[]
}

export interface LlmConfiguration {
  id: string
  created_at: string
  system_instruction: string
  model_id: string
  provider: string
  temperature: number
  top_p: number
  top_k: number
  document_type?: string | null
  structured_output_schema?: any
}
