create table public.extraction_logs (
    id uuid default gen_random_uuid() primary key,
    created_at timestamp with time zone default now(),
    configuration_id bigint references public.prompt_configurations(id),
    file_name text not null,
    file_path text not null,
    extracted_data jsonb not null,
    raw_response jsonb not null,
    status text not null,
    error_message text
);

-- Create policy to allow authenticated users to read logs
-- create policy "Enable read access for authenticated users" on public.extraction_logs
--     for select using (auth.role() = 'authenticated');
