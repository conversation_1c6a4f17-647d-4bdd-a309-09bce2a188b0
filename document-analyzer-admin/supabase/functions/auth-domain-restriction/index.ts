// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// List of allowed email domains
const ALLOWED_DOMAINS = [
  'happyfresh.com'
];

// Function to check if an email domain is allowed
function isAllowedDomain(email: string): boolean {
  if (!email) return false;

  const domain = email.split('@')[1]?.toLowerCase();
  if (!domain) return false;

  return ALLOWED_DOMAINS.includes(domain);
}

serve(async (req) => {
  try {
    // Create a Supabase client with the Auth context of the function
    const supabaseClient = createClient(
      // Supabase API URL - env var exported by default.
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase API ANON KEY - env var exported by default.
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      // Create client with Auth context of the user that called the function.
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    )

    // Get the event data
    const { event, type } = await req.json()

    // Handle auth events
    if (type === 'auth' && event.name === 'user.created') {
      const { id, email } = event.user

      // Check if the email domain is allowed
      if (!isAllowedDomain(email)) {
        console.log(`User with email ${email} has a domain that is not allowed. Deleting user.`)

        // Delete the user if the domain is not allowed
        const { error } = await supabaseClient.auth.admin.deleteUser(id)

        if (error) {
          console.error('Error deleting user:', error)
          return new Response(JSON.stringify({ error: 'Failed to delete unauthorized user' }), {
            headers: { 'Content-Type': 'application/json' },
            status: 500,
          })
        }

        return new Response(JSON.stringify({
          message: 'User deleted due to unauthorized email domain',
          allowed_domains: ALLOWED_DOMAINS
        }), {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        })
      }

      console.log(`User with email ${email} has an allowed domain. User creation successful.`)
    }

    return new Response(JSON.stringify({ message: 'Webhook processed successfully' }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})
