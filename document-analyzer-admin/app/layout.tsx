import type React from "react"
import "./globals.css"
import type { Metadata } from "next"
import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { Providers } from "./providers"
import Script from "next/script"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Invoice Data Extraction",
  description: "Extract data from invoices using Google Vertex AI",
  generator: "v0.dev",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <Script id="env-script" strategy="beforeInteractive">
          {`
            window.ENV = {
              NEXT_PUBLIC_BACKEND_HOST: "${process.env.NEXT_PUBLIC_BACKEND_HOST || 'http://localhost:8000'}",
              NEXT_PUBLIC_SUPABASE_URL: "${process.env.NEXT_PUBLIC_SUPABASE_URL || ''}",
              NEXT_PUBLIC_SUPABASE_ANON_KEY: "${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''}",
              NEXT_PUBLIC_AUTH_REDIRECT_URL: "${process.env.NEXT_PUBLIC_AUTH_REDIRECT_URL || ''}",
            };
          `}
        </Script>
      </head>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false} forcedTheme="light">
          <Providers>
            <main className="min-h-screen bg-gray-50 p-4 md:p-8">{children}</main>
            <Toaster />
          </Providers>
        </ThemeProvider>
      </body>
    </html>
  )
}
