"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/auth-context";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import getSupabase from "@/lib/supabase";

export default function AuthDebugPage() {
  const { user, isLoading } = useAuth();
  const [localStorageData, setLocalStorageData] = useState<Record<string, any>>({});
  const [sessionStorageData, setSessionStorageData] = useState<Record<string, any>>({});
  const [cookieData, setCookieData] = useState<string[]>([]);
  const [directSessionCheck, setDirectSessionCheck] = useState<any>(null);
  const [isCheckingSession, setIsCheckingSession] = useState(false);

  useEffect(() => {
    // Get localStorage data
    const lsData: Record<string, any> = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        try {
          const value = localStorage.getItem(key);
          if (value) {
            try {
              lsData[key] = JSON.parse(value);
            } catch {
              lsData[key] = value;
            }
          }
        } catch (e) {
          console.error("Error reading localStorage:", e);
        }
      }
    }
    setLocalStorageData(lsData);

    // Get sessionStorage data
    const ssData: Record<string, any> = {};
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key) {
        try {
          const value = sessionStorage.getItem(key);
          if (value) {
            try {
              ssData[key] = JSON.parse(value);
            } catch {
              ssData[key] = value;
            }
          }
        } catch (e) {
          console.error("Error reading sessionStorage:", e);
        }
      }
    }
    setSessionStorageData(ssData);

    // Get cookie data
    setCookieData(document.cookie.split(";").map(c => c.trim()));
  }, []);

  const checkSession = async () => {
    setIsCheckingSession(true);
    try {
      const supabase = getSupabase();
      const { data, error } = await supabase.auth.getSession();
      setDirectSessionCheck({ data, error });
    } catch (error) {
      setDirectSessionCheck({ error });
    } finally {
      setIsCheckingSession(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Auth Debug Page</h1>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Auth Context State</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-2">Loading: {isLoading ? "Yes" : "No"}</p>
            <p className="mb-2">User: {user ? "Authenticated" : "Not Authenticated"}</p>
            {user && (
              <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-60">
                {JSON.stringify(user, null, 2)}
              </pre>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Direct Session Check</CardTitle>
          </CardHeader>
          <CardContent>
            <Button
              onClick={checkSession}
              disabled={isCheckingSession}
              className="mb-4"
            >
              {isCheckingSession ? "Checking..." : "Check Session"}
            </Button>

            {directSessionCheck && (
              <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-60">
                {JSON.stringify(directSessionCheck, null, 2)}
              </pre>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Local Storage</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-60">
              {JSON.stringify(localStorageData, null, 2)}
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Session Storage</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-60">
              {JSON.stringify(sessionStorageData, null, 2)}
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Cookies</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-60">
              {JSON.stringify(cookieData, null, 2)}
            </pre>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
