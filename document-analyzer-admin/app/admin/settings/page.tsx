"use client"

import { useState, useEffect } from "react"
import { Expand, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { useToast } from "@/components/ui/use-toast"
import modelsConfig from "@/config/models.json"
import type { Model } from "@/types/models"
import type { DocumentType } from "@/types/document-types"

export default function SettingsPage() {
  const { toast } = useToast()
  const [saveLoading, setSaveLoading] = useState(false)
  const [currentConfigVersion, setCurrentConfigVersion] = useState<string | null>(null)
  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([])
  const [loadingDocumentTypes, setLoadingDocumentTypes] = useState(false)
  const [loadingConfig, setLoadingConfig] = useState(false)

  // Form state
  const [selectedDocumentType, setSelectedDocumentType] = useState<string>("")
  const [systemInstruction, setSystemInstruction] = useState("")
  const [selectedModel, setSelectedModel] = useState<string>(modelsConfig.models[0]?.id || "")
  const [temperature, setTemperature] = useState(0.2)
  const [topP, setTopP] = useState(0.8)
  const [topK, setTopK] = useState(40)

  // Fetch document types on component mount
  useEffect(() => {
    const fetchDocumentTypes = async () => {
      setLoadingDocumentTypes(true)
      try {
        const response = await fetch("/api/document-types")
        const data = await response.json()

        if (response.ok) {
          setDocumentTypes(data.types || [])

          // Show warning if using fallback types
          if (data.warning) {
            console.warn("Using fallback document types:", data.warning)
            toast({
              title: "Warning",
              description: "Using default document types. Some features may be limited.",
              variant: "default",
            })
          }
        } else {
          console.error("Failed to fetch document types:", data.error || response.statusText)
          toast({
            title: "Error",
            description: "Failed to load document types. Using defaults.",
            variant: "destructive",
          })

          // Use any types that might have been returned despite the error
          if (data.types && data.types.length > 0) {
            setDocumentTypes(data.types)
          }
        }
      } catch (error) {
        console.error("Error fetching document types:", error)
        toast({
          title: "Error",
          description: "An error occurred while loading document types.",
          variant: "destructive",
        })
      } finally {
        setLoadingDocumentTypes(false)
      }
    }

    fetchDocumentTypes()
  }, [toast])

  // Fetch configuration when document type is selected
  useEffect(() => {
    const fetchConfigForDocumentType = async () => {
      if (!selectedDocumentType) {
        // Reset form if no document type is selected
        setSystemInstruction("")
        setSelectedModel(modelsConfig.models[0]?.id || "")
        setTemperature(0.2)
        setTopP(0.8)
        setTopK(40)
        setCurrentConfigVersion(null)
        return
      }

      setLoadingConfig(true)
      try {
        const response = await fetch(`/api/config?document_type=${encodeURIComponent(selectedDocumentType)}`)
        if (response.ok) {
          const config = await response.json()
          setSystemInstruction(config.system_instruction || "")
          setSelectedModel(config.model_id || modelsConfig.models[0]?.id || "")
          setTemperature(config.temperature ?? 0.2)
          setTopP(config.top_p ?? 0.8)
          setTopK(config.top_k ?? 40)
          setCurrentConfigVersion(config.id)
        } else if (response.status === 404) {
          // Reset form for new configuration
          setSystemInstruction("")
          setSelectedModel(modelsConfig.models[0]?.id || "")
          setTemperature(0.2)
          setTopP(0.8)
          setTopK(40)
          setCurrentConfigVersion(null)
        } else {
          console.error("Failed to fetch configuration for document type")
          toast({
            title: "Error",
            description: "Failed to load configuration for the selected document type.",
            variant: "destructive",
          })
        }
      } catch (error) {
        console.error("Error fetching configuration:", error)
        toast({
          title: "Error",
          description: "An error occurred while loading configuration.",
          variant: "destructive",
        })
      } finally {
        setLoadingConfig(false)
      }
    }

    fetchConfigForDocumentType()
  }, [selectedDocumentType, toast])

  const handleSaveConfiguration = async () => {
    if (!selectedDocumentType) {
      toast({
        title: "Error",
        description: "Please select a document type first.",
        variant: "destructive",
      })
      return
    }

    setSaveLoading(true)
    try {
      const configToSave = {
        system_instruction: systemInstruction,
        temperature: temperature,
        top_p: topP,
        top_k: topK,
        model_id: selectedModel,
        document_type: selectedDocumentType,
      }

      const response = await fetch("/api/config", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(configToSave),
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Configuration saved.",
        })
        const savedConfig = await response.json()
        setCurrentConfigVersion(savedConfig.id)
      } else {
        const errorData = await response.json()
        console.error("Error response:", errorData)
        toast({
          title: "Error",
          description: `Failed to save configuration: ${errorData.details || errorData.error || "Unknown error"}`,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error saving configuration:", error)
      toast({
        title: "Error",
        description: "An error occurred while saving configuration.",
        variant: "destructive",
      })
    } finally {
      setSaveLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold mb-8">Settings</h1>

      <Card>
        <CardHeader>
          <CardTitle>Configure Extraction Prompt</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-4 gap-6">
              <div className="col-span-3 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="documentType">Document Type</Label>
                  <Select
                    value={selectedDocumentType}
                    onValueChange={setSelectedDocumentType}
                    disabled={loadingDocumentTypes}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a document type" />
                    </SelectTrigger>
                    <SelectContent>
                      {documentTypes.map((docType) => (
                        <SelectItem key={docType.id} value={docType.id}>
                          {docType.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    Select the document type to configure extraction settings for.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model">Model</Label>
                  <Select
                    value={selectedModel}
                    onValueChange={setSelectedModel}
                    disabled={!selectedDocumentType || loadingConfig}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {modelsConfig.models.map((model: Model) => (
                        <SelectItem key={model.id} value={model.id}>
                          {model.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    {modelsConfig.models.find((m) => m.id === selectedModel)?.description ||
                      "Select the model to use for extraction."}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="systemInstruction">System Instruction</Label>
                  <div className="relative">
                    <Textarea
                      id="systemInstruction"
                      value={systemInstruction}
                      onChange={(e) => setSystemInstruction(e.target.value)}
                      rows={18}
                      className="font-mono text-sm pr-12"
                      disabled={!selectedDocumentType || loadingConfig}
                    />
                    <Sheet>
                      <SheetTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute top-0 right-3 h-6 w-6 opacity-50 hover:opacity-100"
                        >
                          <Expand className="h-4 w-4" />
                          <span className="sr-only">Expand Editor</span>
                        </Button>
                      </SheetTrigger>
                      <SheetContent side="right" className="w-[90vw] sm:max-w-[1000px]">
                        <SheetHeader>
                          <SheetTitle>System Instruction</SheetTitle>
                        </SheetHeader>
                        <Textarea
                          value={systemInstruction}
                          onChange={(e) => setSystemInstruction(e.target.value)}
                          className="min-h-[80vh] mt-4 font-mono text-sm"
                          disabled={!selectedDocumentType || loadingConfig}
                        />
                      </SheetContent>
                    </Sheet>
                  </div>
                </div>
              </div>

              {/* Right Column - Model Parameters */}
              <div className="col-span-1 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="temperature">Temperature</Label>
                  <Input
                    id="temperature"
                    type="number"
                    step="0.1"
                    min="0"
                    max="1"
                    value={temperature}
                    onChange={(e) => setTemperature(Number.parseFloat(e.target.value))}
                    disabled={!selectedDocumentType || loadingConfig}
                  />
                  <p className="text-sm text-gray-500">
                    Controls randomness in the output. Lower values make the output more focused and deterministic. (0.0
                    - 1.0)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="topP">Top P</Label>
                  <Input
                    id="topP"
                    type="number"
                    step="0.1"
                    min="0"
                    max="1"
                    value={topP}
                    onChange={(e) => setTopP(Number.parseFloat(e.target.value))}
                    disabled={!selectedDocumentType || loadingConfig}
                  />
                  <p className="text-sm text-gray-500">
                    Nucleus sampling. The model considers tokens whose probability sums to p. (0.0 - 1.0)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="topK">Top K</Label>
                  <Input
                    id="topK"
                    type="number"
                    step="1"
                    min="1"
                    max="40"
                    value={topK}
                    onChange={(e) => setTopK(Number.parseInt(e.target.value, 10))}
                    disabled={!selectedDocumentType || loadingConfig}
                  />
                  <p className="text-sm text-gray-500">
                    Limits the prediction to the top k most likely tokens (1 - 40).
                  </p>
                </div>

                {currentConfigVersion && (
                  <div className="pt-4 border-t mt-6">
                    <p className="text-sm text-gray-500">
                      Current configuration version: <span className="font-mono">{currentConfigVersion}</span>
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div className="h-px bg-border" />

            <div className="flex justify-end">
              <Button
                onClick={handleSaveConfiguration}
                disabled={saveLoading || !selectedDocumentType || loadingConfig}
                type="button"
              >
                {saveLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : loadingConfig ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  "Save Configuration"
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
