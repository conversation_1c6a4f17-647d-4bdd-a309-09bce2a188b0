"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { Eye, Download, Search } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Trash2 } from "lucide-react"
import ResultPage from "@/components/result-page"
// Import blob utilities
import { getFilenameFromBlobName } from "@/lib/blob-utils"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import {
  <PERSON>,
  <PERSON>Content,
  <PERSON>I<PERSON>,
  <PERSON><PERSON>rigger,
  <PERSON>Value,
} from "@/components/ui/select"

interface ExtractionLog {
  id: number
  created_at: string
  blob_name: string
  document_size: number
  extracted_data: any
  raw_llm_response: any
  llm_configuration_id: number
  processing_time: number
  color_analysis_results: {
    color_page_count: number
    color_pages: number[]
    has_color: boolean
    total_pages: number
    detailed_results: Array<{
      color_percentage: number
      is_color: boolean
      page: number
      pixels_analyzed: number
    }>
  }
  field_validations?: Record<string, boolean>
  validation_results?: Array<{
    document_processing_log_id: number
    field_key: string
    id: number
    is_valid: boolean
    validated_at: string
  }>
}

async function deleteLog(id: number) {
  try {
    // Use the API route as a proxy to the backend to avoid CORS issues
    const response = await fetch(`/api/logs?id=${id}`, {
      method: "DELETE",
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`Error response (${response.status}):`, errorText)
      throw new Error(errorText || "Failed to delete log")
    }

    return true
  } catch (error) {
    console.error("Error deleting log:", error)
    throw error
  }
}

// Main component for the history page
export default function HistoryPage() {
  // State variables
  const [logs, setLogs] = useState<ExtractionLog[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedLog, setSelectedLog] = useState<ExtractionLog | null>(null)
  const [viewingResults, setViewingResults] = useState(false)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)

  useEffect(() => {
    fetchLogs(currentPage, pageSize)
  }, [currentPage, pageSize])

  const fetchLogs = async (page = currentPage, pageSize = 10) => {
    try {
      setLoading(true)
      setError(null)

      // Fetch logs from our API endpoint with pagination parameters
      const response = await fetch(`/api/logs?page=${page}&page_size=${pageSize}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error("Error response:", errorText)
        throw new Error(`Error fetching logs: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      // Check if the response has the expected structure
      if (data && data.items) {
        setLogs(data.items || [])

        // Update pagination state
        if (data.pagination) {
          setCurrentPage(data.pagination.page || page)
          setPageSize(data.pagination.page_size || pageSize)
          setTotalPages(data.pagination.pages || 1)
          setTotalItems(data.pagination.total || data.items.length)
        }
      } else {
        // Fallback for backward compatibility if the API hasn't been updated yet
        setLogs(data || [])
      }
    } catch (error: any) {
      console.error("Error fetching logs:", error)
      setError(error.message || "Failed to load extraction history")
      setLogs([])
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
    // Reset to first page when searching
    if (currentPage !== 1) {
      setCurrentPage(1)
    }
  }

  // Helper function to get field value regardless of format
  const getFieldValue = (obj: any, key: string): any => {
    if (!obj || !obj[key]) return '';

    // If it's the old format with value property
    if (typeof obj[key] === 'object' && 'value' in obj[key]) {
      return obj[key].value;
    }

    // If it's the new format with direct values
    return obj[key];
  };

  const filteredLogs = logs.filter(
    (log) => {
      const documentType = getFieldValue(log.extracted_data, 'document_type') || '';
      const vendorName = getFieldValue(log.extracted_data, 'vendor_name') || '';
      const invoiceNumber = getFieldValue(log.extracted_data, 'invoice_number') || '';

      const searchTermLower = searchTerm.toLowerCase();

      return log.blob_name.toLowerCase().includes(searchTermLower) ||
             documentType.toString().toLowerCase().includes(searchTermLower) ||
             vendorName.toString().toLowerCase().includes(searchTermLower) ||
             invoiceNumber.toString().toLowerCase().includes(searchTermLower);
    }
  )

  const handleViewResults = (log: ExtractionLog) => {
    // Check if extracted_data is valid before setting the selected log
    if (!log.extracted_data) {
      alert("Cannot view results: The extraction data is missing or invalid.")
      return
    }

    setSelectedLog(log)
    setViewingResults(true)
  }

  const handleBackToHistory = () => {
    setViewingResults(false)
    setSelectedLog(null)
  }

  // Handle page change
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return
    setCurrentPage(page)
  }

  // Handle page size change
  const handlePageSizeChange = (value: string) => {
    const newPageSize = parseInt(value)
    setPageSize(newPageSize)
    // Reset to first page when changing page size
    setCurrentPage(1)
  }

  const getStatusBadge = (log: ExtractionLog) => {
    // Determine status based on whether there was an error in processing
    if (log.processing_time > 0) {
      return <Badge className="bg-green-500">Success</Badge>
    } else {
      return <Badge variant="destructive">Error</Badge>
    }
  }

  const getValidationBadge = (log: ExtractionLog) => {
    // First check if we have field_validations (either original or converted from validation_results)
    if (log.field_validations && Object.keys(log.field_validations).length > 0) {
      const correctFields = Object.values(log.field_validations).filter(v => v === true).length;
      const incorrectFields = Object.values(log.field_validations).filter(v => v === false).length;

      if (correctFields > 0 && incorrectFields === 0) {
        return (
          <div className="flex flex-col gap-1 max-w-fit">
            <Badge className="bg-blue-500">{correctFields} ✔</Badge>
          </div>
        )
      } else if (incorrectFields > 0 && correctFields === 0) {
        return (
          <div className="flex flex-col gap-1 max-w-fit">
            <Badge variant="destructive">{incorrectFields} ✘</Badge>
          </div>
        )
      } else if (correctFields > 0 && incorrectFields > 0) {
        return (
          <div className="flex flex-col gap-1 max-w-fit">
            <Badge className="bg-blue-500">{correctFields} ✔</Badge>
            <Badge variant="destructive">{incorrectFields} ✘</Badge>
          </div>
        )
      }
    }

    // If we have validation_results but no field_validations (should not happen after our API changes, but just in case)
    if (log.validation_results && log.validation_results.length > 0) {
      const correctFields = log.validation_results.filter(v => v.is_valid === true).length;
      const incorrectFields = log.validation_results.filter(v => v.is_valid === false).length;

      if (correctFields > 0 && incorrectFields === 0) {
        return (
          <div className="flex flex-col gap-1 max-w-fit">
            <Badge className="bg-blue-500">{correctFields} ✔</Badge>
          </div>
        )
      } else if (incorrectFields > 0 && correctFields === 0) {
        return (
          <div className="flex flex-col gap-1 max-w-fit">
            <Badge variant="destructive">{incorrectFields} ✘</Badge>
          </div>
        )
      } else if (correctFields > 0 && incorrectFields > 0) {
        return (
          <div className="flex flex-col gap-1 max-w-fit">
            <Badge className="bg-blue-500">{correctFields} ✔</Badge>
            <Badge variant="destructive">{incorrectFields} ✘</Badge>
          </div>
        )
      }
    }

    // If no validation data found
    return <Badge variant="outline">Not Validated</Badge>
  }

  const handleFieldValidationChange = async (fieldKey: string, isValid: boolean) => {
    if (!selectedLog) return

    try {
      // Create updated field validations for local state
      const updatedFieldValidations = {
        ...(selectedLog.field_validations || {}),
        [fieldKey]: isValid
      }

      // Update the local state immediately for a responsive UI
      setSelectedLog({
        ...selectedLog,
        field_validations: updatedFieldValidations
      })

      // Update the logs list
      setLogs(logs.map(log =>
        log.id === selectedLog.id ? { ...log, field_validations: updatedFieldValidations } : log
      ))

      // Fetch all validations to ensure we have the latest data
      const response = await fetch(`/api/logs/${selectedLog.id}/validations`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (response.ok) {
        const data = await response.json()

        // Convert the validation results to the format expected by the component
        if (data && data.results && Array.isArray(data.results)) {
          const validationMap: Record<string, boolean> = {}
          data.results.forEach((validation: { field_key: string; is_valid: boolean }) => {
            validationMap[validation.field_key] = validation.is_valid
          })

          // Update with the latest validations from the server
          setSelectedLog(prev => {
            if (!prev) return null
            return {
              ...prev,
              field_validations: validationMap
            }
          })

          // Update the logs list with the latest validations
          setLogs(logs.map(log =>
            log.id === selectedLog.id ? { ...log, field_validations: validationMap } : log
          ))
        }
      }
    } catch (error) {
      console.error("Error updating field validation:", error)
    }
  }

  const downloadPdf = (blobName: string) => {
    try {
      // Simplified download function
      const backendHost = process.env.NEXT_PUBLIC_BACKEND_HOST || "http://localhost:8000";
      const apiKey = process.env.BACKEND_API_KEY;

      if (!apiKey) {
        console.error("API key is not defined");
        alert("Cannot download PDF: API key is not configured");
        return;
      }

      // Create the download URL
      const downloadUrl = `${backendHost}/documents/processing-logs/download-file?blob_name=${blobName}`;

      // Create a temporary link and trigger download
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = getFilenameFromBlobName(blobName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error downloading file:", error);
      alert("Failed to download file. Please try again.");
    }
  }

  if (viewingResults && selectedLog) {
    const colorAnalysisData = selectedLog.color_analysis_results && selectedLog.color_analysis_results.color_pages.length > 0
      ? {
          colored_pages: selectedLog.color_analysis_results.color_pages,
          full_analysis: selectedLog.color_analysis_results
        }
      : undefined;

    return (
      <ResultPage
        result={selectedLog.extracted_data}
        filePath={selectedLog.extracted_data?.gs_uri || ""}
        blob_name={selectedLog.blob_name}
        onBack={handleBackToHistory}
        logId={String(selectedLog.id)}
        fieldValidations={selectedLog.field_validations}
        onFieldValidationChange={handleFieldValidationChange}
        colorAnalysis={colorAnalysisData}
      />
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Extraction History</h1>
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
          <Input placeholder="Search files..." className="pl-8" value={searchTerm} onChange={handleSearch} />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Processed Files</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-md" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="rounded-md bg-red-50 p-4 text-red-800">
              <p className="font-medium">Error loading extraction history</p>
              <p className="text-sm mt-1">{error}</p>
              <Button variant="outline" size="sm" onClick={() => fetchLogs()} className="mt-4">
                Try Again
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Vendor Name</TableHead>
                    <TableHead>Invoice Number</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Validation</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                        No extraction logs found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell className="font-medium">
                          {format(new Date(log.created_at), "dd/MM/yyyy HH:mm")}
                        </TableCell>
                        <TableCell>{getFieldValue(log.extracted_data, 'vendor_name')}</TableCell>
                        <TableCell>{getFieldValue(log.extracted_data, 'invoice_number')}</TableCell>
                        <TableCell>{getStatusBadge(log)}</TableCell>
                        <TableCell>{getValidationBadge(log)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewResults(log)}
                              className="flex items-center gap-1"
                            >
                              <Eye className="h-4 w-4" />
                              View
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => downloadPdf(log.blob_name)}
                              className="flex items-center gap-1"
                            >
                              <Download className="h-4 w-4" />
                              Download
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={async () => {
                                if (confirm("Are you sure you want to delete this record?")) {
                                  try {
                                    await deleteLog(log.id)
                                    // Refresh the current page
                                    fetchLogs(currentPage, pageSize)
                                  } catch (error) {
                                    alert("Failed to delete record. Please try again.")
                                  }
                                }
                              }}
                              className="flex items-center gap-1"
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                              Delete
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              {/* Pagination UI */}
              {!loading && !error && (
                <div className="py-4 px-2">
                  {totalPages > 1 && (
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => handlePageChange(currentPage - 1)}
                            className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
                          />
                        </PaginationItem>

                        {/* First page */}
                        {currentPage > 2 && (
                          <PaginationItem>
                            <PaginationLink onClick={() => handlePageChange(1)}>
                              1
                            </PaginationLink>
                          </PaginationItem>
                        )}

                        {/* Ellipsis if needed */}
                        {currentPage > 3 && (
                          <PaginationItem>
                            <PaginationEllipsis />
                          </PaginationItem>
                        )}

                        {/* Previous page if not on first page */}
                        {currentPage > 1 && (
                          <PaginationItem>
                            <PaginationLink onClick={() => handlePageChange(currentPage - 1)}>
                              {currentPage - 1}
                            </PaginationLink>
                          </PaginationItem>
                        )}

                        {/* Current page */}
                        <PaginationItem>
                          <PaginationLink isActive onClick={() => handlePageChange(currentPage)}>
                            {currentPage}
                          </PaginationLink>
                        </PaginationItem>

                        {/* Next page if not on last page */}
                        {currentPage < totalPages && (
                          <PaginationItem>
                            <PaginationLink onClick={() => handlePageChange(currentPage + 1)}>
                              {currentPage + 1}
                            </PaginationLink>
                          </PaginationItem>
                        )}

                        {/* Ellipsis if needed */}
                        {currentPage < totalPages - 2 && (
                          <PaginationItem>
                            <PaginationEllipsis />
                          </PaginationItem>
                        )}

                        {/* Last page if not already shown */}
                        {currentPage < totalPages - 1 && (
                          <PaginationItem>
                            <PaginationLink onClick={() => handlePageChange(totalPages)}>
                              {totalPages}
                            </PaginationLink>
                          </PaginationItem>
                        )}

                        <PaginationItem>
                          <PaginationNext
                            onClick={() => handlePageChange(currentPage + 1)}
                            className={currentPage >= totalPages ? "pointer-events-none opacity-50" : ""}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  )}

                  <div className="flex items-center justify-between mt-4">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">Show</span>
                      <Select
                        value={pageSize.toString()}
                        onValueChange={handlePageSizeChange}
                      >
                        <SelectTrigger className="w-[80px]">
                          <SelectValue placeholder={pageSize.toString()} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="10">10</SelectItem>
                          <SelectItem value="25">25</SelectItem>
                          <SelectItem value="50">50</SelectItem>
                        </SelectContent>
                      </Select>
                      <span className="text-sm text-gray-500">items per page</span>
                    </div>

                    <div className="text-sm text-gray-500">
                      Showing page {currentPage} of {totalPages} ({totalItems} total items)
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
