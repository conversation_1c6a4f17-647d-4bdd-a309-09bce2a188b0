"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2, AlertCircle } from "lucide-react";
import Link from "next/link";

export default function LoginPage() {
  const { user, isLoading, signInWithGoogle } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);

  // Check for error in URL
  useEffect(() => {
    const errorParam = searchParams.get('error');
    if (errorParam) {
      switch (errorParam) {
        case 'configuration_error':
          setError('Authentication configuration error. Please contact support.');
          break;
        case 'auth_error':
          setError('Authentication failed. Please try again.');
          break;
        case 'session_error':
          setError('Session error. Please try again.');
          break;
        case 'unexpected':
          setError('An unexpected error occurred. Please try again.');
          break;
        default:
          setError(`Error: ${errorParam}`);
      }
    }
  }, [searchParams]);

  // Redirect to admin dashboard if already logged in
  useEffect(() => {
    if (user && !isLoading) {
      router.push("/admin");
    }
  }, [user, isLoading, router]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Invoice Data Extraction</CardTitle>
          <CardDescription>
            Sign in to access the admin dashboard
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-4">
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button
            className="w-full"
            onClick={signInWithGoogle}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Signing in...
              </>
            ) : (
              <>Sign in with Google</>
            )}
          </Button>

          <div className="mt-4 text-center">
            <Link href="/debug" className="text-sm text-blue-600 hover:underline">
              Debug Authentication
            </Link>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col items-center justify-center text-center text-sm text-muted-foreground">
          <p>Only authorized users with approved email domains can access this application.</p>
        </CardFooter>
      </Card>
    </div>
  );
}
