"use client";

import { useEffect, useState } from "react";
import { createClient } from "@supabase/supabase-js";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/auth-context";

export default function DebugPage() {
  const { user, isLoading } = useAuth();
  const [envVars, setEnvVars] = useState<Record<string, string>>({});
  const [sessionData, setSessionData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Collect environment variables
    if (typeof window !== 'undefined') {
      setEnvVars({
        NEXT_PUBLIC_SUPABASE_URL: window.ENV?.NEXT_PUBLIC_SUPABASE_URL || 'Not set',
        NEXT_PUBLIC_SUPABASE_ANON_KEY: window.ENV?.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set (hidden)' : 'Not set',
        NEXT_PUBLIC_BACKEND_HOST: window.ENV?.NEXT_PUBLIC_BACKEND_HOST || 'Not set',
      });
    }
  }, []);

  const checkSession = async () => {
    try {
      setError(null);
      
      if (!window.ENV?.NEXT_PUBLIC_SUPABASE_URL || !window.ENV?.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
        setError('Missing Supabase credentials in window.ENV');
        return;
      }
      
      const supabase = createClient(
        window.ENV.NEXT_PUBLIC_SUPABASE_URL,
        window.ENV.NEXT_PUBLIC_SUPABASE_ANON_KEY
      );
      
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        setError(error.message);
        return;
      }
      
      setSessionData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Authentication Debug</h1>
      
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Environment Variables</CardTitle>
            <CardDescription>Check if environment variables are properly set</CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded-md overflow-auto">
              {JSON.stringify(envVars, null, 2)}
            </pre>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Auth Context State</CardTitle>
            <CardDescription>Current state from the Auth Context</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
              <p><strong>User:</strong> {user ? 'Authenticated' : 'Not authenticated'}</p>
            </div>
            
            {user && (
              <pre className="bg-gray-100 p-4 rounded-md overflow-auto">
                {JSON.stringify({
                  id: user.id,
                  email: user.email,
                  metadata: user.user_metadata
                }, null, 2)}
              </pre>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Direct Session Check</CardTitle>
            <CardDescription>Check session directly with Supabase</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={checkSession} className="mb-4">Check Session</Button>
            
            {error && (
              <div className="text-red-500 mb-4">
                <p><strong>Error:</strong> {error}</p>
              </div>
            )}
            
            {sessionData && (
              <pre className="bg-gray-100 p-4 rounded-md overflow-auto">
                {JSON.stringify(sessionData, null, 2)}
              </pre>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
