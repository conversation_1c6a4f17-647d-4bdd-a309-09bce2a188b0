import { type NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import logger from "../../../lib/logger"

// Get backend host from environment variables
const backendHost = process.env.BACKEND_HOST
const apiKey = process.env.BACKEND_API_KEY

if (!backendHost) {
  logger.error("Backend host not configured")
  throw new Error("Backend host configuration missing")
}

if (!apiKey) {
  logger.error("Backend API key not configured")
  throw new Error("Backend API key configuration missing")
}

// Initialize Supabase client for logging
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseKey) {
  logger.error("Supabase URL or Key not configured")
}

const supabase = createClient(supabaseUrl!, supabaseKey!)



async function storeExtractionLog(params: {
  configId: number
  extractedData: any
  rawResponse: any
  status: string
  errorMessage?: string | null
  fileName: string
  filePath: string
  coloredPages?: number[]
}): Promise<string | null> {
  try {
    const { data, error } = await supabase.from("extraction_logs").insert([
      {
        configuration_id: params.configId,
        extracted_data: params.extractedData,
        raw_response: params.rawResponse,
        status: params.status,
        error_message: params.errorMessage,
        file_name: params.fileName,
        file_path: params.filePath,
        colored_pages: params.coloredPages || [],
      },
    ]).select("id").single()

    if (error) throw error

    return data?.id || null
  } catch (error) {
    logger.error("Failed to store extraction log:", error)
    // Don't throw - we don't want to fail the main operation if logging fails
    return null
  }
}

export async function POST(request: NextRequest) {
  logger.debug("Extract API route started")
  try {
    const formData = await request.formData()
    const file = formData.get("file") as File | null

    if (!file) {
      return NextResponse.json({ error: "No file provided for extraction" }, { status: 400 });
    }

    logger.info(`Processing file: ${file.name}, size: ${file.size} bytes`);

    // Create a new FormData object to forward to the backend service
    const backendFormData = new FormData();
    backendFormData.append("file", file);

    // Call the backend service
    logger.info(`Calling backend service at: ${backendHost}/documents/`);
    const response = await fetch(`${backendHost}/documents/`, {
      method: "POST",
      headers: {
        "X-Api-Key": apiKey as string
      },
      body: backendFormData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error(`Backend service error: ${response.status}, ${errorText}`);
      return NextResponse.json(
        { error: `Error from document processing service: ${response.statusText}` },
        { status: response.status }
      );
    }

    // Parse the response from the backend
    const result = await response.json();
    logger.debug("Backend service response:", result);

    // Extract colored pages from the response
    const coloredPages = result.color_pages || [];

    // Extract document_processing_log_id from the backend response
    const backendLogId = result.document_processing_log_id;
    logger.debug("Backend document_processing_log_id:", backendLogId);

    // Store the extraction log
    const logId = await storeExtractionLog({
      configId: 0, // We don't have config ID from the backend
      extractedData: result,
      rawResponse: result,
      status: "success",
      fileName: file.name,
      filePath: result.gs_uri || "", // Use the GS URI as the file path
      coloredPages: coloredPages,
    });

    // Helper function to get value regardless of format
    const getValue = (obj: any, key: string): any => {
      if (!obj || obj[key] === undefined) return null;

      // If it's the old format with value property
      if (typeof obj[key] === 'object' && obj[key] !== null && 'value' in obj[key]) {
        return obj[key].value;
      }

      // If it's the new format with direct values
      return obj[key];
    };

    // Transform the response to match the expected format in the frontend
    const transformedResult = {
      vendor_name: { value: getValue(result, 'vendor_name') },
      invoice_date: { value: getValue(result, 'invoice_date') },
      invoice_number: { value: getValue(result, 'invoice_number') },
      tax_invoice_number: { value: getValue(result, 'tax_invoice_number') },
      total_amount: { value: getValue(result, 'total_amount') },
      total_ppn: { value: getValue(result, 'total_ppn') },
      is_bw: { value: coloredPages.length === 0 },
      document_type: { value: getValue(result, 'document_type') },
      total_pages: { value: getValue(result, 'total_pages') },
    };

    // Extract the blob_name from the gs_uri
    const gsUri = result.gs_uri || "";
    const blobName = gsUri.startsWith('gs://')
      ? gsUri.replace('gs://', '').split('/').slice(1).join('/')
      : `documents/${file.name.replace(/\s+/g, '_')}`;

    // Log the transformed result for debugging
    logger.debug("Transformed result:", transformedResult);

    return NextResponse.json({
      result: transformedResult,
      filePath: gsUri,
      blob_name: blobName,  // Include the blob_name in the response
      logId: backendLogId || logId, // Prefer backend log ID if available
      colorAnalysis: {
        colored_pages: coloredPages,
        full_analysis: result
      },
    });
  } catch (error: any) {
    logger.error({ error }, "Error in Extract API route");
    return NextResponse.json({ error: `Internal server error: ${error.message}` }, { status: 500 });
  }
}
