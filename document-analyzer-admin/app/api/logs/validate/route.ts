import { NextResponse } from "next/server"
import logger from "../../../../lib/logger"

// Get backend host from environment variables
const backendHost = process.env.BACKEND_HOST

if (!backendHost) {
  logger.error("Backend host not configured")
  throw new Error("Backend host configuration missing")
}

/**
 * @deprecated This endpoint is deprecated. Use /api/logs/[logId]/validations instead.
 * This endpoint will be removed in a future release.
 */
export async function POST(request: Request) {
  try {
    const { id, fieldKey, isValid } = await request.json()

    if (!id) {
      return NextResponse.json(
        { error: "Missing required parameter: id" },
        { status: 400 }
      )
    }

    if (!fieldKey) {
      return NextResponse.json(
        { error: "Missing required parameter: fieldKey" },
        { status: 400 }
      )
    }

    if (typeof isValid !== 'boolean') {
      return NextResponse.json(
        { error: "isValid must be a boolean value" },
        { status: 400 }
      )
    }

    logger.warn(
      { id, fieldKey, isValid },
      "DEPRECATED: Using old validation endpoint. Please update to use /api/logs/[logId]/validations"
    )

    // Forward the request to the new endpoint
    const response = await fetch(`/api/logs/${id}/validations`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        field_key: fieldKey,
        is_valid: isValid
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      logger.error(`Error updating validation: ${errorText}`)
      return NextResponse.json(
        { error: "Failed to update validation status" },
        { status: response.status }
      )
    }

    // Get the current validations to maintain backward compatibility
    const validationsResponse = await fetch(`/api/logs/${id}/validations`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })

    if (!validationsResponse.ok) {
      logger.error("Failed to fetch validations after update")
      // Return success anyway since the update succeeded
      return NextResponse.json({
        success: true,
        id,
        fieldKey,
        isValid,
        fieldValidations: { [fieldKey]: isValid }
      })
    }

    const validationsData = await validationsResponse.json()

    // Convert the validation results to the format expected by the old endpoint
    const validationMap: Record<string, boolean> = {}
    if (validationsData && validationsData.results && Array.isArray(validationsData.results)) {
      validationsData.results.forEach((validation: { field_key: string; is_valid: boolean }) => {
        validationMap[validation.field_key] = validation.is_valid
      })
    } else {
      // Fallback if we can't get the validations
      validationMap[fieldKey] = isValid
    }

    return NextResponse.json({
      success: true,
      id,
      fieldKey,
      isValid,
      fieldValidations: validationMap,
      deprecated: "This endpoint is deprecated. Please use /api/logs/[logId]/validations instead."
    })
  } catch (error: any) {
    logger.error(`Error in POST /api/logs/validate: ${error.message}`)
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    )
  }
}
