import { NextResponse } from "next/server"
import logger from "../../../../../lib/logger"

// Get backend host from environment variables
const backendHost = process.env.BACKEND_HOST
const apiKey = process.env.BACKEND_API_KEY

if (!backendHost) {
  logger.error("Backend host not configured")
  throw new Error("Backend host configuration missing")
}

if (!apiKey) {
  logger.error("Backend API key not configured")
  throw new Error("Backend API key configuration missing")
}

export async function GET(
  request: Request,
  context: { params: { logId: string } }
) {
  try {
    // Get the logId from context - using await to satisfy Next.js requirements
    const params = await context.params;
    const logId = params.logId;

    if (!logId) {
      return NextResponse.json(
        { error: "Missing required parameter: logId" },
        { status: 400 }
      )
    }

    logger.info({ logId }, "Fetching validations for log")

    // Fetch validations from the backend service
    const response = await fetch(
      `${backendHost}/documents/processing-logs/${logId}/validations`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": apiKey as string
        },
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      logger.error(`Error fetching validations: ${errorText}`)
      return NextResponse.json(
        { error: "Failed to fetch validations" },
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error: any) {
    const params = await context.params;
    logger.error(`Error in GET /api/logs/${params.logId}/validations: ${error.message}`)
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    )
  }
}

export async function POST(
  request: Request,
  context: { params: { logId: string } }
) {
  try {
    // Get the logId from context - using await to satisfy Next.js requirements
    const params = await context.params;
    const logId = params.logId;
    const { field_key, is_valid } = await request.json()

    if (!logId) {
      return NextResponse.json(
        { error: "Missing required parameter: logId" },
        { status: 400 }
      )
    }

    if (!field_key) {
      return NextResponse.json(
        { error: "Missing required parameter: field_key" },
        { status: 400 }
      )
    }

    if (typeof is_valid !== 'boolean') {
      return NextResponse.json(
        { error: "is_valid must be a boolean value" },
        { status: 400 }
      )
    }

    logger.info({ logId, field_key, is_valid }, "Creating/updating field validation")

    // Log the full request details for debugging
    const requestBody = JSON.stringify({
      field_key,
      is_valid
    });

    logger.info({
      url: `${backendHost}/documents/processing-logs/${logId}/validations`,
      method: "POST",
      body: requestBody
    }, "Validation request details");

    // Create or update validation using the backend service
    const response = await fetch(
      `${backendHost}/documents/processing-logs/${logId}/validations`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": apiKey as string
        },
        body: requestBody
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      logger.error({
        status: response.status,
        statusText: response.statusText,
        errorText,
        logId,
        field_key,
        is_valid
      }, "Error updating validation")

      return NextResponse.json(
        {
          error: "Failed to update validation",
          details: errorText,
          status: response.status,
          field_key
        },
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error: any) {
    const params = await context.params;
    logger.error(`Error in POST /api/logs/${params.logId}/validations: ${error.message}`)
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    )
  }
}
