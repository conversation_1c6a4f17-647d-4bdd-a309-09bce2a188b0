import { NextResponse } from "next/server"
import logger from "../../../lib/logger"

// Get backend host from environment variables
const backendHost = process.env.BACKEND_HOST
const apiKey = process.env.BACKEND_API_KEY

if (!backendHost) {
  logger.error("Backend host not configured")
  throw new Error("Backend host configuration missing")
}

if (!apiKey) {
  logger.error("Backend API key not configured")
  throw new Error("Backend API key configuration missing")
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const page = searchParams.get("page") || "1"
    const pageSize = searchParams.get("page_size") || "10"

    // Fetch logs from the backend service
    const response = await fetch(
      `${backendHost}/documents/processing-logs?page=${page}&page_size=${pageSize}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": api<PERSON><PERSON> as string
        },
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      logger.error(`Error fetching logs: ${errorText}`)
      return NextResponse.json(
        { error: "Failed to fetch logs" },
        { status: response.status }
      )
    }

    const data = await response.json()

    // Process the logs to add field_validations based on validation_results
    const processedLogs = (data.items || []).map((log: any) => {
      // If the log already has field_validations, use it
      if (log.field_validations) {
        logger.debug({ logId: log.id }, "Log already has field_validations")
        return log
      }

      // If the log has validation_results, convert it to field_validations format
      if (log.validation_results && Array.isArray(log.validation_results) && log.validation_results.length > 0) {
        const fieldValidations: Record<string, boolean> = {}

        log.validation_results.forEach((validation: { field_key: string; is_valid: boolean }) => {
          fieldValidations[validation.field_key] = validation.is_valid
        })

        logger.info(
          {
            logId: log.id,
            validationCount: log.validation_results.length,
            correctCount: Object.values(fieldValidations).filter(v => v === true).length,
            incorrectCount: Object.values(fieldValidations).filter(v => v === false).length
          },
          "Converted validation_results to field_validations"
        )

        return {
          ...log,
          field_validations: fieldValidations
        }
      }

      // If no validation data, return the log as is
      logger.debug({ logId: log.id }, "Log has no validation data")
      return log
    })

    // Extract pagination metadata from the response
    const paginationData = {
      page: data.page || parseInt(page),
      page_size: data.page_size || parseInt(pageSize),
      pages: data.pages || 1,
      total: data.total || processedLogs.length
    }

    logger.debug({ pagination: paginationData }, "Pagination data from backend")

    // Return both the logs and pagination data
    return NextResponse.json({
      items: processedLogs,
      pagination: paginationData
    })
  } catch (error: any) {
    logger.error(`Error in GET /api/logs: ${error.message}`)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

/**
 * @note This endpoint acts as a proxy to the backend host endpoint:
 * DELETE backend_host/documents/processing-logs/{log_id}
 *
 * It's used to avoid CORS issues when calling the backend directly from the client.
 * In a production environment with proper CORS configuration, direct calls to the backend
 * would be possible.
 */
export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url)
  const id = searchParams.get("id")

  if (!id) {
    return NextResponse.json(
      { error: "Missing required parameter: id" },
      { status: 400 }
    )
  }

  logger.info(
    { id },
    "Proxying delete request to backend endpoint"
  )

  try {
    // Delete the log record from the backend
    const response = await fetch(`${backendHost}/documents/processing-logs/${id}`, {
      method: "DELETE",
      headers: {
        "X-Api-Key": apiKey as string
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      logger.error(`Error deleting log: ${errorText}`)
      return NextResponse.json(
        { error: "Failed to delete log record" },
        { status: response.status }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error: any) {
    logger.error(`Error in DELETE /api/logs: ${error.message}`)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
