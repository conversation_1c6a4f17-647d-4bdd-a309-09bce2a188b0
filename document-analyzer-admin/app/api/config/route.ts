import { NextResponse } from "next/server"

// Get backend host from environment variables
const backendHost = process.env.BACKEND_HOST
const apiKey = process.env.BACKEND_API_KEY

if (!backendHost) {
  console.error("Backend host not configured")
  throw new Error("Backend host configuration missing")
}

if (!apiKey) {
  console.error("Backend API key not configured")
  throw new Error("Backend API key configuration missing")
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const documentType = searchParams.get("document_type")

    // Build the URL with document_type parameter if provided
    let url = `${backendHost}/llm-configurations/latest`
    if (documentType) {
      url += `?document_type=${encodeURIComponent(documentType)}`
    }

    // Fetch the latest configuration from internal service
    const response = await fetch(url, {
      headers: {
        'X-Api-Key': apiKey as string
      }
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      console.error("Error fetching latest configuration:", errorData)
      return NextResponse.json(
        {
          error: "Failed to fetch configuration",
          details: errorData.message || response.statusText,
        },
        { status: response.status },
      )
    }

    const data = await response.json()

    if (!data) {
      // No configurations found
      return NextResponse.json({ message: "No configurations found" }, { status: 404 })
    }

    return NextResponse.json(data)
  } catch (error: any) {
    console.error("Server error fetching configuration:", error)
    return NextResponse.json({ error: `Internal server error: ${error.message}` }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const configData = await request.json()

    // Ensure model_id is included in the data
    const dataToInsert = {
      ...configData,
      model_id: configData.model_id || "gemini-2.0-flash", // Default to gemini-2.0-flash if not provided
      provider: configData.provider || "google", // Default provider if not provided
      document_type: configData.document_type || null, // Include document_type if provided
    }

    // Parse structured_output_schema if it's a string
    if (typeof dataToInsert.structured_output_schema === 'string' && dataToInsert.structured_output_schema.trim()) {
      try {
        dataToInsert.structured_output_schema = JSON.parse(dataToInsert.structured_output_schema);
      } catch (e) {
        console.error("Error parsing structured_output_schema:", e);
        return NextResponse.json(
          { error: "Invalid structured_output_schema format" },
          { status: 400 }
        );
      }
    }

    const response = await fetch(`${backendHost}/llm-configurations/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Api-Key': apiKey as string
      },
      body: JSON.stringify(dataToInsert),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      console.error("Error saving configuration:", errorData)
      return NextResponse.json(
        {
          error: "Failed to save configuration",
          details: errorData.message || response.statusText
        },
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json(data, { status: 201 })
  } catch (error: any) {
    console.error("Server error saving configuration:", error)
    return NextResponse.json({ error: `Internal server error: ${error.message}` }, { status: 500 })
  }
}
