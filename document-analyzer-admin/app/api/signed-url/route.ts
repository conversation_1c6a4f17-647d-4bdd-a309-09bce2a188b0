import { type NextRequest, NextResponse } from "next/server"
import logger from "../../../lib/logger"

// Get backend host from environment variables
const backendHost = process.env.BACKEND_HOST
const apiKey = process.env.BACKEND_API_KEY

if (!backendHost) {
  logger.error("Backend host not configured")
  throw new Error("Backend host configuration missing")
}

if (!apiKey) {
  logger.error("Backend API key not configured")
  throw new Error("Backend API key configuration missing")
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { filePath, expiresIn = 3600 } = body // Default expiry is 1 hour

    if (!filePath) {
      return NextResponse.json({ error: "File path is required" }, { status: 400 })
    }

    // Create a signed URL for the file using the backend service
    const response = await fetch(`${backendHost}/documents/signed-url`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": api<PERSON>ey as string
      },
      body: JSON.stringify({
        file_path: filePath,
        expires_in: expiresIn
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      logger.error(`Error creating signed URL: ${errorText}`)
      return NextResponse.json(
        { error: "Failed to create signed URL" },
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json({ signedURL: data.signed_url })
  } catch (error: any) {
    logger.error(`Error in signed URL API: ${error.message}`)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}
