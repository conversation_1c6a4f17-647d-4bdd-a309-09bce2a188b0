import { NextRequest, NextResponse } from 'next/server';
import { getServiceSupabase } from '@/lib/supabase';
import logger from '@/lib/logger';

// List of allowed email domains
// This could be moved to a database table for more flexibility
const ALLOWED_DOMAINS = [
  'happyfresh.com'
  // Add more domains as needed
];

export async function GET(request: NextRequest) {
  try {
    // In a real application, you might want to fetch this from a database
    // For now, we'll return the hardcoded list
    return NextResponse.json({ 
      domains: ALLOWED_DOMAINS,
      message: 'Only users with these email domains can sign up'
    });
  } catch (error) {
    logger.error('Error fetching allowed domains:', error);
    return NextResponse.json(
      { error: 'Failed to fetch allowed domains' },
      { status: 500 }
    );
  }
}

// Function to check if an email domain is allowed
export function isAllowedDomain(email: string): boolean {
  if (!email) return false;
  
  const domain = email.split('@')[1]?.toLowerCase();
  if (!domain) return false;
  
  return ALLOWED_DOMAINS.includes(domain);
}
