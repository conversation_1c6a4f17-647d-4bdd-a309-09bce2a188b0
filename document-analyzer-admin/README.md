# Invoice Data Extraction - Admin Frontend

A Next.js admin interface for the Invoice Data Extraction system. This repository contains only the admin frontend; the backend service is in a separate [repository](https://gitlab.happyfresh.net/hf/tpd/rainmakers/document-analyzer).

## Overview

This admin interface allows users to:
- Upload invoice PDFs,
- <PERSON> automatically extracted data, and
- Manually validate the results to assess extraction accuracy.

The frontend communicates with a dedicated backend service that handles data extraction, processing, and storage.

## Features

- PDF invoice upload and submission to backend
- Data extraction visualization
- Admin configuration panel for extraction settings
- Results displayed in table and JSON formats
- PDF preview with fullscreen capability
- Processing history and logs
- Color analysis for uploaded documents
- User authentication with Google OAuth
- Domain-restricted signup for secure access

## Tech Stack

- **Framework**: Next.js 15, React 19, TypeScript
- **UI Components**: Shadcn UI, Radix UI, Tailwind CSS
- **State Management**: React Hooks
- **API Integration**: Next.js API routes (proxying to backend)

## Getting Started

### Prerequisites

- Node.js 18+ and pnpm 10+
- [Backend service](https://gitlab.happyfresh.net/hf/tpd/rainmakers/document-analyzer) running

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   pnpm install
   ```
3. Create a `.env.local` file with the following variables:
   ```
   # Backend configuration
   NEXT_PUBLIC_BACKEND_HOST=http://localhost:8000
   BACKEND_HOST=http://localhost:8000
   BACKEND_API_KEY=your_api_key

   # Supabase authentication
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_key
   NEXT_PUBLIC_AUTH_REDIRECT_URL=http://localhost:3000/auth/callback
   ```

### Development

Run the development server:

```
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

### Build

```
pnpm build
pnpm start
```

## Project Structure

- `/app`: Next.js app router pages and API routes
  - `/admin`: Admin dashboard and settings pages
  - `/api`: API routes that proxy requests to the backend
- `/components`: React components including admin dashboard and result display
- `/lib`: Utility functions and logger
- `/config`: Configuration files for models
- `/types`: TypeScript type definitions

## Backend Integration

This admin interface communicates with a separate backend service that handles:
- Document processing and storage
- AI model integration with Google Vertex AI
- Database operations
- File storage and retrieval

Make sure the backend service is running and properly configured in the environment variables.

## Authentication

The application uses Supabase Authentication with Google OAuth for user login:

1. Only users with approved email domains can sign up (configurable in the auth settings)
2. Authentication state is managed through Supabase Auth client using localStorage
3. Protected routes require authentication via client-side checks
4. User profile information is displayed in the sidebar
5. All authenticated users have admin access since signup is disabled and users are created manually

### Authentication Flow

1. Users are redirected to Google OAuth for authentication
2. After successful authentication, Supabase creates or updates the user session
3. The session is stored in localStorage (not cookies)
4. Client-side components check authentication state using the Supabase Auth client
5. The `ProtectedRoute` component ensures only authenticated users can access protected pages

### Setting Up Authentication

1. Create a Supabase project and configure Google OAuth:
   - Go to your Supabase project dashboard
   - Navigate to Authentication > Providers > Google
   - Enable Google OAuth and add your Google Client ID and Secret
   - Set the authorized domains to include your application domain

2. Configure redirect URLs:
   - Add your application's callback URL to the allowed redirect URLs in Supabase
   - Format: `https://your-domain.com/auth/callback`
   - For local development: `http://localhost:3000/auth/callback`

3. Configure domain restrictions:
   - Edit the allowed domains in `app/api/auth/allowed-domains/route.ts`
   - Default allowed domain is `happyfresh.com`
   - Only users with these email domains can sign in

4. Environment variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   NEXT_PUBLIC_AUTH_REDIRECT_URL=https://your-domain.com/auth/callback
   ```

### Debugging Authentication

If you encounter authentication issues:

1. Visit the `/auth-debug` page to see detailed information about your authentication state
2. Check browser console logs for authentication-related messages
3. Ensure your Supabase project is properly configured
4. Verify that the allowed email domains match your users' email domains

## License

[Your license information here]