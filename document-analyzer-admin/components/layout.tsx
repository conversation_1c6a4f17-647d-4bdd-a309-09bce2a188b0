"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronLeft, ChevronRight, FileText, Home, LogOut, Menu, Settings, User, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/contexts/auth-context"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"

export default function Layout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const pathname = usePathname()
  const { user, signOut } = useAuth()
  const { toast } = useToast()

  // Load sidebar state from localStorage on component mount
  useEffect(() => {
    const savedState = localStorage.getItem("sidebarCollapsed")
    if (savedState !== null) {
      setSidebarCollapsed(savedState === "true")
    }
  }, [])

  // Save sidebar state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem("sidebarCollapsed", String(sidebarCollapsed))
  }, [sidebarCollapsed])

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const toggleCollapse = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const navItems = [
    {
      name: "Dashboard",
      href: "/admin",
      icon: Home,
    },
    {
      name: "History",
      href: "/admin/history",
      icon: FileText,
    },
    {
      name: "Settings",
      href: "/admin/settings",
      icon: Settings,
    },
  ]

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Mobile sidebar toggle */}
      <div className="fixed top-4 left-4 z-50 md:hidden">
        <Button variant="outline" size="icon" onClick={toggleSidebar}>
          <Menu className="h-5 w-5" />
        </Button>
      </div>

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 transform bg-white shadow-lg transition-all duration-300 ease-in-out md:relative md:shrink-0",
          sidebarOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0",
          sidebarCollapsed ? "w-16 md:w-16" : "w-64",
        )}
      >
        <div className="flex h-full flex-col">
          <div className={cn("flex items-center border-b px-4 py-6", sidebarCollapsed ? "justify-end" : "justify-between")}>
            <h2
              className={cn(
                "text-xl font-bold transition-opacity duration-200",
                sidebarCollapsed ? "opacity-0" : "opacity-100",
              )}
            >
              Invoice Extraction
            </h2>
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleCollapse}
                className="hidden md:flex"
                aria-label={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
              >
                {sidebarCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
              </Button>
              <Button variant="ghost" size="icon" onClick={toggleSidebar} className="md:hidden">
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>

          <nav className="flex-1 space-y-1 px-2 py-4">
            {navItems.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
                    isActive ? "bg-gray-100 text-gray-900" : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                  )}
                  title={sidebarCollapsed ? item.name : undefined}
                >
                  <item.icon className={cn("h-5 w-5", isActive ? "text-gray-900" : "text-gray-400")} />
                  <span
                    className={cn(
                      "ml-3 transition-all duration-200",
                      sidebarCollapsed ? "opacity-0 w-0 overflow-hidden" : "opacity-100 w-auto",
                    )}
                  >
                    {item.name}
                  </span>
                </Link>
              )
            })}

            {/* User profile section */}
            <div className="mt-auto pt-4">
              <Separator className="my-2" />
              {user && (
                <>
                  <div className={cn(
                    "flex items-center px-3 py-2",
                    sidebarCollapsed ? "justify-center" : "justify-start"
                  )}>
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.user_metadata.avatar_url} alt={user.user_metadata.name || user.email} />
                      <AvatarFallback>{user.email?.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div className={cn(
                      "ml-3 transition-all duration-200",
                      sidebarCollapsed ? "opacity-0 w-0 overflow-hidden" : "opacity-100 w-auto",
                    )}>
                      <p className="text-sm font-medium">{user.user_metadata.name || user.email}</p>
                      <p className="text-xs text-gray-500 truncate">{user.email}</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-start text-gray-600 hover:bg-gray-50 hover:text-gray-900 mt-2",
                      sidebarCollapsed && "justify-center px-0"
                    )}
                    onClick={async () => {
                      try {
                        await signOut();
                        toast({
                          title: "Signed out successfully",
                          description: "You have been signed out of your account",
                        });
                      } catch (error) {
                        console.error("Error signing out:", error);
                        toast({
                          title: "Error signing out",
                          description: "There was a problem signing out. Please try again.",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <LogOut className={cn("h-5 w-5", "text-gray-400")} />
                    <span
                      className={cn(
                        "ml-3 transition-all duration-200",
                        sidebarCollapsed ? "opacity-0 w-0 overflow-hidden" : "opacity-100 w-auto",
                      )}
                    >
                      Sign Out
                    </span>
                  </Button>
                </>
              )}
            </div>
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-1 flex-col overflow-hidden transition-all duration-300">
        <main className="flex-1 overflow-y-auto p-4 md:p-6">{children}</main>
      </div>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-30 bg-gray-600 bg-opacity-50 md:hidden" onClick={toggleSidebar}></div>
      )}
    </div>
  )
}
