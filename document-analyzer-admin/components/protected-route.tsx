"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { Loader2 } from "lucide-react";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  // Set isClient to true when component mounts (client-side only)
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Check authentication on client side
  useEffect(() => {
    // Only run this check on the client side
    if (isClient && !isLoading) {
      console.log("Protected route check - User:", !!user);

      if (!user) {
        console.log("No user found, redirecting to login");
        router.push("/login");
      } else {
        console.log("User authenticated, allowing access to protected route");
      }
    }
  }, [user, isLoading, router, isClient]);

  // Show loading state while checking authentication
  if (isLoading || !isClient) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <p className="text-sm text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  // If not authenticated and on client side, don't render children
  if (!user && isClient) {
    return null;
  }

  // If authenticated, render children
  return <>{children}</>;
}
