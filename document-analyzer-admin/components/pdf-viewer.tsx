"use client"

import { useRef } from "react"
import { Button } from "@/components/ui/button"
import { Maximize, Minimize } from "lucide-react"

interface PdfViewerProps {
  fileUrl: string
  isFullscreen: boolean
  onToggleFullscreen: () => void
}

export default function PdfViewer({ fileUrl, isFullscreen, onToggleFullscreen }: PdfViewerProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  return (
    <div className="flex flex-col h-full relative">
      <div className="absolute top-2 right-2 z-10">
        <Button variant="outline" size="sm" onClick={onToggleFullscreen} className="bg-white">
          {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
        </Button>
      </div>
      <div ref={containerRef} className="flex-grow overflow-auto bg-gray-100">
        <embed
          src={fileUrl}
          type="application/pdf"
          className="w-full h-full"
          style={{
            minHeight: isFullscreen ? "calc(100vh - 64px)" : "calc(100vh - 300px)",
          }}
        />
      </div>
    </div>
  )
}
