"use client"


import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Download, FileText } from "lucide-react"
import { getBlobName, getFilenameFromBlobName } from "@/lib/blob-utils"

interface PdfFallbackProps {
  fileUrl: string
  blobName?: string
}

export default function PdfFallback({ fileUrl, blobName }: PdfFallbackProps) {
  // Get the backend host from environment variables or window.ENV
  // Make sure we have a valid backend host URL without trailing slash
  const backendHost = typeof window !== 'undefined' && window.ENV?.NEXT_PUBLIC_BACKEND_HOST
    ? window.ENV.NEXT_PUBLIC_BACKEND_HOST.replace(/\/$/, "")
    : (process.env.NEXT_PUBLIC_BACKEND_HOST || process.env.BACKEND_HOST || "http://localhost:8000").replace(/\/$/, "")

  // Get the API key from environment variables
  const apiKey = process.env.BACKEND_API_KEY

  const downloadPdf = () => {
    // Make sure we have a valid backend host
    if (!backendHost) {
      console.error("Backend host is not defined")
      alert("Cannot download PDF: Backend host is not configured")
      return
    }

    if (!apiKey) {
      console.error("API key is not defined")
      alert("Cannot download PDF: API key is not configured")
      return
    }

    const blob = blobName || getBlobName(fileUrl)
    const downloadUrl = `${backendHost}/documents/processing-logs/download-file?blob_name=${blob}`

    const link = document.createElement("a")
    link.href = downloadUrl
    // Use the utility function to get just the filename
    const fileName = getFilenameFromBlobName(blob)
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex flex-col items-center justify-center gap-4">
          <FileText className="h-16 w-16 text-gray-400" />
          <div className="text-center">
            <h3 className="text-lg font-medium">PDF Preview Unavailable</h3>
            <p className="text-sm text-gray-500 mt-1">The PDF viewer couldn't be loaded in this environment.</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={downloadPdf} className="flex items-center gap-1">
              <Download className="h-4 w-4" />
              Download PDF
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
