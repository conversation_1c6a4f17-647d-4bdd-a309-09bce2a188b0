"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Copy } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface ResultField {
  value: string | number | boolean
}

interface ResultData {
  vendor_name: ResultField
  invoice_date: ResultField
  invoice_number: ResultField
  tax_invoice_number: ResultField
  total_amount_idr: ResultField
  total_ppn_idr: ResultField
  is_bw: ResultField
  document_type: ResultField
  [key: string]: ResultField
}

interface ColorAnalysis {
  colored_pages: number[]
  full_analysis?: any
}

interface ResultDisplayProps {
  result: ResultData
  colorAnalysis?: ColorAnalysis
}

export default function ResultDisplay({ result, colorAnalysis }: ResultDisplayProps) {
  const [copied, setCopied] = useState(false)

  const copyToClipboard = () => {
    navigator.clipboard.writeText(JSON.stringify(result, null, 2))
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Extraction Results</CardTitle>
        <Button variant="outline" size="sm" onClick={copyToClipboard} className="flex items-center gap-1">
          <Copy className="h-4 w-4" />
          {copied ? "Copied!" : "Copy JSON"}
        </Button>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="table">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="table">Table View</TabsTrigger>
            <TabsTrigger value="json">JSON View</TabsTrigger>
          </TabsList>

          <TabsContent value="table" className="pt-4">
            <div className="rounded-md border">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b bg-gray-50">
                    <th className="px-4 py-3 text-left font-medium">Field</th>
                    <th className="px-4 py-3 text-left font-medium">Value</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(result).map(([key, field]) => (
                    <tr key={key} className="border-b">
                      <td className="px-4 py-3 align-top font-medium">{key.replace(/_/g, " ")}</td>
                      <td className="px-4 py-3 align-top">
                        {typeof field.value === "boolean" ? field.value.toString() : field.value}
                      </td>
                    </tr>
                  ))}
                  {colorAnalysis && colorAnalysis.colored_pages && colorAnalysis.colored_pages.length > 0 && (
                    <tr className="border-b">
                      <td className="px-4 py-3 align-top font-medium">Colored Pages</td>
                      <td className="px-4 py-3 align-top">
                        {colorAnalysis.colored_pages.join(", ")}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </TabsContent>

          <TabsContent value="json" className="pt-4">
            <pre className="bg-gray-50 p-4 rounded-md overflow-auto text-xs">{JSON.stringify(result, null, 2)}</pre>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
