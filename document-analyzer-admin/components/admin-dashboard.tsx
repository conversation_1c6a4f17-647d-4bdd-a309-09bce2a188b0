"use client"

import type React from "react"

import { FileText, Loader2, Upload } from "lucide-react"
import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import ResultPage from "@/components/result-page"

export default function AdminDashboard() {
  const { toast } = useToast()
  const [file, setFile] = useState<File | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [extractedFilePath, setExtractedFilePath] = useState<string | null>(null)
  const [showResults, setShowResults] = useState(false)
  const [usageMetadata, setUsageMetadata] = useState<any>(null)
  const [logId, setLogId] = useState<string | null>(null)
  const [blobName, setBlobName] = useState<string | null>(null)
  const [colorAnalysis, setColorAnalysis] = useState<any>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0]
      setFile(selectedFile)
      setError(null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!file) {
      setError("Please select a file to upload")
      return
    }

    setLoading(true)
    setError(null)

    try {
      const formData = new FormData()
      formData.append("file", file)

      const response = await fetch("/api/extract", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to extract data")
      }

      const data = await response.json()
      setResult(data.result)
      setUsageMetadata(data.usageMetadata)
      setExtractedFilePath(data.filePath)
      setBlobName(data.blob_name || null)

      // Set color analysis data if available
      if (data.colorAnalysis) {
        setColorAnalysis(data.colorAnalysis)
      } else {
        setColorAnalysis(null)
      }

      // Make sure we get the logId from the response
      if (data.logId) {
        setLogId(String(data.logId)) // Ensure it's a string
      } else {
        setLogId(null)
      }

      setShowResults(true)
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred during extraction")
      toast({
        title: "Extraction Failed",
        description: error instanceof Error ? error.message : "An error occurred during extraction",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFieldValidationChange = (fieldKey: string, isValid: boolean) => {
    // No need to update local state as we're not displaying a list
  }

  if (showResults && result) {
    return (
      <ResultPage
        result={result}
        filePath={extractedFilePath!}
        blob_name={blobName || undefined}
        usageMetadata={usageMetadata ?? undefined}
        logId={logId || undefined}
        colorAnalysis={colorAnalysis ?? undefined}
        onFieldValidationChange={handleFieldValidationChange}
        onBack={() => {
          setShowResults(false)
          setLoading(false)
          setFile(null)
          setResult(null)
          setUsageMetadata(null)
          setExtractedFilePath(null)
          setBlobName(null)
          setLogId(null)
          setColorAnalysis(null)
        }}
      />
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Upload Invoice</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="file">Upload PDF Invoice</Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Input id="file" type="file" accept="application/pdf" onChange={handleFileChange} className="hidden" />
              <Label htmlFor="file" className="cursor-pointer flex flex-col items-center justify-center">
                {file ? (
                  <>
                    <FileText className="h-10 w-10 text-gray-400 mb-2" />
                    <span className="text-sm font-medium">{file.name}</span>
                    <span className="text-xs text-gray-500 mt-1">{(file.size / 1024).toFixed(2)} KB</span>
                  </>
                ) : (
                  <>
                    <Upload className="h-10 w-10 text-gray-400 mb-2" />
                    <span className="text-sm font-medium">Click to upload or drag and drop</span>
                    <span className="text-xs text-gray-500 mt-1">PDF (max. 10MB)</span>
                  </>
                )}
              </Label>
            </div>
          </div>

          {error && <div className="bg-red-50 text-red-600 p-3 rounded-md text-sm">{error}</div>}

          <Button type="submit" disabled={loading} className="w-full">
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              "Extract Data"
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
