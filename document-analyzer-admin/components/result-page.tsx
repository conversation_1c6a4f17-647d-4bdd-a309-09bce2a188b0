"use client"
import { useState, useEffect, use<PERSON>allback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Arrow<PERSON>eft, Copy, Download } from "lucide-react"
import PdfViewer from "@/components/pdf-viewer"
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable"
import { getBlobName, getFilenameFromBlobName } from "@/lib/blob-utils"

interface ResultField {
  value: string | number | boolean
}

// Old format with value property
interface OldResultData {
  vendor_name: ResultField
  invoice_date: ResultField
  invoice_number: ResultField
  tax_invoice_number: ResultField
  total_amount_idr: ResultField
  total_ppn_idr: ResultField
  is_bw: ResultField
  document_type: Result<PERSON><PERSON>
  [key: string]: ResultField
}

// New format with direct values
interface NewResultData {
  vendor_name: string
  invoice_date: string
  invoice_number: string
  tax_invoice_number: string
  total_amount: number
  total_ppn: number
  color_pages: number[]
  document_type: string
  gs_uri: string
  total_pages: number
  [key: string]: any
}

// Combined type to handle both formats
type ResultData = OldResultData | NewResultData

interface UsageMetadata {
  promptTokenCount: number
  candidatesTokenCount: number
  totalTokenCount: number
  model: string
}

interface ColorAnalysis {
  colored_pages: number[]
  full_analysis?: any
}

interface ResultPageProps {
  result: ResultData
  filePath: string  // This is typically the gs_uri
  onBack: () => void
  usageMetadata?: UsageMetadata
  logId?: string
  fieldValidations?: Record<string, boolean>
  onFieldValidationChange?: (fieldKey: string, isValid: boolean) => void
  colorAnalysis?: ColorAnalysis
  blob_name?: string  // Direct blob_name from log data
}

import { formatNumber } from "@/lib/utils"
import { CheckCircle, XCircle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export default function ResultPage({
  result,
  filePath,
  usageMetadata,
  onBack,
  logId,
  fieldValidations = {},
  onFieldValidationChange,
  colorAnalysis,
  blob_name
}: ResultPageProps) {
  const [fullscreen, setFullscreen] = useState<boolean>(false)
  const [copied, setCopied] = useState<boolean>(false)
  const [highlightedField, setHighlightedField] = useState<string | null>(null)
  const [signedUrl, setSignedUrl] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [validationStatuses, setValidationStatuses] = useState<Record<string, boolean>>(fieldValidations)
  const [submittingFields, setSubmittingFields] = useState<Record<string, boolean>>({})
  const { toast } = useToast()

  // Get the backend host from environment variables or window.ENV
  // Make sure we have a valid backend host URL without trailing slash
  const backendHost = typeof window !== 'undefined' && window.ENV?.NEXT_PUBLIC_BACKEND_HOST
    ? window.ENV.NEXT_PUBLIC_BACKEND_HOST.replace(/\/$/, "")
    : (process.env.NEXT_PUBLIC_BACKEND_HOST || process.env.BACKEND_HOST || "http://localhost:8000").replace(/\/$/, "")

  // Get the API key from environment variables
  const apiKey = process.env.BACKEND_API_KEY

  // We're now using the imported getBlobName function from lib/blob-utils.ts

  // Function to fetch validations for the current log
  const fetchValidations = useCallback(async () => {
    if (!logId) {
      return;
    }

    try {
      const response = await fetch(`/api/logs/${logId}/validations`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || "Failed to fetch validations");
      }

      const data = await response.json();

      // Convert the validation results to the format expected by the component
      if (data && data.results && Array.isArray(data.results)) {
        const validationMap: Record<string, boolean> = {};
        data.results.forEach((validation: { field_key: string; is_valid: boolean }) => {
          validationMap[validation.field_key] = validation.is_valid;
        });
        setValidationStatuses(validationMap);
      }
    } catch (error) {
      // Error handling without logging
    }
  }, [logId]);

  // Fetch validations when the component loads
  useEffect(() => {
    if (logId) {
      fetchValidations();
    }
  }, [logId, fetchValidations]);

  // Set up URLs for viewing and downloading
  useEffect(() => {
    try {
      setIsLoading(true)
      if (!filePath) {
        throw new Error("Could not extract file path")
      }

      // Use the direct blob_name if available, otherwise extract it from filePath
      const blobName = getBlobName(filePath, blob_name)

      // Make sure we have a valid backend host
      if (!backendHost) {
        setSignedUrl(null)
        return
      }

      // No need to fetch a signed URL anymore, we'll use the direct endpoints
      const viewUrl = `${backendHost}/documents/processing-logs/view-file?blob_name=${blobName}`
      setSignedUrl(viewUrl)
    } catch (error) {
      setSignedUrl(null)
    } finally {
      setIsLoading(false)
    }
  }, [filePath, backendHost, blob_name]) // Depend on filePath, backendHost, and blob_name

  // We'll use direct URLs to the backend endpoints instead of a signed URL

  // Function to toggle fullscreen
  const toggleFullscreen = () => {
    setFullscreen(!fullscreen)
  }

  // Function to copy JSON to clipboard
  const copyToClipboard = () => {
    // Use normalized result for both formats to ensure consistency
    const jsonToCopy = normalizedResult && Object.keys(normalizedResult).length > 0
      ? normalizedResult
      : safeResult;

    try {
      navigator.clipboard.writeText(JSON.stringify(jsonToCopy, null, 2))
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      alert("Failed to copy to clipboard. See console for details.");
    }
  }

  // Function to download the PDF
  const downloadPdf = () => {
    // Make sure we have a valid backend host
    if (!backendHost) {
      console.error("Backend host is not defined")
      alert("Cannot download PDF: Backend host is not configured")
      return
    }

    if (!apiKey) {
      console.error("API key is not defined")
      alert("Cannot download PDF: API key is not configured")
      return
    }

    // Use the direct blob_name if available, otherwise extract it from filePath
    const blobName = getBlobName(filePath, blob_name)
    const downloadUrl = `${backendHost}/documents/processing-logs/download-file?blob_name=${blobName}`

    // Create a temporary link and trigger download
    const link = document.createElement("a")
    link.href = downloadUrl
    // Use the utility function to get just the filename
    const fileName = getFilenameFromBlobName(blobName)
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Function to view the PDF in a new tab
  const viewPdf = () => {
    // Make sure we have a valid backend host
    if (!backendHost) {
      console.error("Backend host is not defined")
      alert("Cannot view PDF: Backend host is not configured")
      return
    }

    if (!apiKey) {
      console.error("API key is not defined")
      alert("Cannot view PDF: API key is not configured")
      return
    }

    // Use the direct blob_name if available, otherwise extract it from filePath
    const blobName = getBlobName(filePath, blob_name)
    const viewUrl = `${backendHost}/documents/processing-logs/view-file?blob_name=${blobName}`
    window.open(viewUrl, "_blank")
  }

  // Function to update field validation status
  const updateFieldValidation = async (fieldKey: string, isValid: boolean) => {
    if (!logId) {
      return;
    }

    // Map display field keys to backend field keys
    let backendFieldKey = fieldKey;

    // Handle special cases for field key mapping
    // if (fieldKey === "total_amount_idr") {
    //   backendFieldKey = "total_amount";
    // } else if (fieldKey === "total_ppn_idr") {
    //   backendFieldKey = "total_ppn";
    // }

    try {
      // Mark this field as submitting
      setSubmittingFields(prev => ({ ...prev, [fieldKey]: true }))

      const response = await fetch(`/api/logs/${logId}/validations`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          field_key: backendFieldKey,
          is_valid: isValid,
        }),
      })

      if (!response.ok) {
        let errorMessage = "Failed to update validation status";
        try {
          const errorData = await response.json();
          errorMessage = errorData.details || errorData.error || errorMessage;
        } catch (parseError) {
          const errorText = await response.text();
          errorMessage = errorText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      // Update the local validation status
      setValidationStatuses(prev => ({
        ...prev,
        [fieldKey]: isValid
      }))

      if (onFieldValidationChange) {
        onFieldValidationChange(fieldKey, isValid)
      }

      toast({
        title: "Field validation updated",
        description: `Field "${fieldKey}" has been marked as ${isValid ? "correct" : "incorrect"}.`,
      })

      // Refresh validations to ensure we have the latest data
      fetchValidations()
    } catch (error) {
      toast({
        title: "Validation Error",
        description: error instanceof Error ? error.message : "Failed to update field validation status. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSubmittingFields(prev => ({ ...prev, [fieldKey]: false }))
    }
  }

  // Helper function to get validation status for a field
  const getFieldValidationStatus = (fieldKey: string): boolean | undefined => {
    return validationStatuses ? validationStatuses[fieldKey] : undefined
  }

  // Helper function to check if a field is currently being submitted
  const isFieldSubmitting = (fieldKey: string): boolean => {
    return submittingFields ? !!submittingFields[fieldKey] : false
  }

  // Helper function to find keys that contain certain terms
  const findKeysByTerm = (obj: any, term: string): string[] => {
    return Object.keys(obj).filter(key =>
      key.toLowerCase().includes(term.toLowerCase())
    );
  }

  // Helper function to check if we're dealing with the old format (with value property)
  const isOldFormat = (obj: any): obj is OldResultData => {
    if (!obj || typeof obj !== 'object') return false;

    // Check if vendor_name exists and has a value property
    if (obj.vendor_name && typeof obj.vendor_name === 'object' && obj.vendor_name !== null && 'value' in obj.vendor_name) {
      return true;
    }

    // Check if any key has a value property (fallback check)
    for (const key in obj) {
      if (obj[key] && typeof obj[key] === 'object' && obj[key] !== null && 'value' in obj[key]) {
        return true;
      }
    }

    return false;
  };

  // Helper function to get field value regardless of format
  const getFieldValue = (obj: any, key: string): any => {
    if (!obj || obj[key] === undefined || obj[key] === null) return null;

    // If it's the old format with value property
    if (typeof obj[key] === 'object' && obj[key] !== null && 'value' in obj[key]) {
      return obj[key].value;
    }

    // If it's the new format with direct values
    return obj[key];
  };

  // Make sure result is not null or undefined
  const safeResult = result || {};

  // Find keys for total amount and total ppn
  const totalAmountKeys = findKeysByTerm(safeResult, "total_amount");
  const totalPpnKeys = findKeysByTerm(safeResult, "total_ppn");

  // Use the first matching key if found, otherwise use the default key
  const totalAmountKey = totalAmountKeys.length > 0 ? totalAmountKeys[0] : "total_amount_idr";
  const totalPpnKey = totalPpnKeys.length > 0 ? totalPpnKeys[0] : "total_ppn_idr";

  // Create a normalized version of the result for display
  const normalizedResult: Record<string, any> = {};

  // Add all fields from the original result to the normalized result
  Object.keys(safeResult).forEach(key => {
    normalizedResult[key] = getFieldValue(safeResult, key);
  });

  // Add color_pages if they exist in colorAnalysis but not in the result
  if (colorAnalysis?.colored_pages && !normalizedResult.color_pages) {
    normalizedResult.color_pages = colorAnalysis.colored_pages;
  }

  const orderedKeys = [
    "vendor_name",
    "invoice_number",
    "tax_invoice_number",
    "invoice_date",
    totalAmountKey,  // Use the found key
    totalPpnKey,     // Use the found key
    "total_pages",
    "color_pages",   // Add color_pages to the ordered keys
    "document_type",
    "processing_time_ms",
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={onBack} className="flex items-center gap-1">
          <ArrowLeft className="h-4 w-4" />
          Back to Upload
        </Button>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={copyToClipboard} className="flex items-center gap-1">
            <Copy className="h-4 w-4" />
            {copied ? "Copied!" : "Copy JSON"}
          </Button>
          <Button variant="outline" size="sm" onClick={downloadPdf} className="flex items-center gap-1">
            <Download className="h-4 w-4" />
            Download PDF
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={viewPdf}
            className="flex items-center gap-1"
          >
            View PDF in New Tab
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-[calc(100vh-200px)] rounded-md border shadow-sm">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p>Loading PDF...</p>
          </div>
        </div>
      ) : fullscreen ? (
        <Card className="fixed inset-0 z-50 overflow-hidden bg-white">
          <CardContent className="h-full p-0">
            <PdfViewer fileUrl={signedUrl || ""} isFullscreen={true} onToggleFullscreen={toggleFullscreen} />
          </CardContent>
        </Card>
      ) : (
        <ResizablePanelGroup direction="horizontal" className="h-[calc(100vh-200px)] rounded-md border shadow-sm">
          <ResizablePanel defaultSize={50} minSize={20}>
            {/* PDF Viewer */}
            <div className="h-full overflow-hidden bg-white">
              <PdfViewer fileUrl={signedUrl || ""} isFullscreen={false} onToggleFullscreen={toggleFullscreen} />
            </div>
          </ResizablePanel>
          <ResizableHandle withHandle />
          <ResizablePanel defaultSize={50} minSize={20}>
            {/* Extraction Results */}
            <div className="h-full overflow-auto bg-white p-4">
              <div className="text-lg font-medium mb-4">Extraction Results</div>

              <Tabs defaultValue="table">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="table">Table View</TabsTrigger>
                  <TabsTrigger value="json">JSON View</TabsTrigger>
                </TabsList>

                <TabsContent value="table" className="pt-4">
                  <div className="rounded-md border">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b bg-gray-50">
                          <th className="px-4 py-3 text-left font-medium">Field</th>
                          <th className="px-4 py-3 text-left font-medium">Value</th>
                          {logId && <th className="px-4 py-3 text-left font-medium">Validation</th>}
                        </tr>
                      </thead>
                      <tbody>
                        {orderedKeys.map((key) => {
                          const value = normalizedResult[key];
                          if (value === undefined || value === null) return null; // Handle cases where a key might be missing

                          // Custom display names for specific fields
                          let displayName = key;
                          if (key === totalAmountKey) {
                            displayName = "total amount";
                          } else if (key === totalPpnKey) {
                            displayName = "total ppn";
                          } else if (key === "color_pages") {
                            displayName = "colored pages";
                          }

                          const fieldValidationStatus = getFieldValidationStatus(key);
                          const isSubmitting = isFieldSubmitting(key);

                          return (
                            <tr
                              key={key}
                              className={`border-b hover:bg-gray-50 cursor-pointer ${highlightedField === key ? "bg-blue-50" : ""}`}
                              onClick={() => setHighlightedField(highlightedField === key ? null : key)}
                            >
                              <td className="px-4 py-3 align-top font-medium">
                                {displayName.replace(/_/g, " ").split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                              </td>
                              <td className="px-4 py-3 align-top">
                                {value === null || value === undefined ? '' :
                                  typeof value === "boolean" ? value.toString() :
                                  key === totalAmountKey || key === totalPpnKey ?
                                    formatNumber(value) :
                                  Array.isArray(value) ? value.join(", ") :
                                  typeof value === "object" ? JSON.stringify(value) : value}
                              </td>
                              {logId && (
                                <td className="px-4 py-3 align-top">
                                  <div className="flex space-x-2">
                                    <Button
                                      variant={fieldValidationStatus === true ? "default" : "outline"}
                                      size="sm"
                                      className="h-8 px-2 flex items-center gap-1"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        updateFieldValidation(key, true);
                                      }}
                                      disabled={isSubmitting}
                                    >
                                      <CheckCircle className="h-3 w-3" />
                                      {fieldValidationStatus === true ? "Correct" : "✓"}
                                    </Button>
                                    <Button
                                      variant={fieldValidationStatus === false ? "destructive" : "outline"}
                                      size="sm"
                                      className="h-8 px-2 flex items-center gap-1"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        updateFieldValidation(key, false);
                                      }}
                                      disabled={isSubmitting}
                                    >
                                      <XCircle className="h-3 w-3" />
                                      {fieldValidationStatus === false ? "Incorrect" : "✗"}
                                    </Button>
                                  </div>
                                </td>
                              )}
                            </tr>
                          );
                        })}

                        {/* We no longer need to add color_pages separately since it's included in orderedKeys */}
                      </tbody>
                    </table>
                  </div>
                </TabsContent>

                <TabsContent value="json" className="pt-4">
                  {/* Adjusted height calculation for JSON view within resizable panel */}
                  <pre className="bg-gray-50 p-4 rounded-md overflow-auto text-xs h-[calc(100%-150px)]">
                    {JSON.stringify(
                      // Use normalized result for both formats to ensure consistency
                      normalizedResult && Object.keys(normalizedResult).length > 0
                        ? normalizedResult
                        : safeResult,
                      null, 2
                    )}
                  </pre>
                </TabsContent>
              </Tabs>

              {/* Field-level validation is now handled in the table */}

              {/* Usage Metadata Section */}
              {usageMetadata && (
                <div className="mt-6 pt-4 border-t">
                  <h3 className="text-md font-medium mb-3">Vertex AI Usage</h3>
                  <div className="space-y-1 text-sm">
                    <div className="flex">
                      <span>Input Tokens:</span>
                      <span className="font-mono ml-2">{usageMetadata.promptTokenCount}</span>
                    </div>
                    <div className="flex">
                      <span>Output Tokens:</span>
                      <span className="font-mono ml-2">{usageMetadata.candidatesTokenCount}</span>
                    </div>
                    <div className="flex">
                      <span>Model:</span>
                      <span className="font-mono ml-2">{usageMetadata.model}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      )}
    </div>
  )
}
