﻿using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

var client = new HttpClient();
var request = new HttpRequestMessage(HttpMethod.Post, "https://kim-staging.happyfresh.io/api/process-invoice-file");
// var request = new HttpRequestMessage(HttpMethod.Post, "https://3955-103-101-231-18.ngrok-free.app/api/process-invoice-file");
request.Headers.Add("X-API-Key", "ki_c718b1aeb9ba4705.f29245776484bae3b38a9d340a446a61424dc146124d42ebdc197766958053b7");

// string customBoundary = "--------------------------960074056336624809239814";


var content = new MultipartFormDataContent();
// content.Add(new StreamContent(File.OpenRead("/Users/<USER>/Desktop/Komatsu/test_files/invoice/air_liquide_invoice.pdf")), "file", "/Users/<USER>/Desktop/Komatsu/test_files/invoice/air_liquide_invoice.pdf");
content.Add(new StreamContent(File.OpenRead("/Users/<USER>/Desktop/Komatsu/test_files/invoice/air_liquide_invoice.pdf")), "file", "air_liquide_invoice.pdf");
content.Add(new StringContent("PT. AIR LIQUID INDONESIA"), "vendor_name");
content.Add(new StringContent("AL00235487"), "invoice_number");
content.Add(new StringContent("2024-10-17"), "invoice_date");
content.Add(new StringContent("5254740.00"), "invoice_amount");
content.Add(new StringContent("520700.00"), "vat_amount");
request.Content = content;
var response = await client.SendAsync(request);
response.EnsureSuccessStatusCode();
Console.WriteLine(await response.Content.ReadAsStringAsync());





// using System;
// using System.IO;
// using System.Net.Http;
// using System.Threading.Tasks;

// // Define a class to encapsulate the API client logic
// public class ApiClient
// {
//     public async Task SendInvoiceData()
//     {
//         // Ensure HttpClient is created once and reused if you make multiple calls in a real app,
//         // or dispose of it properly with 'using' if it's for a single call context like this example.
//         using (var client = new HttpClient())
//         // The HttpRequestMessage should also be within a using block or disposed
//         using (var request = new HttpRequestMessage(HttpMethod.Post, "https://3955-103-101-231-18.ngrok-free.app/api/process-invoice-file"))
//         {
//             request.Headers.Add("X-API-Key", "ki_28eea623ba7024ba.eef0d455cd49480bf32c5012e3f28a295258c08d3100ed6c677a548812aa4908");

//             // !!! IMPORTANT: Update this path to a valid PDF file on your system !!!
//             var filePath = "/Users/<USER>/Desktop/Komatsu/test_files/invoice/air_liquide_invoice.pdf"; // <--- CHANGE THIS
            
//             if (!File.Exists(filePath))
//             {
//                 Console.WriteLine($"Error: File not found at path: {filePath}");
//                 Console.WriteLine("Please update the filePath variable in the code.");
//                 return;
//             }

//             var fileName = Path.GetFileName(filePath);

//             // MultipartFormDataContent should be disposed
//             using (var multipartFormContent = new MultipartFormDataContent())
//             // FileStream should be disposed
//             using (var fileStream = File.OpenRead(filePath))
//             // StreamContent should be disposed
//             using (var streamContent = new StreamContent(fileStream))
//             {
//                 // Add the file stream content. The 'fileName' parameter here sets the
//                 // filename in the Content-Disposition header for the part.
//                 multipartFormContent.Add(streamContent, "file", fileName);

//                 // Add other string content (these will be form fields)
//                 multipartFormContent.Add(new StringContent("Parker Hannifin Indonesia, PT"), "vendor_name");
//                 multipartFormContent.Add(new StringContent("69657555"), "invoice_number");
//                 multipartFormContent.Add(new StringContent("2024-10-31"), "invoice_date");
//                 multipartFormContent.Add(new StringContent("6914140"), "invoice_amount");
//                 multipartFormContent.Add(new StringContent("594000"), "vat_amount");

//                 request.Content = multipartFormContent;

//                 Console.WriteLine($"Sending request to {request.RequestUri} with file: {fileName}");
//                 // Optional: Log the Content-Type header to verify it's multipart/form-data with a boundary
//                 // Console.WriteLine($"C# Request Content-Type: {request.Content.Headers.ContentType}");

//                 HttpResponseMessage response = null; // Declare response here to access it in catch
//                 try
//                 {
//                     response = await client.SendAsync(request);
//                     Console.WriteLine($"Response Status Code: {response.StatusCode}");
//                     response.EnsureSuccessStatusCode(); // Throws an exception for HTTP error codes (4xx, 5xx)
                    
//                     string responseBody = await response.Content.ReadAsStringAsync();
//                     Console.WriteLine("Response from server:");
//                     Console.WriteLine(responseBody);
//                 }
//                 catch (HttpRequestException e)
//                 {
//                     Console.WriteLine($"Request failed: {e.Message}");
//                     if (e.InnerException != null)
//                     {
//                         Console.WriteLine($"Inner exception: {e.InnerException.Message}");
//                     }
//                     // If the response is available even on failure, log its content
//                     if (response != null && response.Content != null)
//                     {
//                         string errorResponseBody = await response.Content.ReadAsStringAsync();
//                         Console.WriteLine($"Failure response content ({response.StatusCode}):");
//                         Console.WriteLine(errorResponseBody);
//                     }
//                     else if (response != null)
//                     {
//                          Console.WriteLine($"Failure response status code: {response.StatusCode}");
//                     }
//                 }
//                 catch (Exception ex) // Catch other potential errors like file access issues etc.
//                 {
//                     Console.WriteLine($"An unexpected error occurred: {ex.Message}");
//                 }
//             }
//         }
//     }
// }

// // Main program class
// public class Program
// {
//     // The Main method is the entry point of the program.
//     // It needs to be async if you want to use 'await' directly within it.
//     public static async Task Main(string[] args)
//     {
//         Console.WriteLine("Attempting to send invoice data...");

//         var apiClient = new ApiClient();
//         await apiClient.SendInvoiceData();

//         Console.WriteLine("\nProcess finished. Press any key to exit.");
//         Console.ReadKey();
//     }
// }





// using System;
// using System.IO;
// using System.Net.Http;
// using System.Net.Http.Headers; // Required for MediaTypeHeaderValue
// using System.Threading.Tasks;
// using System.Linq; // Required for .FirstOrDefault()

// public class Program
// {
//     public static async Task Main(string[] args)
//     {
//         using var client = new HttpClient();
//         // using var request = new HttpRequestMessage(HttpMethod.Post, "https://kim-staging.happyfresh.io/api/process-invoice-file");
//         using var request = new HttpRequestMessage(HttpMethod.Post, "https://3955-103-101-231-18.ngrok-free.app/api/process-invoice-file");

//         request.Headers.Add("X-API-Key", "ki_c718b1aeb9ba4705.f29245776484bae3b38a9d340a446a61424dc146124d42ebdc197766958053b7");

//         string customBoundary = "--------------------------960074056336624809239814";

//         using (var content = new MultipartFormDataContent(customBoundary))
//         {
//             // content.Add(new StreamContent(File.OpenRead("/Users/<USER>/Desktop/Komatsu/test_files/invoice/air_liquide_invoice.pdf")), "file", "air_liquide_invoice.pdf");
//             content.Add(new StringContent("PT. AIR LIQUID INDONESIA"), "vendor_name");
//             content.Add(new StringContent("AL00235487"), "invoice_number");
//             content.Add(new StringContent("2024-10-17"), "invoice_date");
//             content.Add(new StringContent("5254740.00"), "invoice_amount");
//             content.Add(new StringContent("520700.00"), "vat_amount");

//             // --- HACK STARTS HERE ---
//             // Remove the existing Content-Type header generated by MultipartFormDataContent
//             // This header will have the quotes around the boundary
//             content.Headers.Remove("Content-Type");

//             // Manually construct the Content-Type header string with the unquoted boundary
//             // We assume the customBoundary string itself does NOT contain quotes
//             string newContentTypeHeaderValue = $"multipart/form-data; boundary={customBoundary}";

//             // Add the modified Content-Type header using TryAddWithoutValidation
//             // This attempts to add the header without strict parsing validation.
//             // It's still possible for the underlying HTTP stack to re-add quotes
//             // if it deems it necessary for compliance.
//             if (!content.Headers.TryAddWithoutValidation("Content-Type", newContentTypeHeaderValue))
//             {
//                 Console.WriteLine("Warning: Failed to add Content-Type header without validation. It might still be quoted.");
//             }
//             // --- HACK ENDS HERE ---

//             request.Content = content;

//             var response = await client.SendAsync(request);

//             // Check if the request was successful
//             if (response.IsSuccessStatusCode)
//             {
//                 Console.WriteLine("Request successful!");
//                 Console.WriteLine(await response.Content.ReadAsStringAsync());
//             }
//             else
//             {
//                 Console.WriteLine($"Error: {response.StatusCode}");
//                 Console.WriteLine(await response.Content.ReadAsStringAsync()); // Read error details from server
//             }
//         }
//     }
// }




// var boundary = Guid.NewGuid().ToString(); // or any unique string
// var content = new MultipartFormDataContent(boundary);

// content.Add(new StreamContent(File.OpenRead("/Users/<USER>/Desktop/Komatsu/test_files/invoice/air_liquide_invoice.pdf")), "file", "parker.pdf");
// content.Add(new StringContent("Parker Hannifin Indonesia, PT"), "vendor_name");
// content.Add(new StringContent("69657555"), "invoice_number");
// content.Add(new StringContent("2024-10-31"), "invoice_date");
// content.Add(new StringContent("6914140"), "invoice_amount");
// content.Add(new StringContent("594000"), "vat_amount");

// // Remove and reset Content-Type header to ensure no quotes
// content.Headers.Remove("Content-Type");
// content.Headers.TryAddWithoutValidation("Content-Type", $"multipart/form-data; boundary={boundary}");

// var client = new HttpClient();
// var request = new HttpRequestMessage(HttpMethod.Post, "https://3955-103-101-231-18.ngrok-free.app/api/process-invoice-file");
// request.Headers.Add("X-API-Key", "ki_28eea623ba7024ba.eef0d455cd49480bf32c5012e3f28a295258c08d3100ed6c677a548812aa4908");
// request.Content = content;

// Console.WriteLine(content.Headers.ContentType); // For debugging
// var response = await client.SendAsync(request);
// response.EnsureSuccessStatusCode();
// Console.WriteLine(await response.Content.ReadAsStringAsync());



var client = new HttpClient();
var request = new HttpRequestMessage(HttpMethod.Post, "https://dona-api-staging.happyfresh.io/documents/invoice-file");
request.Headers.Add("accept", "application/json");
request.Headers.Add("X-API-Key", "m9mOq0v3Qtzx73iekKMLGjnNo7MpqGRN");
var content = new MultipartFormDataContent();
// content.Add(new StreamContent(File.OpenRead("/Users/<USER>/Desktop/Komatsu/test_files/invoice/air_liquide_invoice.pdf")), "file", "/Users/<USER>/Desktop/Komatsu/test_files/invoice/air_liquide_invoice.pdf");
content.Add(new StringContent("false"), "show_confidence");
content.Add(new StringContent("false"), "debug_timing");
request.Content = content;
var response = await client.SendAsync(request);
response.EnsureSuccessStatusCode();
Console.WriteLine(await response.Content.ReadAsStringAsync());




// var client = new HttpClient();
// var request = new HttpRequestMessage(HttpMethod.Post, "https://kim-staging.happyfresh.io/api/process-invoice-file");
// request.Headers.Add("X-API-Key", "ki_c718b1aeb9ba4705.f29245776484bae3b38a9d340a446a61424dc146124d42ebdc197766958053b7");
// var content = new MultipartFormDataContent();
// content.Add(new StringContent("AL00235487"), "invoice_number");
// request.Content = content;
// var response = await client.SendAsync(request);
// response.EnsureSuccessStatusCode();
// Console.WriteLine(await response.Content.ReadAsStringAsync());




