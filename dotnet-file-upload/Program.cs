﻿using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text; // For Encoding

public class ApiClient
{
    public static async Task Main(string[] args)
    {
        // Default to localhost if no argument is provided
        bool useLocalhost = true;

        if (args.Length > 0 && args[0].ToLower() == "staging")
        {
            useLocalhost = false;
            Console.WriteLine("=== Using STAGING environment ===");
        }
        else
        {
            Console.WriteLine("=== Using LOCALHOST environment ===");
        }

        var client = new HttpClient { Timeout = TimeSpan.FromMinutes(5) };
        var requestUrl = useLocalhost
            ? "http://localhost:3000/api/process-invoice-file"
            : "https://kim-staging.happyfresh.io/api/process-invoice-file";

        var request = new HttpRequestMessage(HttpMethod.Post, requestUrl);
        var apiKey = "ki_c718b1aeb9ba4705.f29245776484bae3b38a9d340a446a61424dc146124d42ebdc197766958053b7";
        request.Headers.Add("X-API-Key", apiKey);

        Console.WriteLine("=== Starting API Request ===");
        Console.WriteLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

        Console.WriteLine("\n=== Request Headers (Client-side) ===");
        Console.WriteLine($"X-API-Key: {apiKey[..10]}...{apiKey[^5..]}");

        var content = new MultipartFormDataContent();

        var formFields = new Dictionary<string, string>
        {
            { "vendor_name", "Parker Hannifin Indonesia, PT" },
            { "invoice_number", "69657555" },
            { "invoice_date", "2024-10-31" },
            { "invoice_amount", "6914140" },
            { "vat_amount", "594000" }
        };

        Console.WriteLine("\n=== Form Data (Fields) being added ===");
        foreach (var field in formFields)
        {
            // Convert string to byte array
            var fieldBytes = Encoding.UTF8.GetBytes(field.Value);
            var byteArrayContent = new ByteArrayContent(fieldBytes);

            // IMPORTANT: DO NOT set byteArrayContent.Headers.ContentType here.
            // The MultipartFormDataContent.Add method overload that takes (HttpContent content, string name)
            // will set the Content-Disposition header correctly.
            // It will NOT add a Content-Type for this part if not already set on byteArrayContent.
            content.Add(byteArrayContent, field.Key);
            Console.WriteLine($"Added Field (as ByteArrayContent): Name='{field.Key}', Value='{field.Value}'");
            // For debugging, check headers right after adding (though it's tricky to inspect part headers directly before serialization)
        }

        var filePath = "/Users/<USER>/Desktop/Komatsu/test_files/invoice/air_liquide_invoice.pdf"; // Ensure this path is correct
        Console.WriteLine("\n=== File Information ===");
        Console.WriteLine($"File Path: {filePath}");
        Console.WriteLine($"File Exists: {File.Exists(filePath)}");

        Stream? fileStream = null;
        if (File.Exists(filePath))
        {
            var fileInfo = new FileInfo(filePath);
            Console.WriteLine($"File Size: {fileInfo.Length} bytes");
            fileStream = File.OpenRead(filePath);
            var streamContent = new StreamContent(fileStream);
            var contentType = new MediaTypeHeaderValue("application/pdf");
            streamContent.Headers.ContentType = contentType;
            content.Add(streamContent, "file", Path.GetFileName(filePath));
            Console.WriteLine($"Added File: Name='file', FileName='{Path.GetFileName(filePath)}', ContentType='{contentType}'");
        }
        else
        {
            Console.WriteLine("Error: File not found!");
            content.Dispose(); // Dispose content if we return early
            request.Dispose();
            client.Dispose();
            return;
        }

        request.Content = content;

        Console.WriteLine("\n=== Sending Request ===");
        Console.WriteLine($"Method: {request.Method}");
        Console.WriteLine($"URL: {request.RequestUri}");
        Console.WriteLine($"Request Content-Type Header: {request.Content.Headers.ContentType}");

        try
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var response = await client.SendAsync(request);
            stopwatch.Stop();

            Console.WriteLine("\n=== Response Received ===");
            Console.WriteLine($"Status Code: {(int)response.StatusCode} {response.StatusCode}");
            Console.WriteLine($"Time Taken: {stopwatch.ElapsedMilliseconds}ms");

            Console.WriteLine("Response Headers:");
            foreach (var header in response.Headers)
            {
                Console.WriteLine($"  {header.Key}: {string.Join(", ", header.Value)}");
            }

            if (response.Content != null)
            {
                foreach (var header in response.Content.Headers)
                {
                    Console.WriteLine($"  Content-{header.Key}: {string.Join(", ", header.Value)}");
                }
            }

            string responseContentString = string.Empty;
            if (response.Content != null)
            {
                responseContentString = await response.Content.ReadAsStringAsync();
                Console.WriteLine("\n=== Response Body ===");
                Console.WriteLine(responseContentString);
            }
            else
            {
                Console.WriteLine("\n=== No response content ===");
            }

            if (!response.IsSuccessStatusCode)
            {
                Console.WriteLine($"\n!!! HTTP Request Error (handled after reading body): {response.StatusCode}");
            }
            else
            {
                Console.WriteLine("\n=== Request completed successfully ===");
            }
        }
        catch (HttpRequestException ex)
        {
            Console.WriteLine($"\n!!! HTTP Request Exception: {ex.Message}");
            if (ex.StatusCode.HasValue)
            {
                Console.WriteLine($"Status Code from Exception: {ex.StatusCode}");
            }
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n!!! Unexpected Error: {ex.Message}");
            Console.WriteLine($"Stack Trace: {ex.StackTrace}");
        }
        finally
        {
            fileStream?.Dispose();
            content.Dispose(); // This will also dispose contained HttpContent objects like byteArrayContent and streamContent
            request.Dispose();
            client.Dispose();
        }
    }
}
