# Testing Documentation for Invoice Matching Project

This document provides an overview of the testing approach and implementation for the Invoice Matching project.

## Testing Framework

The project uses the following testing tools:

- **Vitest**: A fast and lightweight testing framework compatible with Next.js
- **React Testing Library**: For testing React components
- **<PERSON><PERSON> (Mock Service Worker)**: For mocking API requests
- **JSDOM**: For simulating a browser environment

## Test Structure

The tests are organized to mirror the application structure:

```
tests/
├── app/
│   └── api/
│       ├── logs/
│       ├── process-invoice/
│       └── process-tax-invoice/
├── lib/
│   └── api/
│       ├── color-detection/
│       └── external-service/
├── mocks/
│   ├── color-detection.ts
│   ├── handlers.ts
│   └── prisma.ts
├── setup.ts
└── utils.ts
```

## Running Tests

To run the tests, use the following commands:

```bash
# Run tests in watch mode
pnpm test

# Run tests once
pnpm test:run

# Run tests with verbose output
pnpm test:coverage

# Run integration tests against remote API
pnpm test:integration
```

## Test Coverage

The tests cover the following areas:

1. **API Endpoints**:
   - `/api/process-invoice`: Tests for processing standard invoices
   - `/api/process-tax-invoice`: Tests for processing tax invoices
   - `/api/logs`: Tests for retrieving processing logs

2. **Utility Functions**:
   - `external-service.ts`: Tests for the external API client
   - `color-detection.ts`: Tests for the color detection functionality
   - `db/index.ts`: Tests for the database connection

## Mocking Strategy

The tests use several mocking strategies:

1. **MSW (Mock Service Worker)**: Used to mock HTTP requests to external APIs
2. **Prisma Mock**: Used to mock database operations
3. **Function Mocks**: Used to mock specific functions like color detection

## Test Utilities

The `tests/utils.ts` file contains utility functions for creating test data:

- `createMockPdfBuffer()`: Creates a mock PDF buffer
- `createMockFormData()`: Creates a mock FormData object with a PDF file
- `createMockInvoiceFormData()`: Creates a mock FormData for invoice processing
- `createMockTaxInvoiceFormData()`: Creates a mock FormData for tax invoice processing
- `MockNextRequest`: A mock implementation of NextRequest for testing API routes

## Integration Tests

The project includes integration tests that call the remote API endpoints for invoice and tax invoice processing. These tests are configured using a JSON file that specifies the environment settings and test cases.

### Running Integration Tests

To run the integration tests:

```bash
# Run integration tests
pnpm test:integration
```

### Integration Test Structure

The integration tests are located in the `tests/integration` directory:

```
tests/integration/
├── README.md
├── remote-api-test.ts
└── test-config.json
```

- `remote-api-test.ts`: Contains the test logic for calling the remote APIs
- `test-config.json`: Contains the environment configuration and test cases

### Test Configuration

The test configuration file (`test-config.json`) has the following structure:

```json
{
   "env":{
      "base_url":"https://kim-staging.happyfresh.io",
      "api_key":"your-api-key"
   },
   "tests":[
      {
         "input":{
            "file_url":"https://example.com/invoice.pdf",
            "vendor_name":"Vendor Name",
            "invoice_amount":1000.00,
            "vat_amount":100.00,
            "invoice_date":"2023-01-01",
            "invoice_number":"INV-123"
         },
         "output":{
            "vendor_name":"matched",
            "invoice_amount":"mismatched",
            "vat_amount":"mismatched",
            "invoice_date":"matched",
            "invoice_number":"matched"
         }
      }
   ]
}
```

See [tests/integration/README.md](tests/integration/README.md) for more details on the integration tests.

## Future Improvements

Some areas for future improvement in the testing approach:

1. **Component Tests**: Add tests for React components in the admin UI
2. **Integration Tests**: Add more comprehensive integration tests
3. **E2E Tests**: Add end-to-end tests using Playwright or Cypress
4. **Code Coverage**: Implement proper code coverage reporting
5. **Dependency Injection**: Refactor the code to use dependency injection for better testability

## Best Practices

When adding new tests, follow these best practices:

1. **Test Isolation**: Each test should be independent and not rely on the state from other tests
2. **Mock External Dependencies**: Always mock external dependencies like APIs and databases
3. **Test Edge Cases**: Include tests for error conditions and edge cases
4. **Keep Tests Simple**: Each test should test one specific behavior
5. **Use Descriptive Names**: Use descriptive test names that explain what is being tested
