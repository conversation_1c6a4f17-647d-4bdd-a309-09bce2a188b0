# syntax=docker/dockerfile:1

# This image is solely for running Prisma migrations.
# It doesn't need to be optimized for size, only for functionality.
FROM node:22-alpine AS migrator

WORKDIR /app

# Install pnpm (or npm if you prefer, as it's built-in)
RUN npm install -g pnpm@10.8.1

# Copy package files and install all dependencies (including dev where prisma CLI is)
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# Copy scripts
COPY scripts ./scripts

# Copy Prisma schema and migrations
COPY prisma ./prisma

# Generate Prisma client
RUN pnpm db:generate

# Set the entrypoint to "sh -c".
ENTRYPOINT ["sh", "-c"]

# Define the command for migration
# CMD ["sh", "-c", "pnpm prisma migrate deploy"]