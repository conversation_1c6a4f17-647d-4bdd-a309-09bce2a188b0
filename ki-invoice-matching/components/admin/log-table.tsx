"use client"

import * as React from "react"
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { FormattedLogEntry } from "@/lib/types/log"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogTitle
} from "@/components/ui/dialog"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Badge } from "@/components/ui/badge"
import { X, CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { DateRange } from "react-day-picker"

interface LogTableProps {
  data: FormattedLogEntry[]
  onFilterChange: (filters: { [key: string]: string }) => void
  currentFilters: {
    requestType: string
    processingStatus: string
    vendorName: string
    documentNo: string
  }
  dateRange: { from: Date | undefined; to?: Date | undefined }
  showDateRangePicker?: boolean
}

export function LogTable({ data, onFilterChange, currentFilters, dateRange, showDateRangePicker = true }: LogTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [selectedLog, setSelectedLog] = React.useState<FormattedLogEntry | null>(null)

  // State to control date picker popover
  const [datePickerOpen, setDatePickerOpen] = React.useState(false)
  const [localDateRange, setLocalDateRange] = React.useState<DateRange | undefined>(dateRange)

  // Create refs for input fields to maintain focus
  const vendorInputRef = React.useRef<HTMLInputElement>(null)
  const documentInputRef = React.useRef<HTMLInputElement>(null)

  // State for debounced input values
  const [vendorInput, setVendorInput] = React.useState(currentFilters.vendorName)
  const [documentInput, setDocumentInput] = React.useState(currentFilters.documentNo)

  // Debounce function
  const useDebounce = (value: string, delay: number) => {
    const [debouncedValue, setDebouncedValue] = React.useState(value)

    React.useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedValue(value)
      }, delay)

      return () => {
        clearTimeout(handler)
      }
    }, [value, delay])

    return debouncedValue
  }

  // Debounced values
  const debouncedVendorInput = useDebounce(vendorInput, 500)
  const debouncedDocumentInput = useDebounce(documentInput, 500)

  // Apply debounced filters
  React.useEffect(() => {
    if (debouncedVendorInput !== currentFilters.vendorName) {
      table?.getColumn("inputVendorName")?.setFilterValue(debouncedVendorInput)
      onFilterChange({ vendorName: debouncedVendorInput })
    }
  }, [debouncedVendorInput])

  React.useEffect(() => {
    if (debouncedDocumentInput !== currentFilters.documentNo) {
      table?.getColumn("inputDocumentNo")?.setFilterValue(debouncedDocumentInput)
      onFilterChange({ documentNo: debouncedDocumentInput })
    }
  }, [debouncedDocumentInput])

  // Update input states when currentFilters change externally
  React.useEffect(() => {
    setVendorInput(currentFilters.vendorName)
    setDocumentInput(currentFilters.documentNo)
  }, [currentFilters.vendorName, currentFilters.documentNo])

  // Update local date range when prop changes
  React.useEffect(() => {
    setLocalDateRange(dateRange)
  }, [dateRange])

  // Handle date range changes
  const handleDateRangeChange = (range: DateRange | undefined) => {
    // Always update local state for UI display
    setLocalDateRange(range)

    // Close the date picker when both from and to dates are selected
    if (range?.from && range?.to) {
      setTimeout(() => {
        setDatePickerOpen(false)
      }, 100)

      // Only notify parent component when both dates are selected
      const startDate = range.from.toISOString()
      const endDate = range.to.toISOString()
      onFilterChange({
        startDate,
        endDate
      })
    } else if (!range) {
      // Clear the filter when range is undefined (reset button clicked)
      onFilterChange({
        startDate: '',
        endDate: ''
      })
    }
    // If only one date is selected, don't trigger filtering
  }

  // Initialize column filters with current filter values
  React.useEffect(() => {
    // Set initial column filters based on currentFilters
    const initialFilters: ColumnFiltersState = [];

    if (currentFilters.requestType) {
      initialFilters.push({
        id: 'requestType',
        value: currentFilters.requestType
      });
    }

    if (currentFilters.processingStatus) {
      initialFilters.push({
        id: 'processingStatus',
        value: currentFilters.processingStatus
      });
    }

    if (currentFilters.vendorName) {
      initialFilters.push({
        id: 'inputVendorName',
        value: currentFilters.vendorName
      });
    }

    if (currentFilters.documentNo) {
      initialFilters.push({
        id: 'inputDocumentNo',
        value: currentFilters.documentNo
      });
    }

    setColumnFilters(initialFilters);
  }, [currentFilters])

  // State for dialog open/close
  const [dialogOpen, setDialogOpen] = React.useState(false)

  const columns: ColumnDef<FormattedLogEntry>[] = [
    {
      accessorKey: "id",
      header: "ID",
      cell: ({ row }) => <div className="truncate max-w-[100px]">{row.getValue("id")}</div>,
    },
    {
      accessorKey: "requestType",
      header: "Type",
      cell: ({ row }) => (
        <Badge variant={row.getValue("requestType") === "invoice" ? "default" : "secondary"}>
          {row.getValue("requestType")}
        </Badge>
      ),
    },
    {
      accessorKey: "processingStatus",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("processingStatus") as string
        return (
          <Badge
            variant={
              status === "success" ? "default" :
              status === "failed" ? "destructive" :
              "outline"
            }
            className={status === "success" ? "bg-green-500 hover:bg-green-600 text-white" : ""}
          >
            {status}
          </Badge>
        )
      },
    },
    {
      accessorKey: "inputVendorName",
      header: "Vendor",
      cell: ({ row }) => <div>{row.getValue("inputVendorName") || "-"}</div>,
    },
    {
      accessorKey: "inputDocumentNo",
      header: "Document #",
      cell: ({ row }) => <div>{row.getValue("inputDocumentNo") || "-"}</div>,
    },
    {
      accessorKey: "matchedFields",
      header: "Matched",
      cell: ({ row }) => {
        const matchedFields = row.original.matchedFields;
        const totalFields = row.original.totalFields;
        const percentage = row.original.matchedFieldsPercentage;

        if (matchedFields === null || totalFields === null) return <div>-</div>;

        return (
          <div className="flex items-center gap-2">
            <span>{matchedFields}/{totalFields}</span>
            {percentage !== null && percentage !== undefined && (
              <Badge
                variant={percentage > 80 ? "default" : percentage > 50 ? "default" : "destructive"}
                className={percentage > 80 ? "bg-green-500 hover:bg-green-600 text-white" : ""}
              >
                {percentage}%
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "processedAt",
      header: "Processed At",
      cell: ({ row }) => <div>{row.getValue("processedAt") || "-"}</div>,
    },
    {
      id: "actions",
      cell: ({ row }) => {
        // Function to handle opening the dialog and setting the selected log
        const handleOpenDialog = () => {
          setSelectedLog(row.original);
          setDialogOpen(true);
        };

        return (
          <div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleOpenDialog}
            >
              Details
            </Button>

            <Dialog open={dialogOpen && selectedLog?.id === row.original.id} onOpenChange={(open) => {
              if (!open) setDialogOpen(false);
            }}>
              <DialogContent className="max-w-2xl">
                {/* Consistent spacing with 16px (4 in Tailwind) between major sections */}
                <div className="space-y-4">
                  {/* Header section with consistent internal spacing */}
                  <div className="space-y-3 pb-3 border-b">
                    <DialogTitle className="text-xl font-semibold">Log Details</DialogTitle>

                    {/* Status and processed info with equal spacing */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            row.original.processingStatus === "success" ? "default" :
                            row.original.processingStatus === "failed" ? "destructive" :
                            "outline"
                          }
                          className={
                            row.original.processingStatus === "success"
                              ? "bg-green-500 hover:bg-green-600 text-white px-3 py-1 text-sm"
                              : "px-3 py-1 text-sm"
                          }
                        >
                          {row.original.processingStatus}
                        </Badge>
                        <Badge variant={row.original.requestType === "invoice" ? "default" : "secondary"} className="px-3 py-1 text-sm">
                          {row.original.requestType}
                        </Badge>
                      </div>

                      <div className="text-sm text-gray-600">
                        <span className="whitespace-nowrap">
                          Processed: <span className="font-medium">{row.original.processedAt || "Pending"}</span>
                        </span>
                      </div>
                    </div>

                    {/* ID with consistent spacing */}
                    <div className="text-xs text-gray-500 font-mono">
                      ID: {row.original.id}
                    </div>
                  </div>

                  {/* Content section with consistent spacing */}
                  <div className="space-y-4">
                    {/* Error message section - retained for important errors */}
                    {row.original.errorMessage && (
                      <div className="space-y-3">
                        <h3 className="text-base font-semibold text-red-600 border-b border-red-200 pb-2">Error</h3>
                        <div className="p-3 bg-red-50 text-red-800 rounded-md text-sm">
                          {row.original.errorMessage}
                        </div>
                      </div>
                    )}

                    {/* Matching results section - the main focus */}
                    {row.original.matchingResult && (
                      <div className="space-y-3">
                        <h3 className="text-base font-semibold border-b pb-2">Matching Results</h3>
                        <div className="relative">
                          <pre className="p-4 bg-gray-50 rounded-md text-sm overflow-auto max-h-[400px] font-mono text-xs">
                            {JSON.stringify(row.original.matchingResult, null, 2)}
                          </pre>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </DialogContent>
          </Dialog>
          </div>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  })



  return (
    <div>
      <div className="flex items-center py-4 gap-2 flex-wrap">
        <div className="relative flex-1 min-w-[200px]">
          <Input
            ref={vendorInputRef}
            placeholder="Filter by vendor name..."
            value={vendorInput}
            onChange={(event) => setVendorInput(event.target.value)}
            className="pr-8"
          />
          {vendorInput && (
            <button
              className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
              onClick={() => {
                setVendorInput("")
                onFilterChange({ vendorName: "" })
                vendorInputRef.current?.focus()
              }}
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        <div className="relative flex-1 min-w-[200px]">
          <Input
            ref={documentInputRef}
            placeholder="Filter by invoice or tax invoice number..."
            value={documentInput}
            onChange={(event) => setDocumentInput(event.target.value)}
            className="pr-8"
          />
          {documentInput && (
            <button
              className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
              onClick={() => {
                setDocumentInput("")
                onFilterChange({ documentNo: "" })
                documentInputRef.current?.focus()
              }}
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        <Select
          onValueChange={(value) => {
            table.getColumn("requestType")?.setFilterValue(value === "all" ? "" : value)
            onFilterChange({ requestType: value === "all" ? "" : value })
          }}
          value={currentFilters.requestType ? currentFilters.requestType : "all"}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="invoice">Invoice</SelectItem>
            <SelectItem value="taxInvoice">Tax Invoice</SelectItem>
          </SelectContent>
        </Select>
        <Select
          onValueChange={(value) => {
            table.getColumn("processingStatus")?.setFilterValue(value === "all" ? "" : value)
            onFilterChange({ processingStatus: value === "all" ? "" : value })
          }}
          value={currentFilters.processingStatus ? currentFilters.processingStatus : "all"}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="success">Success</SelectItem>
            <SelectItem value="failed">Failed</SelectItem>
            <SelectItem value="processing">Processing</SelectItem>
          </SelectContent>
        </Select>
        {/* Interactive date range picker */}
        {!showDateRangePicker && (
          <Popover open={datePickerOpen} onOpenChange={setDatePickerOpen}>
            <PopoverTrigger asChild>
              {/* wrapper – “pill” with consistent internal spacing */}
              <label className="
                inline-flex items-center gap-2 rounded-lg border border-gray-300
                bg-white px-3 py-2 shadow-sm cursor-pointer
                hover:border-gray-400 focus-visible:ring focus-visible:ring-indigo-500
                w-[260px] max-w-full
              ">
                <CalendarIcon className="h-4 w-4 shrink-0 text-gray-500" />

                {/* date string */}
                <span className="whitespace-nowrap text-sm text-gray-700">
                  {localDateRange?.from && localDateRange?.to
                    ? `${format(localDateRange.from, "LLL dd, y")} – ${format(
                        localDateRange.to,
                        "LLL dd, y",
                      )}`
                    : "All Dates"}
                </span>

                {/* close button */}
                {localDateRange?.from && localDateRange?.to && (
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDateRangeChange(undefined)
                    }}
                    className="
                      ml-2 flex h-5 w-5 items-center justify-center rounded-full
                      text-gray-500 hover:bg-gray-100 hover:text-gray-700
                      focus-visible:ring focus-visible:ring-indigo-500
                    "
                  >
                    <X className="h-3.5 w-3.5" />
                    <span className="sr-only">Clear date range</span>
                  </button>
                )}
              </label>
            </PopoverTrigger>

            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={localDateRange?.from || new Date()}
                selected={localDateRange}
                onSelect={handleDateRangeChange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        )}
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

    </div>
  )
}
