"use client";

import React, { useCallback, useEffect, useRef, useState } from 'react';
import * as Sentry from '@sentry/nextjs';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { eachDayOfInterval, startOfDay, endOfDay, format } from 'date-fns';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { ProcessingTimeData } from '@/types/dashboard';

// Add cache control
const CACHE_TIMEOUT = 5000; // 5 seconds cache
const timeStatsCache = new Map<string, { data: any; timestamp: number }>();

const getCacheKey = (startDate?: Date, endDate?: Date) => {
  return `processing_times_${startDate?.toISOString()}_${endDate?.toISOString()}`;
};

interface ProcessingTimePoint {
  timestamp: string;
  p50?: number;
  p95?: number;
  p99?: number;
  standardInvoice?: number;
  taxInvoice?: number;
}

const fillMissingDates = (
  data: ProcessingTimePoint[],
  dateRange: { from: Date; to: Date },
  metrics: string[]
): ProcessingTimePoint[] => {
  // Ensure we start from beginning of day for consistent comparison
  const startDate = startOfDay(dateRange.from);
  const endDate = startOfDay(dateRange.to);

  // Generate all dates in range
  const allDates = eachDayOfInterval({
    start: startDate,
    end: endDate,
  });

  // Create a map of existing data using date string as key for reliable matching
  const dataMap = new Map(
    data.map(item => {
      const date = startOfDay(new Date(item.timestamp));
      return [date.toISOString().split('T')[0], item];
    })
  );

  // Map all dates to data points, filling with 0s where needed
  return allDates.map(date => {
    const dateKey = date.toISOString().split('T')[0];
    const existingData = dataMap.get(dateKey);

    if (existingData) {
      return existingData;
    }

    // Create empty point with 0 values
    const emptyPoint: ProcessingTimePoint = {
      timestamp: date.toISOString(),
    };
    metrics.forEach(metric => {
      (emptyPoint as any)[metric] = 0;
    });
    return emptyPoint;
  });
};

interface ProcessingTimeChartProps {
  dateRange: {
    from: Date;
    to: Date;
  };
}

export const ProcessingTimeChart: React.FC<ProcessingTimeChartProps> = ({ dateRange }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Add request tracking refs
  const abortControllerRef = useRef<AbortController | null>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout>();
  const latestRequestRef = useRef<number>(0);

  const fetchProcessingTimes = useCallback(async (startDate: Date, endDate: Date) => {
    // Cancel any pending requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    const cacheKey = getCacheKey(startDate, endDate);
    const cached = timeStatsCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < CACHE_TIMEOUT) {
      setData(cached.data);
      setLoading(false);
      return cached.data;
    }

    const requestId = Date.now();
    latestRequestRef.current = requestId;

    const transaction = Sentry.startSpan({
      op: 'function',
      name: 'fetchProcessingTimes',
      attributes: { startDate: startDate.toISOString(), endDate: endDate.toISOString(), requestId },
    }, async () => {
      if (requestId !== latestRequestRef.current) return;

      try {
        setLoading(true);

        // Ensure we're using start of day for start date and end of day for end date
        const adjustedStartDate = startOfDay(startDate);
        const adjustedEndDate = endOfDay(endDate);

        const queryParams = new URLSearchParams({
          startDate: adjustedStartDate.toISOString(),
          endDate: adjustedEndDate.toISOString(),
        });

        const response = await fetch(`/api/dashboard/processing-times?${queryParams.toString()}`, {
          signal: abortControllerRef.current?.signal,
          headers: {
            'Accept': 'application/json',
          }
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`API request failed with status ${response.status}: ${errorText}`);
        }

        const timeData = await response.json();

        if (requestId === latestRequestRef.current) {
          setData(timeData);
          // Cache the result
          timeStatsCache.set(cacheKey, { data: timeData, timestamp: Date.now() });
        }

        return timeData;
      } finally {
        if (requestId === latestRequestRef.current) {
          setLoading(false);
        }
      }
    });

    try {
      return await transaction;
    } catch (error) {
      if (error.name === 'AbortError') return;
      console.error('Error fetching processing times:', error);
      Sentry.captureException(error);
      throw error;
    }
  }, []);

  useEffect(() => {
    if (!dateRange.from || !dateRange.to) return;

    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      fetchProcessingTimes(dateRange.from, dateRange.to);
    }, 250);

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [dateRange.from, dateRange.to, fetchProcessingTimes]);

  const processedChartData = React.useMemo(() => {
    if (!data?.overall) return [];
    return fillMissingDates(data.overall, dateRange, ['p50', 'p95', 'p99']);
  }, [data?.overall, dateRange]);

  const processedDocumentData = React.useMemo(() => {
    if (!data?.byDocumentType) return [];
    return fillMissingDates(data.byDocumentType, dateRange, ['standardInvoice', 'taxInvoice']);
  }, [data?.byDocumentType, dateRange]);

  if (loading) {
    return <div className="space-y-4">
      <Card>
        <CardContent className="flex items-center justify-center h-[300px]">
          Loading...
        </CardContent>
      </Card>
    </div>;
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Overall Processing Time</CardTitle>
        </CardHeader>
        <CardContent>
          {processedChartData.length === 0 ? (
            <div className="flex items-center justify-center h-[300px] text-muted-foreground">
              <div className="text-center">
                <p className="text-sm">No processing data available for the selected date range.</p>
                <p className="text-xs mt-1">Try selecting a different time period or check if there are completed processing logs.</p>
              </div>
            </div>
          ) : processedChartData.length === 1 ? (
            <div className="space-y-4">
              <div className="flex items-center justify-center h-[60px] text-muted-foreground bg-muted/30 rounded-md">
                <div className="text-center">
                  <p className="text-sm">Only one day of data available in the selected range.</p>
                  <p className="text-xs mt-1">Try selecting "Last Month" or a custom date range to see trends over time.</p>
                </div>
              </div>
              <ResponsiveContainer width="100%" height={240}>
                <LineChart data={processedChartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={timestamp => format(new Date(timestamp), 'MMM d')}
                    interval="preserveStartEnd"
                  />
                  <YAxis
                    tickFormatter={value => value.toFixed(1)}
                    domain={[0, 24]}
                    label={{ value: 'seconds', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip
                    labelFormatter={timestamp => format(new Date(timestamp), 'MMM d, yyyy')}
                    formatter={(value: number, name) => [`${value.toFixed(1)} sec`, name]}
                    contentStyle={{ background: '#fff', border: '1px solid #ccc', borderRadius: '4px', padding: '8px' }}
                  />
                  <Legend verticalAlign="top" height={36}/>
                  <Line
                    type="linear"
                    dataKey="p50"
                    stroke="#2563eb"
                    name="p50"
                    strokeWidth={2}
                    dot={{ r: 4, fill: '#2563eb' }}
                    isAnimationActive={false}
                    connectNulls={true}
                  />
                  <Line
                    type="linear"
                    dataKey="p95"
                    stroke="#16a34a"
                    name="p95"
                    strokeWidth={2}
                    dot={{ r: 4, fill: '#16a34a' }}
                    isAnimationActive={false}
                    connectNulls={true}
                  />
                  <Line
                    type="linear"
                    dataKey="p99"
                    stroke="#dc2626"
                    name="p99"
                    strokeWidth={2}
                    dot={{ r: 4, fill: '#dc2626' }}
                    isAnimationActive={false}
                    connectNulls={true}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={processedChartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="timestamp"
                  tickFormatter={timestamp => format(new Date(timestamp), 'MMM d')}
                  interval="preserveStartEnd"
                />
                <YAxis
                  tickFormatter={value => value.toFixed(1)}
                  domain={[0, 24]}
                  label={{ value: 'seconds', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip
                  labelFormatter={timestamp => format(new Date(timestamp), 'MMM d, yyyy')}
                  formatter={(value: number, name) => [`${value.toFixed(1)} sec`, name]}
                  contentStyle={{ background: '#fff', border: '1px solid #ccc', borderRadius: '4px', padding: '8px' }}
                />
                <Legend verticalAlign="top" height={36}/>
                <Line
                  type="linear"
                  dataKey="p50"
                  stroke="#2563eb"
                  name="p50"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#2563eb' }}
                  isAnimationActive={false}
                  connectNulls={true}
                />
                <Line
                  type="linear"
                  dataKey="p95"
                  stroke="#16a34a"
                  name="p95"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#16a34a' }}
                  isAnimationActive={false}
                  connectNulls={true}
                />
                <Line
                  type="linear"
                  dataKey="p99"
                  stroke="#dc2626"
                  name="p99"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#dc2626' }}
                  isAnimationActive={false}
                  connectNulls={true}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Latency by Document Type (median)</CardTitle>
        </CardHeader>
        <CardContent>
          {processedDocumentData.length === 0 ? (
            <div className="flex items-center justify-center h-[240px] text-muted-foreground">
              <div className="text-center">
                <p className="text-sm">No completed processing logs found in the selected date range.</p>
                <p className="text-xs mt-1">Try selecting a different date range or check if there are any completed requests.</p>
              </div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={240}>
              <LineChart data={processedDocumentData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="timestamp"
                  tickFormatter={timestamp => format(new Date(timestamp), 'MMM d')}
                  interval="preserveStartEnd"
                />
                <YAxis
                  tickFormatter={value => value.toFixed(1)}
                  domain={[0, 'auto']}
                  label={{ value: 'seconds', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip
                  labelFormatter={timestamp => format(new Date(timestamp), 'MMM d, yyyy')}
                  formatter={(value: number, name) => [`${value.toFixed(1)} sec`, name]}
                  contentStyle={{ background: '#fff', border: '1px solid #ccc', borderRadius: '4px', padding: '8px' }}
                />
                <Legend verticalAlign="top" height={36}/>
                <Line
                  type="linear"
                  dataKey="standardInvoice"
                  stroke="#2563eb"
                  name="Standard Invoice"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#2563eb' }}
                  isAnimationActive={false}
                  connectNulls={false}
                />
                <Line
                  type="linear"
                  dataKey="taxInvoice"
                  stroke="#16a34a"
                  name="Tax Invoice"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#16a34a' }}
                  isAnimationActive={false}
                  connectNulls={false}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
