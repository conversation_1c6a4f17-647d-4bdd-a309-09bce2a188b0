stages:
  # - test
  - build
  - deploy

# test_job:
#   stage: test
#   image: node:22-alpine
#   script:
#     - pnpm install
#     - pnpm test:run
#   cache:
#     key: ${CI_COMMIT_REF_SLUG}
#     paths:
#       - .pnpm-store
#       - node_modules
#   rules:
#     - if: '$CI_COMMIT_BRANCH'
#       when: manual
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"'
#       when: manual

# Common configuration for build jobs
.build_common: &build_common
  stage: build
  image: docker:latest
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
    CA_CERTIFICATE: "$CA_CERTIFICATE"
    GITLAB_IMAGE_BASE: "registry-gitlab.happyfresh.net/hf/tpd/rainmakers/ki-invoice-matching"
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  rules:
    - when: manual

build_and_push:
  <<: *build_common
  script:
    - docker build -t "${GITLAB_IMAGE_BASE}:${CI_COMMIT_SHORT_SHA}" -t "${GITLAB_IMAGE_BASE}:latest" .
    - docker push "${GITLAB_IMAGE_BASE}:${CI_COMMIT_SHORT_SHA}"
    - docker push "${GITLAB_IMAGE_BASE}:latest"

build_and_push_setup_image:
  <<: *build_common
  script:
    - docker build -f Dockerfile.setup -t "${GITLAB_IMAGE_BASE}:setup-${CI_COMMIT_SHORT_SHA}" -t "${GITLAB_IMAGE_BASE}:setup" .
    - docker push "${GITLAB_IMAGE_BASE}:setup-${CI_COMMIT_SHORT_SHA}"
    - docker push "${GITLAB_IMAGE_BASE}:setup"

deploy_to_staging:
  stage: deploy
  image: mcr.microsoft.com/azure-cli
  services:
    - docker:dind
  variables:
    AZURE_REGISTRY_NAME: "happyfresh"
    AZURE_IMAGE_BASE: "happyfresh.azurecr.io/ext/ki/invoice-matching"
    GITLAB_IMAGE_BASE: "registry-gitlab.happyfresh.net/hf/tpd/rainmakers/ki-invoice-matching"
    CONTAINER_APP_NAME: "kim-staging"
    RESOURCE_GROUP: "kidemo"
    PLATFORMS: "linux/amd64,linux/arm64"
  script:
    - az login --service-principal -u $AZURE_CLIENT_ID -p $AZURE_CLIENT_SECRET --tenant $AZURE_TENANT_ID
    - az acr login --name "$AZURE_REGISTRY_NAME"
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker buildx use colima-builder || docker buildx create --name colima-builder --use
    - docker buildx build --platform "$PLATFORMS" -t "${AZURE_IMAGE_BASE}:${CI_COMMIT_SHORT_SHA}" -t "${GITLAB_IMAGE_BASE}:${CI_COMMIT_SHORT_SHA}" --push .
    - az containerapp update --name "$CONTAINER_APP_NAME" --resource-group "$RESOURCE_GROUP" --image "${AZURE_IMAGE_BASE}:${CI_COMMIT_SHORT_SHA}" --revision-suffix "$CI_COMMIT_SHORT_SHA"
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: manual
  environment:
    name: staging