# PDF Color Detection

This document explains the implementation of the PDF color detection functionality and how to adjust its sensitivity.

## Overview

The PDF color detection tool analyzes PDF documents to determine if they contain color and which specific pages have color. It uses a pixel-by-pixel RGB analysis approach for high accuracy.

## How It Works

### Core Implementation

1. **PDF to Image Conversion**:
   - Each page of the PDF is converted to an image using ImageMagick
   - This allows access to the actual pixel data

2. **RGB Analysis Algorithm**:
   - For each pixel, we extract the R, G, and B values
   - We calculate the average of these values: `avg = (r + g + b) / 3`
   - We set a threshold based on this average: `threshold = max(2.55, avg * 0.01)` (1% of average)
   - A pixel is considered colored if:
     - Any RGB component differs from the average by more than the threshold, OR
     - The RGB components differ from each other by more than the threshold

3. **Page Classification**:
   - A page is considered colored if:
     - More than 0.5% of analyzed pixels are colored, AND
     - There are at least 5 colored pixels
   - Pure black and white pixels are excluded from the analysis to avoid skewing the results

## Usage

```bash
node detect-color-standalone.js /path/to/your/file.pdf
```

Or to process a folder of PDFs:

```bash
node detect-color-standalone.js /path/to/your/folder
```

## Sample Output

```
=== PDF Color Analysis Results ===

File: sample.pdf
Total Pages: 5
Contains Color: Yes
Color Pages: 1, 3, 5
Detection Method: Pixel-by-Pixel RGB Analysis
Confidence: high
Processing Time: 1.25 seconds
```

## Adjustable Thresholds

You can tweak several thresholds in the `detect-color-standalone.js` file to improve sensitivity:

### 1. Pixel Color Threshold (currently 1% of average RGB)

```javascript
const threshold = Math.max(2.55, avg * 0.01); // Line ~238
```

- **Increase** this value (e.g., to 0.02 or 0.03) to be more conservative (fewer pages detected as colored)
- **Decrease** this value (e.g., to 0.005) to be more sensitive (more pages detected as colored)

### 2. Page Color Percentage Threshold (currently 0.5%)

```javascript
const isColored = colorPercentage > 0.5 && colorPixels >= 5; // Line ~253
```

- **Increase** this value (e.g., to 1.0 or 2.0) to require more colored pixels before classifying a page as colored
- **Decrease** this value (e.g., to 0.1) to be more sensitive to small amounts of color

### 3. Minimum Colored Pixels (currently 5)

```javascript
const isColored = colorPercentage > 0.5 && colorPixels >= 5; // Line ~253
```

- **Increase** this value to reduce false positives from noise
- **Decrease** this value if you want to detect even tiny amounts of color

### 4. Sample Size (currently max 10,000 pixels)

```javascript
const maxSamplePixels = 10000; // Line ~203
```

- **Increase** this value for more accurate results (but slower processing)
- **Decrease** this value for faster processing (but potentially less accurate)

### 5. Image Sampling Resolution (currently max 500x500)

```javascript
const pixelDataCmd = `convert "${path.join(tempDir, file)}" -colorspace RGB -sample ${Math.min(width, 500)}x${Math.min(height, 500)} txt:- | grep -v "^#" | head -n ${maxSamplePixels}`; // Line ~208
```

- **Increase** these values for higher resolution sampling
- **Decrease** these values for faster processing

## Recommended Adjustments

### For false positives (detecting B&W as color)

If the tool is incorrectly identifying black and white documents as colored:

- Increase the pixel color threshold from 0.01 to 0.02
- Increase the page color percentage threshold from 0.5% to 1.0%
- Increase the minimum colored pixels from 5 to 10

### For false negatives (not detecting actual color)

If the tool is failing to detect color in documents that do have color:

- Decrease the pixel color threshold from 0.01 to 0.005
- Decrease the page color percentage threshold from 0.5% to 0.1%
- Increase the sample size to analyze more pixels

## Troubleshooting

### "0 pixels analyzed" Issue

If you see a message like "Page X: Analyzed 0 pixels, found 0 color pixels (0.00%)", this indicates that the script was unable to extract pixel data from that page. This can happen for several reasons:

1. **Empty or Blank Pages**: The page might be completely blank or contain only transparent content.

2. **Pages with Large White Spaces**: Pages with small content areas surrounded by large white spaces might be missed during sampling.

3. **ImageMagick Issues**: The `convert` command might be failing to extract pixel data.

4. **PDF Format Issues**: Some PDF pages might use a format or encoding that ImageMagick has trouble processing.

The script includes several enhanced mechanisms to handle these situations:

1. **Non-White Content Detection**:
   - Before pixel analysis, the script checks if the page has any non-white content
   - This works even for pages with minimal content surrounded by large white spaces
   - If content is found but pixel extraction fails, synthetic data is created to ensure analysis continues

2. **White Space Trimming**:
   - Uses `-trim +repage` options to remove white borders before analysis
   - This focuses the sampling on the actual content rather than white space

3. **Improved Sampling**:
   - Uses higher resolution sampling (800x800 instead of 500x500)
   - Uses `awk 'NR % 10 == 0'` to sample pixels throughout the image, not just at the beginning
   - Increases density for better detail capture

4. **Multiple Fallback Methods**:
   - **File Size Heuristic**: If the extracted image file is larger than 20KB, it might contain color
   - **Colorspace Analysis**: Checks if the image has a non-grayscale colorspace
   - **Content Analysis**: Searches for color-related keywords in the file content
   - **Large File Detection**: Files larger than 100KB are more likely to contain color

5. **Synthetic Data Creation**:
   - If non-white content is detected but pixel extraction fails, creates synthetic pixel data
   - This ensures that pages with content are not reported as having "0 pixels analyzed"

You can adjust these thresholds in the script:
```javascript
// Non-white content detection with 1% fuzz factor
const contentCheckCmd = `... -fuzz 1% ...`;

// Sampling resolution
-sample ${Math.min(width, 800)}x${Math.min(height, 800)}

// File size thresholds
if (fileSizeKB > 20) { ... }
if (fileSizeKB > 100) { ... }
```

## Requirements

- Node.js
- ImageMagick (must be installed on the system)

### ImageMagick Version Compatibility

The script automatically detects which version of ImageMagick is installed:

- For ImageMagick v7: Uses the `magick` command first, then falls back to `convert` if needed
- For ImageMagick v6: Uses the `convert` command first, then falls back to `magick` if needed

This ensures compatibility with both older and newer versions of ImageMagick. If you see warnings like "The convert command is deprecated in IMv7, use 'magick' instead of 'convert'", don't worry - the script will automatically try the appropriate command.

## Integration with Invoice Processing

This color detection functionality can be integrated into the invoice processing API to add `color_pages` and `is_colored` to the response. The Prisma schema should be updated to include a `colorPages` field for the `ProcessingLog` model to store color detection results.

## Implementation Notes

- The implementation uses a threshold-based approach to determine if pixels are black and white
- Pure white (255,255,255) and pure black (0,0,0) pixels are excluded from the analysis
- The confidence level is reported as "high" when using the pixel-by-pixel RGB analysis method
- Results are saved to a JSON file when processing a folder of PDFs
