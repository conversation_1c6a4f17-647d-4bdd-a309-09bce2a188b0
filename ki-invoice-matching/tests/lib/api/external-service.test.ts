import { describe, it, expect, beforeEach, vi } from 'vitest'
import { processInvoice, processTaxInvoice } from '@/lib/api/external-service'

// Mock global fetch
global.fetch = vi.fn()

describe('External Service API', () => {
  beforeEach(() => {
    vi.resetAllMocks()
  })

  describe('processInvoice', () => {
    it('should process an invoice successfully', async () => {
      // Mock successful response
      const mockResponse = {
        processing_id: 'test-processing-id',
        document_processing_log_id: 'test-document-processing-log-id',
        results: {
          success: true,
          summary: {
            total_fields: 5,
            matched: 3,
            mismatched: 1,
            not_found: 1
          },
          fields: {
            vendor_name: { input_value: 'Test Vendor', ocr_value: 'Test Vendor', status: 'matched' },
            invoice_number: { input_value: 'INV-123', ocr_value: 'INV-123', status: 'matched' },
            invoice_date: { input_value: '2023-01-01', ocr_value: '2023-01-01', status: 'matched' },
            invoice_amount: { input_value: '1000', ocr_value: '1000.00', status: 'mismatched' },
            vat_amount: { input_value: '100', ocr_value: '', status: 'not_found' }
          }
        }
      }

      // Mock fetch implementation
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      } as Response)

      // Create test data
      const fileUrl = 'https://example.com/invoice.pdf'
      const inputData = {
        vendorName: 'Test Vendor',
        invoiceNo: 'INV-123',
        invoiceDate: '2023-01-01',
        invoiceAmount: 1000,
        vatAmount: 100
      }

      // Call the function
      const result = await processInvoice(fileUrl, inputData)

      // Verify the result
      expect(result).toEqual(mockResponse)
      expect(global.fetch).toHaveBeenCalledTimes(1)
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/documents/invoice'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'X-API-Key': expect.any(String)
          })
        })
      )
    })

    it('should handle API errors', async () => {
      // Mock error response
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: false,
        status: 500,
        text: async () => 'Internal Server Error'
      } as Response)

      // Create test data
      const fileUrl = 'https://example.com/invoice.pdf'

      // Verify that the function throws an error
      await expect(processInvoice(fileUrl)).rejects.toThrow('API request failed')
    })
  })

  describe('processTaxInvoice', () => {
    it('should process a tax invoice successfully', async () => {
      // Mock successful response
      const mockResponse = {
        processing_id: 'test-processing-id',
        document_processing_log_id: 'test-document-processing-log-id',
        results: {
          success: true,
          summary: {
            total_fields: 5,
            matched: 4,
            mismatched: 1,
            not_found: 0
          },
          fields: {
            vendor_name: { input_value: 'Test Vendor', ocr_value: 'Test Vendor', status: 'matched' },
            tax_invoice_number: { input_value: 'TAX-123', ocr_value: 'TAX-123', status: 'matched' },
            tax_invoice_date: { input_value: '2023-01-01', ocr_value: '2023-01-01', status: 'matched' },
            invoice_amount: { input_value: '1000', ocr_value: '1000', status: 'matched' },
            vat_amount: { input_value: '100', ocr_value: '110', status: 'mismatched' }
          }
        }
      }

      // Mock fetch implementation
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      } as Response)

      // Create test data
      const fileUrl = 'https://example.com/tax-invoice.pdf'
      const inputData = {
        vendorName: 'Test Vendor',
        taxInvoiceNo: 'TAX-123',
        invoiceDate: '2023-01-01',
        invoiceAmount: 1000,
        vatAmount: 100
      }

      // Call the function
      const result = await processTaxInvoice(fileUrl, inputData)

      // Verify the result
      expect(result).toEqual(mockResponse)
      expect(global.fetch).toHaveBeenCalledTimes(1)
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/documents/tax-invoice'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'X-API-Key': expect.any(String)
          })
        })
      )
    })
  })
})
