import { describe, it, expect, vi, beforeEach } from 'vitest';
import { NextRequest, NextResponse } from 'next/server';

// Mock next-auth/jwt
vi.mock('next-auth/jwt', () => ({
  getToken: vi.fn().mockResolvedValue(null) // Default to no session
}));

// Mock the database module
vi.mock('@/lib/db', () => ({
  default: {
    apiKey: {
      findFirst: vi.fn(),
      update: vi.fn(),
    },
  },
}));

// Import the modules after mocking
import prisma from '@/lib/db';
import { validateApiKey } from '@/lib/api/api-key-middleware';
import { getToken } from 'next-auth/jwt';

describe('API Key Middleware', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    // Reset getToken mock to default (no session)
    vi.mocked(getToken).mockResolvedValue(null);
  });

  it('should allow requests with valid session token', async () => {
    // Mock getToken to return a valid session
    vi.mocked(getToken).mockResolvedValue({
      id: 'user-123',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'admin',
      iat: 1234567890,
      exp: 1234567890,
      jti: 'test-jti'
    });

    // Create a mock request without an API key
    const req = new NextRequest('https://example.com/api/test');

    // Call the middleware
    const response = await validateApiKey(req);

    // Verify the response - should allow the request without an API key ID
    expect(response).toEqual({});

    // Verify that no API key lookup was performed
    expect(prisma.apiKey.findFirst).not.toHaveBeenCalled();
  });

  it('should return 401 if no API key is provided', async () => {
    // Create a mock request without an API key
    const req = new NextRequest('https://example.com/api/test');

    // Call the middleware
    const result = await validateApiKey(req);

    // Verify the response
    expect(result.error).toBeInstanceOf(NextResponse);
    expect(result.error?.status).toBe(401);

    const data = await result.error?.json();
    expect(data).toHaveProperty('error', 'API key is required');
  });

  it('should return 401 if an invalid API key is provided', async () => {
    // Mock the findFirst method to return null (invalid API key)
    vi.mocked(prisma.apiKey.findFirst).mockResolvedValue(null);

    // Create a mock request with an invalid API key
    const req = new NextRequest('https://example.com/api/test', {
      headers: {
        'X-API-Key': 'invalid-key',
      },
    });

    // Call the middleware
    const result = await validateApiKey(req);

    // Verify the response
    expect(result.error).toBeInstanceOf(NextResponse);
    expect(result.error?.status).toBe(401);

    const data = await result.error?.json();
    expect(data).toHaveProperty('error', 'Invalid API key');
  });

  it('should return API key ID if a valid API key is provided', async () => {
    // Mock the findFirst method to return a valid API key
    vi.mocked(prisma.apiKey.findFirst).mockResolvedValue({
      id: 'test-id',
      name: 'Test API Key',
      keyHash: 'hashed-key',
      description: 'Test description',
      createdAt: new Date(),
      updatedAt: new Date(),
      lastUsedAt: null,
      isActive: true,
      createdById: 'user-id',
    });

    // Mock the update method
    vi.mocked(prisma.apiKey.update).mockResolvedValue({} as any);

    // Create a mock request with a valid API key
    const req = new NextRequest('https://example.com/api/test', {
      headers: {
        'X-API-Key': 'valid-key',
      },
    });

    // Call the middleware
    const result = await validateApiKey(req);

    // Verify the response - should return the API key ID
    expect(result).toEqual({ apiKeyId: 'test-id' });

    // Verify that the lastUsedAt timestamp was updated
    expect(prisma.apiKey.update).toHaveBeenCalledWith({
      where: {
        id: 'test-id',
      },
      data: {
        lastUsedAt: expect.any(Date),
      },
    });
  });

  it('should return 500 if an error occurs', async () => {
    // Mock the findFirst method to throw an error
    vi.mocked(prisma.apiKey.findFirst).mockRejectedValue(new Error('Database error'));

    // Create a mock request with an API key
    const req = new NextRequest('https://example.com/api/test', {
      headers: {
        'X-API-Key': 'some-key',
      },
    });

    // Call the middleware
    const result = await validateApiKey(req);

    // Verify the response
    expect(result.error).toBeInstanceOf(NextResponse);
    expect(result.error?.status).toBe(500);

    const data = await result.error?.json();
    expect(data).toHaveProperty('error', 'Error validating API key');
  });
});
