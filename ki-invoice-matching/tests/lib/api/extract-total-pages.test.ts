import { describe, it, expect } from 'vitest';
import { extractTotalPages } from '@/lib/api/shared-invoice-processing';

describe('extractTotalPages', () => {
  it('should extract page_count from external service response', () => {
    const externalResponse = {
      processing_id: 'test-processing-id',
      document_processing_log_id: 'test-document-processing-log-id',
      page_count: 5,
      results: {
        success: true,
        summary: {
          total_fields: 5,
          matched: 4,
          mismatched: 1,
          not_found: 0
        },
        fields: {}
      }
    };

    const result = extractTotalPages(externalResponse);
    expect(result).toBe(5);
  });

  it('should return null when page_count is not present', () => {
    const externalResponse = {
      processing_id: 'test-processing-id',
      document_processing_log_id: 'test-document-processing-log-id',
      results: {
        success: true,
        summary: {
          total_fields: 5,
          matched: 4,
          mismatched: 1,
          not_found: 0
        },
        fields: {}
      }
    };

    const result = extractTotalPages(externalResponse);
    expect(result).toBe(null);
  });

  it('should return null when page_count is undefined', () => {
    const externalResponse = {
      processing_id: 'test-processing-id',
      document_processing_log_id: 'test-document-processing-log-id',
      page_count: undefined,
      results: {
        success: true,
        summary: {
          total_fields: 5,
          matched: 4,
          mismatched: 1,
          not_found: 0
        },
        fields: {}
      }
    };

    const result = extractTotalPages(externalResponse);
    expect(result).toBe(null);
  });

  it('should return null when external response is null', () => {
    const result = extractTotalPages(null);
    expect(result).toBe(null);
  });

  it('should return null when external response is undefined', () => {
    const result = extractTotalPages(undefined);
    expect(result).toBe(null);
  });

  it('should handle page_count value of 0', () => {
    const externalResponse = {
      processing_id: 'test-processing-id',
      document_processing_log_id: 'test-document-processing-log-id',
      page_count: 0,
      results: {
        success: true,
        summary: {
          total_fields: 5,
          matched: 4,
          mismatched: 1,
          not_found: 0
        },
        fields: {}
      }
    };

    const result = extractTotalPages(externalResponse);
    expect(result).toBe(0);
  });
});