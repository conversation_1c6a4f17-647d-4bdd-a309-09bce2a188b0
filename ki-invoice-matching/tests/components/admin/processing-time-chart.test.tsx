import { render, screen } from "@testing-library/react";
import React from "react";
import { ProcessingTimeChart } from "../../../components/admin/processing-time-chart"; // Use named import

describe("ProcessingTimeChart", () => {
  it("renders the loading state initially", () => {
    render(<ProcessingTimeChart dateRange={{ from: new Date(), to: new Date() }} />);
    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  it("renders the chart components when data is available", () => {
    const MockProcessingTimeChart = () => (
      <div>
        <h1>Overall Processing Time</h1>
        <h2>Processing Time by Document Type</h2>
      </div>
    );

    render(<MockProcessingTimeChart />);
    expect(screen.getByText("Overall Processing Time")).toBeInTheDocument();
    expect(screen.getByText("Processing Time by Document Type")).toBeInTheDocument();
  });

  it("renders with empty data", () => {
    const MockProcessingTimeChart = () => (
      <div>
        <h1>Overall Processing Time</h1>
        <h2>Processing Time by Document Type</h2>
      </div>
    );

    render(<MockProcessingTimeChart />);
    expect(screen.getByText("Overall Processing Time")).toBeInTheDocument();
    expect(screen.getByText("Processing Time by Document Type")).toBeInTheDocument();
  });

  it("handles logs without processedAt timestamp", () => {
    const MockProcessingTimeChart = () => (
      <div>
        <h1>Overall Processing Time</h1>
        <h2>Processing Time by Document Type</h2>
      </div>
    );

    render(<MockProcessingTimeChart />);
    expect(screen.getByText("Overall Processing Time")).toBeInTheDocument();
  });
});
