import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import fs from 'fs/promises';
import { createReadStream } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { server } from '../../tests/setup';
import FormData from 'form-data';
import fetch from 'node-fetch';

// Define types for the test configuration
interface TestEnv {
  base_url: string;
  api_key: string;
  local_file_base_path: string;
}

interface TestConfig {
  tests: Array<{
    uuid: string;
    input: {
      file_url?: string;
      file?: string;
      vendor_name?: string;
      invoice_amount?: number;
      vat_amount?: number;
      invoice_date?: string;
      invoice_number?: string;
      tax_invoice_date?: string;
      tax_invoice_number?: string;
    };
    output: {
      // Legacy format
      vendor_name?: string;
      invoice_amount?: string;
      vat_amount?: string;
      invoice_date?: string;
      invoice_number?: string;
      tax_invoice_date?: string;
      tax_invoice_number?: string;
      is_colored?: boolean;

      // New detailed format
      fields?: {
        vendor_name?: {
          input_value: string;
          ocr_value: string;
          status: string;
        };
        invoice_number?: {
          input_value: string;
          ocr_value: string;
          status: string;
        };
        invoice_date?: {
          input_value: string;
          ocr_value: string;
          status: string;
        };
        invoice_amount?: {
          input_value: string;
          ocr_value: string;
          status: string;
        };
        vat_amount?: {
          input_value: string;
          ocr_value: string;
          status: string;
        };
        tax_invoice_number?: {
          input_value: string;
          ocr_value: string;
          status: string;
        };
        tax_invoice_date?: {
          input_value: string;
          ocr_value: string;
          status: string;
        };
      };
      summary?: {
        total_fields: number;
        matched: number;
        mismatched: number;
        not_found: number;
      };
      color_pages?: number[];
      total_pages?: number;
    };
    focus?: boolean;
  }>;
}

// Helper function to determine if a test is for a tax invoice
function isTaxInvoiceTest(test: TestConfig['tests'][0]): boolean {
  return 'tax_invoice_number' in test.input || 'tax_invoice_date' in test.input;
}

// Helper function to read a local file
async function readLocalFile(filePath: string, localFileBasePath: string): Promise<Buffer> {
  try {
    // Resolve the full path using the base path
    const fullPath = path.join(localFileBasePath, filePath);
    return await fs.readFile(fullPath);
  } catch (error) {
    throw new Error(`Failed to read local file ${filePath} (full path: ${path.join(localFileBasePath, filePath)}): ${error instanceof Error ? error.message : String(error)}`);
  }
}

// Helper function to make API requests to the process-invoice endpoint
async function callProcessInvoiceApi(
  baseUrl: string,
  apiKey: string,
  localFileBasePath: string,
  input: TestConfig['tests'][0]['input']
): Promise<any> {
  // Determine which API endpoint to use based on whether we have a file_url or a local file
  const hasLocalFile = input.file && !input.file_url;
  const url = `${baseUrl}/api/${hasLocalFile ? 'process-invoice-file' : 'process-invoice'}`;

  let response;

  if (hasLocalFile && input.file) {
    // Handle local file upload
    console.log(`Using local file: ${input.file} (with local file base path: ${localFileBasePath})`);

    // Read the file
    const fileBuffer = await readLocalFile(input.file, localFileBasePath);

    // Create form data
    const formData = new FormData();

    // Create a temporary file path for the PDF
    const tempFilePath = path.join(localFileBasePath, `temp-${Date.now()}.pdf`);
    await fs.writeFile(tempFilePath, fileBuffer);

    // Add the file to the form data
    formData.append('file', createReadStream(tempFilePath), {
      filename: path.basename(input.file),
      contentType: 'application/pdf'
    });

    if (input.vendor_name) formData.append('vendor_name', input.vendor_name);
    if (input.invoice_number) formData.append('invoice_number', input.invoice_number);
    if (input.invoice_date) formData.append('invoice_date', input.invoice_date);
    if (input.invoice_amount !== undefined) formData.append('invoice_amount', input.invoice_amount.toString());
    if (input.vat_amount !== undefined) formData.append('vat_amount', input.vat_amount.toString());

    // Make the API request
    response = await fetch(url, {
      method: 'POST',
      headers: {
        'X-API-Key': apiKey,
        ...formData.getHeaders()
      },
      body: formData
    });

    // Clean up the temporary file
    try {
      await fs.unlink(tempFilePath);
    } catch (error) {
      console.warn(`Failed to delete temporary file ${tempFilePath}:`, error);
    }
  } else {
    // Handle URL-based request
    const payload = {
      file_url: input.file_url,
      vendor_name: input.vendor_name || '',
      invoice_number: input.invoice_number || '',
      invoice_date: input.invoice_date || '',
      invoice_amount: input.invoice_amount !== undefined ? input.invoice_amount : 0,
      vat_amount: input.vat_amount !== undefined ? input.vat_amount : 0
    };

    response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey
      },
      body: JSON.stringify(payload)
    });
  }

  if (!response.ok) {
    throw new Error(`API request failed with status ${response.status}: ${await response.text()}`);
  }

  return response.json();
}

// Helper function to make API requests to the process-tax-invoice endpoint
async function callProcessTaxInvoiceApi(
  baseUrl: string,
  apiKey: string,
  localFileBasePath: string,
  input: TestConfig['tests'][0]['input']
): Promise<any> {
  // Determine which API endpoint to use based on whether we have a file_url or a local file
  const hasLocalFile = input.file && !input.file_url;
  const url = `${baseUrl}/api/${hasLocalFile ? 'process-tax-invoice-file' : 'process-tax-invoice'}`;

  let response;

  if (hasLocalFile && input.file) {
    // Handle local file upload
    console.log(`Using local file: ${input.file} (with local file base path: ${localFileBasePath})`);

    // Read the file
    const fileBuffer = await readLocalFile(input.file, localFileBasePath);

    // Create form data
    const formData = new FormData();

    // Create a temporary file path for the PDF
    const tempFilePath = path.join(localFileBasePath, `temp-${Date.now()}.pdf`);
    await fs.writeFile(tempFilePath, fileBuffer);

    // Add the file to the form data
    formData.append('file', createReadStream(tempFilePath), {
      filename: path.basename(input.file),
      contentType: 'application/pdf'
    });

    if (input.vendor_name) formData.append('vendor_name', input.vendor_name);
    if (input.tax_invoice_number) formData.append('tax_invoice_number', input.tax_invoice_number);
    if (input.tax_invoice_date) formData.append('tax_invoice_date', input.tax_invoice_date);
    if (input.invoice_amount !== undefined) formData.append('invoice_amount', input.invoice_amount.toString());
    if (input.vat_amount !== undefined) formData.append('vat_amount', input.vat_amount.toString());

    // Make the API request
    response = await fetch(url, {
      method: 'POST',
      headers: {
        'X-API-Key': apiKey,
        ...formData.getHeaders()
      },
      body: formData
    });

    // Clean up the temporary file
    try {
      await fs.unlink(tempFilePath);
    } catch (error) {
      console.warn(`Failed to delete temporary file ${tempFilePath}:`, error);
    }
  } else {
    // Handle URL-based request
    const payload = {
      file_url: input.file_url,
      vendor_name: input.vendor_name || '',
      tax_invoice_number: input.tax_invoice_number || '',
      tax_invoice_date: input.tax_invoice_date || '',
      invoice_amount: input.invoice_amount !== undefined ? input.invoice_amount : 0,
      vat_amount: input.vat_amount !== undefined ? input.vat_amount : 0
    };

    response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey
      },
      body: JSON.stringify(payload)
    });
  }

  if (!response.ok) {
    throw new Error(`API request failed with status ${response.status}: ${await response.text()}`);
  }

  return response.json();
}

// Helper function to validate the API response against expected output
function validateResponse(
  response: any,
  expectedOutput: TestConfig['tests'][0]['output']
): { passed: boolean; mismatches: Record<string, { expected: any; actual: any; value?: any }> } {
  const mismatches: Record<string, { expected: any; actual: any; value?: any }> = {};
  let passed = true;

  // Check if we're using the new detailed format or legacy format
  const isDetailedFormat = !!expectedOutput.fields;

  if (isDetailedFormat) {
    // Validate using the new detailed format

    // 1. Validate fields
    if (expectedOutput.fields) {
      for (const [fieldName, fieldDetails] of Object.entries(expectedOutput.fields)) {
        if (!fieldDetails) continue;

        // Convert field name to the format used in the API response
        const apiField = fieldName.replace(/([A-Z])/g, '_$1').toLowerCase();

        // Get the field data from the response
        const responseField = response.results.fields[apiField];

        if (!responseField) {
          passed = false;
          mismatches[`fields.${fieldName}`] = {
            expected: fieldDetails,
            actual: 'Field not found in response'
          };
          continue;
        }

        // Check status
        if (fieldDetails.status !== responseField.status) {
          passed = false;
          mismatches[`fields.${fieldName}.status`] = {
            expected: fieldDetails.status,
            actual: responseField.status
          };
        }

        // Check input value (if available in response)
        if (responseField.input_value && fieldDetails.input_value !== responseField.input_value) {
          passed = false;
          mismatches[`fields.${fieldName}.input_value`] = {
            expected: fieldDetails.input_value,
            actual: responseField.input_value
          };
        }

        // Check OCR value (if available in response)
        if (responseField.ocr_value && fieldDetails.ocr_value !== responseField.ocr_value) {
          passed = false;
          mismatches[`fields.${fieldName}.ocr_value`] = {
            expected: fieldDetails.ocr_value,
            actual: responseField.ocr_value
          };
        }
      }
    }

    // 2. Validate summary (if provided)
    if (expectedOutput.summary && response.results.summary) {
      const expectedSummary = expectedOutput.summary;
      const actualSummary = response.results.summary;

      for (const [key, value] of Object.entries(expectedSummary)) {
        if (actualSummary[key] !== value) {
          passed = false;
          mismatches[`summary.${key}`] = {
            expected: value,
            actual: actualSummary[key]
          };
        }
      }
    }

    // 3. Validate is_colored
    if (expectedOutput.is_colored !== undefined &&
        response.results.is_colored !== expectedOutput.is_colored) {
      passed = false;
      mismatches['is_colored'] = {
        expected: expectedOutput.is_colored,
        actual: response.results.is_colored
      };
    }

    // 4. Validate color_pages
    if (expectedOutput.color_pages && response.results.color_pages) {
      // Compare arrays
      const expectedPages = expectedOutput.color_pages;
      const actualPages = response.results.color_pages;

      if (expectedPages.length !== actualPages.length ||
          !expectedPages.every((page, index) => page === actualPages[index])) {
        passed = false;
        mismatches['color_pages'] = {
          expected: expectedPages,
          actual: actualPages
        };
      }
    }

    // 5. Validate total_pages
    if (expectedOutput.total_pages !== undefined &&
        response.results.total_pages !== expectedOutput.total_pages) {
      passed = false;
      mismatches['total_pages'] = {
        expected: expectedOutput.total_pages,
        actual: response.results.total_pages
      };
    }
  } else {
    // Legacy validation logic
    for (const [field, expectedValue] of Object.entries(expectedOutput)) {
      if (field === 'is_colored') {
        // Handle is_colored field differently since it's not in fields but at results.is_colored
        const actualValue = response.results.is_colored;
        if (expectedValue !== actualValue) {
          passed = false;
          mismatches[field] = {
            expected: expectedValue as string | boolean,
            actual: actualValue
          };
        }
        continue;
      }

      // For other fields, use the existing field validation logic
      // Convert field name to the format used in the API response
      const apiField = field.replace(/([A-Z])/g, '_$1').toLowerCase();

      // Get the actual status and value from the response
      const fieldData = response.results.fields[apiField];
      const actualStatus = fieldData?.status || 'not_found';
      const fieldValue = fieldData?.value;

      // Compare expected and actual status
      if (expectedValue !== actualStatus) {
        passed = false;
        mismatches[field] = {
          expected: expectedValue as string,
          actual: actualStatus,
          value: fieldValue
        };
      }
    }
  }

  return { passed, mismatches };
}

// Check if focus mode is enabled via environment variable
const focusMode = process.env.FOCUS_MODE === 'true';
console.log('Focus mode:', focusMode ? 'enabled' : 'disabled');

describe('Remote API Integration Tests', () => {
  let config: TestConfig;
  let env: TestEnv;

  // Stop MSW server before running integration tests
  beforeAll(() => {
    console.log('Stopping MSW server for integration tests...');
    server.close();
  });

  // Load the test configuration before running tests
  beforeAll(async () => {
    try {
      // Get the directory of the current file
      const __filename = fileURLToPath(import.meta.url);
      const __dirname = path.dirname(__filename);

      // Helper function to read config file with fallback to example
      async function readConfigFile(fileName: string): Promise<TestConfig | null> {
        const configPath = path.join(__dirname, fileName);
        try {
          const configData = await fs.readFile(configPath, 'utf-8');
          return JSON.parse(configData);
        } catch (readError) {
          // Try to read the example file
          const exampleConfigPath = path.join(__dirname, `${fileName}.example`);
          try {
            console.warn(`${fileName} not found. Trying to use ${fileName}.example instead.`);
            const configData = await fs.readFile(exampleConfigPath, 'utf-8');
            return JSON.parse(configData);
          } catch (exampleError) {
            console.warn(`Neither ${fileName} nor ${fileName}.example found.`);
            return null;
          }
        }
      }

      // Try to read both configuration files
      const invoiceConfig = await readConfigFile('test-config-invoice.json');
      const taxInvoiceConfig = await readConfigFile('test-config-tax-invoice.json');

      // Check if at least one configuration file was loaded
      if (!invoiceConfig && !taxInvoiceConfig) {
        throw new Error('No configuration files found. Please ensure test-config-invoice.json and/or test-config-tax-invoice.json exist.');
      }

      // Combine the configurations
      const allTests = [];
      if (invoiceConfig) {
        console.log(`Loaded ${invoiceConfig.tests.length} invoice test cases`);
        allTests.push(...invoiceConfig.tests);
      }
      if (taxInvoiceConfig) {
        console.log(`Loaded ${taxInvoiceConfig.tests.length} tax-invoice test cases`);
        allTests.push(...taxInvoiceConfig.tests);
      }

      config = { tests: allTests };
      console.log(`Total test cases loaded: ${allTests.length}`);

      // Try to read the environment configuration file
      const envPath = path.join(__dirname, 'test-env.json');
      let envData;

      try {
        // First try to read the actual env file
        envData = await fs.readFile(envPath, 'utf-8');
      } catch (readError) {
        // If the actual env file doesn't exist, try to read the example file
        console.warn('test-env.json not found. Trying to use test-env.json.example instead.');
        console.warn('Please create your own test-env.json file with your API credentials.');

        const exampleEnvPath = path.join(__dirname, 'test-env.json.example');
        envData = await fs.readFile(exampleEnvPath, 'utf-8');
      }

      env = JSON.parse(envData);

      // Log focus mode status
      if (focusMode) {
        console.log('Focus mode enabled. Only test cases with focus=true will be run.');

        // Count focused tests
        const focusedTests = config.tests.filter(test => test.focus);
        if (focusedTests.length === 0) {
          console.warn('No test cases with focus=true found. All tests will be skipped.');
        } else {
          console.log(`Found ${focusedTests.length} focused test cases.`);
        }
      }
    } catch (error) {
      console.error('Failed to load test configuration:', error);
      throw new Error(`Failed to load test configuration: ${error instanceof Error ? error.message : String(error)}\n` +
        'Please make sure either:\n' +
        '1. test-config-invoice.json and/or test-config-tax-invoice.json exist, OR\n' +
        '2. The corresponding .example files exist\n' +
        'Also ensure test-env.json or test-env.json.example exists in the tests/integration directory.');
    }
  });

  // Restart MSW server after tests are done
  afterAll(() => {
    console.log('Restarting MSW server...');
    server.listen({ onUnhandledRequest: 'error' });
  });

  it('should load the test configuration and environment', () => {
    expect(config).toBeDefined();
    expect(env).toBeDefined();
    expect(env.base_url).toBeDefined();
    expect(env.api_key).toBeDefined();
    expect(env.local_file_base_path).toBeDefined();
    expect(config.tests).toBeDefined();
    expect(config.tests.length).toBeGreaterThan(0);
  }, 60000);

  // Helper function to check if the server is available
  async function isServerAvailable(url: string): Promise<boolean> {
    try {
      // Try to make a simple HEAD request to the base URL
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      console.error('Error checking server availability:', error);
      return false;
    }
  }

  it('should process all test cases correctly', async () => {
    // Track test results
    const testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: 0
    };

    // Skip if config isn't loaded properly
    if (!config || !config.tests || config.tests.length === 0) {
      console.warn('Skipping API tests because config is not properly loaded');
      return;
    }

    // Check if the server is available
    const serverAvailable = await isServerAvailable(env.base_url);
    if (!serverAvailable) {
      console.error(`Server at ${env.base_url} is not available. Marking test as failed.`);
      // Mark the test as failed instead of skipping
      throw new Error(`Server at ${env.base_url} is not available. Please ensure the server is running before running the tests.`);
    }

    // Filter test cases based on focus mode
    const testCasesToRun = focusMode
      ? config.tests.filter(test => test.focus)
      : config.tests;

    // Skip if no test cases to run in focus mode
    if (focusMode && testCasesToRun.length === 0) {
      console.warn('No test cases with focus=true found. Skipping all tests.');
      return;
    }

    // Log the number of test cases to run
    if (focusMode) {
      console.log(`Focus mode enabled. Running ${testCasesToRun.length} of ${config.tests.length} test cases.`);
    } else {
      console.log(`Running all ${testCasesToRun.length} test cases.`);
    }

    // Array to collect errors from failed tests
    const testErrors = [];

    // Process each test case
    for (let i = 0; i < testCasesToRun.length; i++) {
      const testCase = testCasesToRun[i];
      const originalIndex = config.tests.indexOf(testCase);
      let response;

      try {
        console.log(`Processing test case ${originalIndex} [${testCase.uuid}]${testCase.focus ? ' (focused)' : ''}...`);

        // Call the appropriate API based on the test case
        if (isTaxInvoiceTest(testCase)) {
          console.log(`Calling process-tax-invoice API for test case ${originalIndex} [${testCase.uuid}]`);
          response = await callProcessTaxInvoiceApi(
            env.base_url,
            env.api_key,
            env.local_file_base_path,
            testCase.input
          );
        } else {
          console.log(`Calling process-invoice API for test case ${originalIndex} [${testCase.uuid}]`);
          response = await callProcessInvoiceApi(
            env.base_url,
            env.api_key,
            env.local_file_base_path,
            testCase.input
          );
        }

        // Validate the response
        const { passed, mismatches } = validateResponse(response, testCase.output);

        // Update test stats
        testResults.total++;

        if (!passed) {
          testResults.failed++;
          console.error(`\n==== TEST CASE ${originalIndex} [${testCase.uuid}] FAILED ====`);
          console.error(`Mismatches:`, JSON.stringify(mismatches, null, 2));
          console.error(`Input:`, JSON.stringify(testCase.input, null, 2));
          console.error(`Expected Output:`, JSON.stringify(testCase.output, null, 2));
          console.error(`Complete API Response:`, JSON.stringify(response, null, 2));

          // Log detailed information based on the response structure
          if (response.results) {
            if (response.results.fields) {
              console.error(`Response Fields:`, JSON.stringify(response.results.fields, null, 2));
            }
            if (response.results.summary) {
              console.error(`Response Summary:`, JSON.stringify(response.results.summary, null, 2));
            }
            if (response.results.color_pages) {
              console.error(`Response Color Pages:`, JSON.stringify(response.results.color_pages, null, 2));
            }
            if (response.results.total_pages) {
              console.error(`Response Total Pages:`, response.results.total_pages);
            }
          }

          console.error(`==================\n`);

          // Collect error info for later assertion
          const errorMessage = `Test case ${originalIndex} [${testCase.uuid}] failed with mismatches: ${JSON.stringify(mismatches)}`;
          testErrors.push(errorMessage);
        } else {
          testResults.passed++;
          console.log(`Test case ${originalIndex} [${testCase.uuid}] passed successfully`);
        }
      } catch (error) {
        // Update test stats
        testResults.total++;
        testResults.errors++;

        console.error(`\n==== TEST CASE ${originalIndex} [${testCase.uuid}] ERROR ====`);
        console.error(`Error:`, error);
        console.error(`Input:`, JSON.stringify(testCase.input, null, 2));
        console.error(`Expected Output:`, JSON.stringify(testCase.output, null, 2));
        console.error(`Last Response:`, response ? JSON.stringify(response, null, 2) : "No response received");

        // Log detailed information based on the response structure (if available)
        if (response && response.results) {
          if (response.results.fields) {
            console.error(`Response Fields:`, JSON.stringify(response.results.fields, null, 2));
          }
          if (response.results.summary) {
            console.error(`Response Summary:`, JSON.stringify(response.results.summary, null, 2));
          }
          if (response.results.color_pages) {
            console.error(`Response Color Pages:`, JSON.stringify(response.results.color_pages, null, 2));
          }
          if (response.results.total_pages) {
            console.error(`Response Total Pages:`, response.results.total_pages);
          }
        }

        console.error(`==================\n`);

        // Collect error info for later assertion
        const errorMessage = error instanceof Error && error.message.includes('503')
          ? `Test case ${originalIndex} [${testCase.uuid}] failed: Server returned 503 Service Unavailable.`
          : `Test case ${originalIndex} [${testCase.uuid}] failed with error: ${error instanceof Error ? error.message : String(error)}`;
        testErrors.push(errorMessage);
      }
    }

    // Print summary
    console.log(`\n==== TEST SUMMARY ====`);
    console.log(`Total Tests: ${testResults.total}`);
    console.log(`Passed: ${testResults.passed}`);
    console.log(`Failed: ${testResults.failed}`);
    console.log(`Errors: ${testResults.errors}`);
    console.log(`Success Rate: ${testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0}%`);
    console.log(`====================\n`);

    // Assert all test errors at the end
    if (testErrors.length > 0) {
      throw new Error(`${testErrors.length} test(s) failed:\n${testErrors.join('\n')}`);
    }
  }, 7200000);
});
