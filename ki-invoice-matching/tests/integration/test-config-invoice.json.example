{"tests": [{"uuid": "test-example-invoice-match", "input": {"file": "invoice/example_invoice.pdf", "vendor_name": "Example Company Ltd", "invoice_number": "INV-2024-001", "invoice_date": "2024-01-15", "invoice_amount": 1000000.0, "vat_amount": 100000.0}, "output": {"fields": {"vendor_name": {"input_value": "Example Company Ltd", "ocr_value": "Example Company Ltd", "status": "matched"}, "invoice_number": {"input_value": "INV-2024-001", "ocr_value": "INV-2024-001", "status": "matched"}, "invoice_date": {"input_value": "2024-01-15", "ocr_value": "15/01/2024", "status": "matched"}, "invoice_amount": {"input_value": "1000000", "ocr_value": "1,000,000.00", "status": "matched"}, "vat_amount": {"input_value": "100000", "ocr_value": "100,000.00", "status": "matched"}}, "summary": {"total_fields": 5, "matched": 5, "mismatched": 0, "not_found": 0}, "is_colored": true, "color_pages": [1], "total_pages": 1}, "focus": false}, {"uuid": "test-example-invoice-mismatch", "input": {"file": "invoice/example_invoice.pdf", "vendor_name": "Wrong Company Name", "invoice_number": "INV-2024-002", "invoice_date": "2024-01-16", "invoice_amount": 2000000.0, "vat_amount": 200000.0}, "output": {"fields": {"vendor_name": {"input_value": "Wrong Company Name", "ocr_value": "Example Company Ltd", "status": "mismatched"}, "invoice_number": {"input_value": "INV-2024-002", "ocr_value": "INV-2024-001", "status": "mismatched"}, "invoice_date": {"input_value": "2024-01-16", "ocr_value": "15/01/2024", "status": "mismatched"}, "invoice_amount": {"input_value": "2000000", "ocr_value": "1,000,000.00", "status": "mismatched"}, "vat_amount": {"input_value": "200000", "ocr_value": "100,000.00", "status": "mismatched"}}, "summary": {"total_fields": 5, "matched": 0, "mismatched": 5, "not_found": 0}, "is_colored": true, "color_pages": [1], "total_pages": 1}, "focus": false}]}