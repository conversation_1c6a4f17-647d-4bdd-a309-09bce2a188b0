{"tests": [{"uuid": "test-air-liquide-tax-invoice-match-all", "input": {"file": "tax-invoice/air_liquide_faktur_pajak.pdf", "vendor_name": "PT. AIR LIQUIDE INDONESIA", "tax_invoice_number": "010.001-24.59355111", "tax_invoice_date": "2024-10-18", "invoice_amount": 4734000.0, "vat_amount": 520740.0}, "output": {"fields": {"vendor_name": {"input_value": "PT. AIR LIQUIDE INDONESIA", "ocr_value": "PT AIR LIQUIDE INDONESIA", "status": "matched"}, "tax_invoice_number": {"input_value": "010.001-24.59355111", "ocr_value": "010.001-24.59355111", "status": "matched"}, "tax_invoice_date": {"input_value": "2024-10-18", "ocr_value": "18 Oktober 2024", "status": "matched"}, "invoice_amount": {"input_value": "4734000", "ocr_value": "4.734.000,00", "status": "matched"}, "vat_amount": {"input_value": "520740", "ocr_value": "520.740,00", "status": "matched"}}, "summary": {"total_fields": 5, "matched": 5, "mismatched": 0, "not_found": 0}, "total_pages": 1}, "focus": false}, {"uuid": "test-air-liquide-tax-invoice-mismatch-all", "input": {"file": "tax-invoice/air_liquide_faktur_pajak.pdf", "vendor_name": "PT. AIR LIQUIDE INDONESIB", "tax_invoice_number": "010.001-24.59355112", "tax_invoice_date": "2024-10-19", "invoice_amount": 4734001.0, "vat_amount": 520741.0}, "output": {"fields": {"vendor_name": {"input_value": "PT. AIR LIQUIDE INDONESIB", "ocr_value": "PT AIR LIQUIDE INDONESIA", "status": "mismatched"}, "tax_invoice_number": {"input_value": "010.001-24.59355112", "ocr_value": "010.001-24.59355111", "status": "mismatched"}, "tax_invoice_date": {"input_value": "2024-10-19", "ocr_value": "18 Oktober 2024", "status": "mismatched"}, "invoice_amount": {"input_value": "4734001", "ocr_value": "4.734.000,00", "status": "mismatched"}, "vat_amount": {"input_value": "520741", "ocr_value": "520.740,00", "status": "mismatched"}}, "summary": {"total_fields": 5, "matched": 0, "mismatched": 5, "not_found": 0}, "total_pages": 1}, "focus": false}, {"uuid": "test-parker-hannifin-tax-invoice-match-all", "input": {"file": "tax-invoice/parker_faktur_pajak.pdf", "vendor_name": "PT PARKER HANNIFIN INDONESIA", "tax_invoice_number": "010.009-24.91687015", "tax_invoice_date": "2024-10-31", "invoice_amount": 6914140.0, "vat_amount": 760555.0}, "output": {"fields": {"vendor_name": {"input_value": "PT PARKER HANNIFIN INDONESIA", "ocr_value": "PT PARKER HANNIFIN INDONESIA", "status": "matched"}, "tax_invoice_number": {"input_value": "010.009-24.91687015", "ocr_value": "010.009-24.91687015", "status": "matched"}, "tax_invoice_date": {"input_value": "2024-10-31", "ocr_value": "31 Oktober 2024", "status": "matched"}, "invoice_amount": {"input_value": "6914140", "ocr_value": "6.914.140,00", "status": "matched"}, "vat_amount": {"input_value": "760555", "ocr_value": "760.555,00", "status": "matched"}}, "summary": {"total_fields": 5, "matched": 5, "mismatched": 0, "not_found": 0}, "total_pages": 1}, "focus": false}, {"uuid": "test-parker-hannifin-tax-invoice-mismatch-all", "input": {"file": "tax-invoice/parker_faktur_pajak.pdf", "vendor_name": "PT PARKER HANNIFIN INDONESIB", "tax_invoice_number": "010.009-24.91687016", "tax_invoice_date": "2024-10-30", "invoice_amount": 6914141.0, "vat_amount": 760556.0}, "output": {"fields": {"vendor_name": {"input_value": "PT PARKER HANNIFIN INDONESIB", "ocr_value": "PT PARKER HANNIFIN INDONESIA", "status": "mismatched"}, "tax_invoice_number": {"input_value": "010.009-24.91687016", "ocr_value": "010.009-24.91687015", "status": "mismatched"}, "tax_invoice_date": {"input_value": "2024-10-30", "ocr_value": "31 Oktober 2024", "status": "mismatched"}, "invoice_amount": {"input_value": "6914141", "ocr_value": "6.914.140,00", "status": "mismatched"}, "vat_amount": {"input_value": "760556", "ocr_value": "760.555,00", "status": "mismatched"}}, "summary": {"total_fields": 5, "matched": 0, "mismatched": 5, "not_found": 0}, "total_pages": 1}, "focus": false}, {"uuid": "test-packet-systems-tax-invoice-match-all", "input": {"file": "tax-invoice/psi_faktur_pajak.pdf", "vendor_name": "PT PACKET SYSTEMS INDONESIA", "tax_invoice_number": "010.009-24.83852180", "tax_invoice_date": "2024-10-16", "invoice_amount": 59816137.0, "vat_amount": 6579775.0}, "output": {"fields": {"vendor_name": {"input_value": "PT PACKET SYSTEMS INDONESIA", "ocr_value": "PT PACKET SYSTEMS INDONESIA", "status": "matched"}, "tax_invoice_number": {"input_value": "010.009-24.83852180", "ocr_value": "010.009-24.83852180", "status": "matched"}, "tax_invoice_date": {"input_value": "2024-10-16", "ocr_value": "16 Oktober 2024", "status": "matched"}, "invoice_amount": {"input_value": "59816137", "ocr_value": "59.816.137,00", "status": "matched"}, "vat_amount": {"input_value": "6579775", "ocr_value": "6.579.775,00", "status": "matched"}}, "summary": {"total_fields": 5, "matched": 5, "mismatched": 0, "not_found": 0}, "total_pages": 2}, "focus": false}, {"uuid": "test-packet-systems-tax-invoice-mismatch-all", "input": {"file": "tax-invoice/psi_faktur_pajak.pdf", "vendor_name": "PT PACKET SYSTEM INDONESIA", "tax_invoice_number": "010.009-24.83852181", "tax_invoice_date": "2024-10-17", "invoice_amount": 59816138.0, "vat_amount": 6579776.0}, "output": {"fields": {"vendor_name": {"input_value": "PT PACKET SYSTEM INDONESIA", "ocr_value": "PT PACKET SYSTEMS INDONESIA", "status": "mismatched"}, "tax_invoice_number": {"input_value": "010.009-24.83852181", "ocr_value": "010.009-24.83852180", "status": "mismatched"}, "tax_invoice_date": {"input_value": "2024-10-17", "ocr_value": "16 Oktober 2024", "status": "mismatched"}, "invoice_amount": {"input_value": "59816138", "ocr_value": "59.816.137,00", "status": "mismatched"}, "vat_amount": {"input_value": "6579776", "ocr_value": "6.579.775,00", "status": "mismatched"}}, "summary": {"total_fields": 5, "matched": 0, "mismatched": 5, "not_found": 0}, "total_pages": 2}, "focus": false}, {"uuid": "test-nipsea-paint-and-chemicals-tax-invoice-match-all", "input": {"file": "tax-invoice/taxdoc_TN07301_683_326073.pdf", "vendor_name": "PT NIPSEA PAINT AND CHEMICALS", "tax_invoice_number": "040.003-25.39318602", "tax_invoice_date": "2025-05-09", "invoice_amount": 15504000.0, "vat_amount": 1705440.0}, "output": {"fields": {"vendor_name": {"input_value": "PT NIPSEA PAINT AND CHEMICALS", "ocr_value": "PT NIPSEA PAINT AND CHEMICALS", "status": "matched"}, "tax_invoice_number": {"input_value": "040.003-25.39318602", "ocr_value": "040.003-25.39318602", "status": "matched"}, "tax_invoice_date": {"input_value": "2025-05-09", "ocr_value": "09 Mei 2025", "status": "matched"}, "invoice_amount": {"input_value": "15504000", "ocr_value": "15.504.000,00", "status": "matched"}, "vat_amount": {"input_value": "1705440", "ocr_value": "1.705.440,00", "status": "matched"}}, "summary": {"total_fields": 5, "matched": 5, "mismatched": 0, "not_found": 0}, "total_pages": 1}, "focus": false}, {"uuid": "test-nipsea-paint-and-chemicals-tax-invoice-mismatch-all", "input": {"file": "tax-invoice/taxdoc_TN07301_683_326073.pdf", "vendor_name": "PT NIPSEA PAINT AND CHEMICAES", "tax_invoice_number": "040.003-25.39318603", "tax_invoice_date": "2025-05-10", "invoice_amount": 15504001.0, "vat_amount": 1705441.0}, "output": {"fields": {"vendor_name": {"input_value": "PT NIPSEA PAINT AND CHEMICAES", "ocr_value": "PT NIPSEA PAINT AND CHEMICALS", "status": "mismatched"}, "tax_invoice_number": {"input_value": "040.003-25.39318603", "ocr_value": "040.003-25.39318602", "status": "mismatched"}, "tax_invoice_date": {"input_value": "2025-05-10", "ocr_value": "09 Mei 2025", "status": "mismatched"}, "invoice_amount": {"input_value": "15504001", "ocr_value": "15.504.000,00", "status": "mismatched"}, "vat_amount": {"input_value": "1705441", "ocr_value": "1.705.440,00", "status": "mismatched"}}, "summary": {"total_fields": 5, "matched": 0, "mismatched": 5, "not_found": 0}, "total_pages": 1}, "focus": false}]}