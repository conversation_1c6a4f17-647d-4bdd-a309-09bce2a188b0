import { vi } from 'vitest'
import { ColorDetectionResult } from '@/lib/api/enhanced-color-detection'

// Mock for enhanced color detection function
export const detectColorMock = vi.fn<[Buffer], Promise<ColorDetectionResult>>().mockResolvedValue({
  is_colored: false,
  color_pages: [],
  total_pages: 3,
  confidence: 'high',
  method: 'Pixel-by-Pixel RGB Analysis'
})

// Mock the enhanced color detection module
vi.mock('@/lib/api/enhanced-color-detection', () => ({
  detectColor: () => detectColorMock(),
}))

// Helper function to set the mock to return a colored document
export function mockColoredDocument(pages: number[] = [1]) {
  detectColorMock.mockResolvedValue({
    is_colored: true,
    color_pages: pages,
    total_pages: Math.max(...pages, 1),
    confidence: 'high',
    method: 'Pixel-by-Pixel RGB Analysis'
  })
}

// Helper function to set the mock to return a black and white document
export function mockBlackAndWhiteDocument(totalPages: number = 3) {
  detectColorMock.mockResolvedValue({
    is_colored: false,
    color_pages: [],
    total_pages: totalPages,
    confidence: 'high',
    method: 'Pixel-by-Pixel RGB Analysis'
  })
}

// Helper function to set the mock to simulate an error
export function mockColorDetectionError(errorMessage: string = 'Color detection failed') {
  detectColorMock.mockRejectedValue(new Error(errorMessage))
}
