import { http, HttpResponse } from 'msw'

// Mock responses for external API calls
export const handlers = [
  // Mock for external document processing service - invoice endpoint
  http.post('https://ca77-***********-75.ngrok-free.app/documents/invoice', () => {
    return HttpResponse.json({
      processing_id: 'mock-processing-id-123',
      document_processing_log_id: 'mock-document-processing-log-id-123',
      results: {
        success: true,
        summary: {
          total_fields: 5,
          matched: 3,
          mismatched: 1,
          not_found: 1
        },
        fields: {
          vendor_name: {
            input_value: 'Test Vendor',
            ocr_value: 'Test Vendor',
            status: 'matched'
          },
          invoice_number: {
            input_value: 'INV-123',
            ocr_value: 'INV-123',
            status: 'matched'
          },
          invoice_date: {
            input_value: '2023-01-01',
            ocr_value: '2023-01-01',
            status: 'matched'
          },
          invoice_amount: {
            input_value: '1000',
            ocr_value: '1000.00',
            status: 'mismatched'
          },
          vat_amount: {
            input_value: '100',
            ocr_value: '',
            status: 'not_found'
          }
        }
      }
    })
  }),

  // Mock for external document processing service - tax invoice endpoint
  http.post('https://ca77-***********-75.ngrok-free.app/documents/tax-invoice', () => {
    return HttpResponse.json({
      processing_id: 'mock-processing-id-456',
      document_processing_log_id: 'mock-document-processing-log-id-456',
      results: {
        success: true,
        summary: {
          total_fields: 5,
          matched: 4,
          mismatched: 1,
          not_found: 0
        },
        fields: {
          vendor_name: {
            input_value: 'Test Vendor',
            ocr_value: 'Test Vendor',
            status: 'matched'
          },
          tax_invoice_number: {
            input_value: 'TAX-123',
            ocr_value: 'TAX-123',
            status: 'matched'
          },
          tax_invoice_date: {
            input_value: '2023-01-01',
            ocr_value: '2023-01-01',
            status: 'matched'
          },
          invoice_amount: {
            input_value: '1000',
            ocr_value: '1000',
            status: 'matched'
          },
          vat_amount: {
            input_value: '100',
            ocr_value: '110',
            status: 'mismatched'
          }
        }
      }
    })
  })
]
