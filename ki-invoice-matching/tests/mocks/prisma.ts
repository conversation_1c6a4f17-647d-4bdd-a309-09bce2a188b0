import { vi } from 'vitest'

// Mock AdminUser model
const adminUserMock = {
  findUnique: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
}

// Mock ProcessingLog model
const processingLogMock = {
  create: vi.fn(),
  update: vi.fn(),
  findMany: vi.fn(),
  findUnique: vi.fn(),
  count: vi.fn(),
}

// Mock Prisma client
export const prismaMock = {
  adminUser: adminUserMock,
  processingLog: processingLogMock,
  $connect: vi.fn(),
  $disconnect: vi.fn(),
}

// Mock implementation for creating a processing log
processingLogMock.create.mockImplementation(async ({ data }) => {
  return {
    id: 'mock-log-id',
    ...data,
    requestTimestamp: new Date(),
  }
})

// Mock implementation for updating a processing log
processingLogMock.update.mockImplementation(async ({ where, data }) => {
  return {
    id: where.id,
    ...data,
    requestTimestamp: new Date(),
    processedAt: new Date(),
  }
})

// Mock implementation for finding processing logs
processingLogMock.findMany.mockImplementation(async () => {
  return [
    {
      id: 'mock-log-id-1',
      requestType: 'invoice',
      fileName: 'test1.pdf',
      fileSize: 1024,
      processingStatus: 'completed',
      requestTimestamp: new Date(),
      processedAt: new Date(),
    },
    {
      id: 'mock-log-id-2',
      requestType: 'tax_invoice',
      fileName: 'test2.pdf',
      fileSize: 2048,
      processingStatus: 'completed',
      requestTimestamp: new Date(),
      processedAt: new Date(),
    }
  ]
})

// Mock implementation for finding a single processing log
processingLogMock.findUnique.mockImplementation(async () => {
  return {
    id: 'mock-log-id',
    requestType: 'invoice',
    fileName: 'test.pdf',
    fileSize: 1024,
    processingStatus: 'completed',
    requestTimestamp: new Date(),
    processedAt: new Date(),
  }
})

// Mock implementation for counting processing logs
processingLogMock.count.mockImplementation(async () => {
  return 1
})

// Mock for Prisma client
vi.mock('@/lib/db', () => ({
  prisma: prismaMock,
  default: prismaMock,
}))
