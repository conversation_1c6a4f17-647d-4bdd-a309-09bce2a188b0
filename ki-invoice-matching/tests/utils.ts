import { ReadableStream } from 'stream/web'

/**
 * Creates a mock PDF file buffer for testing
 */
export function createMockPdfBuffer(): Buffer {
  return Buffer.from('Mock PDF content')
}

/**
 * Creates a mock File with arrayBuffer method
 */
export class MockFile extends File {
  constructor(content: BlobPart[], name: string, options?: FilePropertyBag) {
    super(content, name, options)
  }

  async arrayBuffer(): Promise<ArrayBuffer> {
    return new ArrayBuffer(8)
  }
}

/**
 * Creates a mock FormData object with a PDF file
 */
export function createMockFormData(fileName: string = 'test.pdf'): FormData {
  const formData = new FormData()
  const fileContent = createMockPdfBuffer()

  // Create a mock File object with arrayBuffer method
  const file = new MockFile([fileContent], fileName, { type: 'application/pdf' })

  formData.append('file', file)
  return formData
}

/**
 * Creates a mock invoice form data with all fields
 */
export function createMockInvoiceFormData(options: {
  fileName?: string
  vendorName?: string
  invoiceNo?: string
  invoiceDate?: string
  invoiceAmount?: number
  vatAmount?: number
} = {}): FormData {
  const formData = createMockFormData(options.fileName || 'invoice.pdf')

  if (options.vendorName) {
    formData.append('vendor_name', options.vendorName)
  }

  if (options.invoiceNo) {
    formData.append('invoice_number', options.invoiceNo)
  }

  if (options.invoiceDate) {
    formData.append('invoice_date', options.invoiceDate)
  }

  if (options.invoiceAmount) {
    formData.append('invoice_amount', options.invoiceAmount.toString())
  }

  if (options.vatAmount) {
    formData.append('vat_amount', options.vatAmount.toString())
  }

  return formData
}

/**
 * Creates a mock tax invoice form data with all fields
 */
export function createMockTaxInvoiceFormData(options: {
  fileName?: string
  vendorName?: string
  taxInvoiceNo?: string
  invoiceDate?: string
  invoiceAmount?: number
  vatAmount?: number
} = {}): FormData {
  const formData = createMockFormData(options.fileName || 'tax-invoice.pdf')

  if (options.vendorName) {
    formData.append('vendor_name', options.vendorName)
  }

  if (options.taxInvoiceNo) {
    formData.append('tax_invoice_number', options.taxInvoiceNo)
  }

  if (options.invoiceDate) {
    formData.append('tax_invoice_date', options.invoiceDate)
  }

  if (options.invoiceAmount) {
    formData.append('invoice_amount', options.invoiceAmount.toString())
  }

  if (options.vatAmount) {
    formData.append('vat_amount', options.vatAmount.toString())
  }

  return formData
}

/**
 * Mock implementation of NextRequest for testing API routes
 */
export class MockNextRequest implements Request {
  public readonly method: string
  public readonly url: string
  public readonly headers: Headers
  private formDataValue: FormData | null = null
  private bodyValue: ReadableStream<Uint8Array> | null = null

  constructor(method: string, url: string, options: {
    headers?: Record<string, string>,
    formData?: FormData,
    body?: ReadableStream<Uint8Array>
  } = {}) {
    this.method = method
    this.url = url
    this.headers = new Headers(options.headers || {})
    this.formDataValue = options.formData || null
    this.bodyValue = options.body || null
  }

  // Implement required Request interface methods
  get body(): ReadableStream<Uint8Array> | null {
    return this.bodyValue
  }

  async formData(): Promise<FormData> {
    if (!this.formDataValue) {
      throw new Error('No form data available')
    }
    return this.formDataValue
  }

  // Mock the File object with arrayBuffer method
  get[Symbol.toStringTag](): string {
    return 'MockNextRequest'
  }

  // Stub implementations for other required methods
  get bodyUsed(): boolean { return false }
  get cache(): RequestCache { return 'default' }
  get credentials(): RequestCredentials { return 'same-origin' }
  get destination(): RequestDestination { return '' }
  get integrity(): string { return '' }
  get keepalive(): boolean { return false }
  get mode(): RequestMode { return 'cors' }
  get redirect(): RequestRedirect { return 'follow' }
  get referrer(): string { return '' }
  get referrerPolicy(): ReferrerPolicy { return '' }
  get signal(): AbortSignal { return new AbortController().signal }

  async arrayBuffer(): Promise<ArrayBuffer> {
    return new ArrayBuffer(8)
  }
  async blob(): Promise<Blob> { return new Blob(['test']) }
  clone(): Request { return this }
  async json(): Promise<any> { return {} }
  async text(): Promise<string> { return 'test' }
}
