-- Create the processing_logs table first
CREATE TABLE "processing_logs" (
  "id" UUID NOT NULL,
  "request_type" TEXT NOT NULL,
  "request_timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "file_name" TEXT,
  "file_size" INTEGER,
  "processing_status" TEXT NOT NULL,
  "error_message" TEXT,
  "processing_id" TEXT,
  "input_vendor_name" TEXT,
  "ocr_vendor_name" TEXT,
  "input_document_no" TEXT,
  "ocr_document_no" TEXT,
  "input_document_date" TIMESTAMP(3),
  "ocr_document_date" TIMESTAMP(3),
  "input_invoice_amount" DOUBLE PRECISION,
  "ocr_invoice_amount" DOUBLE PRECISION,
  "input_vat_amount" DOUBLE PRECISION,
  "ocr_vat_amount" DOUBLE PRECISION,
  "is_colored" BOOLEAN,
  "matching_result" JSONB,
  "total_fields" INTEGER,
  "matched_fields" INTEGER,
  "mismatched_fields" INTEGER,
  "not_found_fields" INTEGER,
  "processed_at" TIMESTAMP(3),

  CONSTRAINT "processing_logs_pkey" PRIMARY KEY ("id")
);

-- Create indexes
CREATE INDEX "processing_log_request_type_idx" ON "processing_logs"("request_type");
CREATE INDEX "processing_log_processing_status_idx" ON "processing_logs"("processing_status");
CREATE INDEX "processing_log_request_timestamp_idx" ON "processing_logs"("request_timestamp");