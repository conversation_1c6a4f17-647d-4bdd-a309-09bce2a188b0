# Invoice Matching API Postman Collection

This directory contains Postman collection and environment files for the Invoice Matching API.

## Files

- `invoice-matching-api.json` - Postman collection with all API endpoints
- `invoice-matching-environment.json` - Postman environment with variables

## How to Use

1. Download both files from the admin dashboard or directly from this directory
2. Import the collection into Postman:
   - Open Postman
   - Click "Import" button
   - Select the downloaded `invoice-matching-api.json` file
3. Import the environment:
   - Click "Import" button again
   - Select the downloaded `invoice-matching-environment.json` file
4. Set up your environment:
   - Click on the environment dropdown in the top right corner
   - Select "Invoice Matching API Environment"
   - Click the "eye" icon to view environment variables
   - Update the `baseUrl` variable if needed (default: https://kim-staging.happyfresh.io)
5. Add your API key:
   - Create an API key in the admin dashboard if you don't have one
   - In each request, replace the placeholder API key in the X-API-Key header with your actual API key

## Available Endpoints

The collection includes the following endpoints:

### Invoice Processing
- **Process Invoice (URL)** - Process a standard invoice from a URL
- **Process Invoice File (Upload)** - Upload and process a standard invoice file

### Tax Invoice Processing
- **Process Tax Invoice (URL)** - Process a tax invoice from a URL
- **Process Tax Invoice File (Upload)** - Upload and process a tax invoice file

## Authentication

All API endpoints require authentication using an API key. The API key should be included in the request headers as `X-API-Key`.

Example:
```
X-API-Key: ki_12345678.your-api-key-secret
```

## Response Format

All API responses are returned in JSON format with a consistent structure. See the example responses in the collection for details.
