// Types for processing logs

import { ProcessingLog } from '@prisma/client';

// Log entry with formatted dates for display
export interface FormattedLogEntry extends Omit<ProcessingLog, 'requestTimestamp' | 'inputDocumentDate' | 'ocrDocumentDate' | 'processedAt'> {
  // Formatted date strings for display
  requestTimestamp: string;
  inputDocumentDate: string | null;
  ocrDocumentDate: string | null;
  processedAt: string | null;
  // Raw timestamps for calculations
  _rawRequestTimestamp?: Date;
  _rawProcessedAt?: Date;
  // Additional calculated fields
  matchedFieldsPercentage?: number | null;
  // API key information
  apiKeyName?: string | null;
}

// Filter options for logs
export interface LogFilterOptions {
  requestType?: string;
  processingStatus?: string;
  startDate?: Date;
  endDate?: Date;
  vendorName?: string;
  documentNo?: string;
}

// Pagination options
export interface PaginationOptions {
  page: number;
  pageSize: number;
}

// Sorted and filtered logs response
export interface LogsResponse {
  logs: FormattedLogEntry[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
