// Types for invoice processing

// Base invoice data that will be provided by the user
export interface BaseInvoiceData {
  vendorName?: string;
  invoiceDate?: string | Date;
  invoiceAmount?: number; // Renamed from totalAmount
}

// Standard invoice data
export interface InvoiceData extends BaseInvoiceData {
  invoiceNo?: string;
  vatAmount?: number; // Renamed from totalVatAmount
}

// Tax invoice data
export interface TaxInvoiceData extends BaseInvoiceData {
  taxInvoiceNo?: string;
  vatAmount?: number; // Renamed from totalVatAmount
}

// Field status in matching result
export type FieldStatus = 'matched' | 'mismatched' | 'not_found';

// Field matching result
export interface FieldMatch {
  input_value: string;
  ocr_value: string;
  ocr_value_normalized?: string;
  status: FieldStatus;
}

// Summary of matching results
export interface MatchingSummary {
  total_fields: number;
  matched: number;
  mismatched: number;
  not_found: number;
}

// Invoice matching result fields
export interface InvoiceMatchingFields {
  vendor_name: FieldMatch;
  invoice_number: FieldMatch;
  invoice_date: FieldMatch;
  invoice_amount: FieldMatch;
  vat_amount: FieldMatch;
}

// Tax invoice matching result fields
export interface TaxInvoiceMatchingFields {
  vendor_name: FieldMatch;
  tax_invoice_number: FieldMatch;
  tax_invoice_date: FieldMatch;
  invoice_amount: FieldMatch;
  vat_amount: FieldMatch;
}

// External service response format for invoice
export interface ExternalInvoiceResponse {
  processing_id: string;
  document_processing_log_id: string;
  page_count?: number;
  results: {
    success: boolean;
    summary: MatchingSummary;
    fields: InvoiceMatchingFields;
  };
}

// External service response format for tax invoice
export interface ExternalTaxInvoiceResponse {
  processing_id: string;
  document_processing_log_id: string;
  page_count?: number;
  results: {
    success: boolean;
    summary: MatchingSummary;
    fields: TaxInvoiceMatchingFields;
  };
}

// Extracted data from the invoice PDF
export interface ExtractedInvoiceData {
  vendorName: string;
  invoiceNo: string;
  invoiceDate: string;
  invoiceAmount: number; // Renamed from totalAmount
  vatAmount: number; // Renamed from totalVatAmount
}

// Extracted data from the tax invoice PDF
export interface ExtractedTaxInvoiceData {
  vendorName: string;
  taxInvoiceNo: string;
  invoiceDate: string;
  invoiceAmount: number; // Renamed from totalAmount
  vatAmount: number; // Renamed from totalVatAmount
}

// Result of matching process
export interface MatchingResult {
  success: boolean;
  summary: MatchingSummary;
  fields: InvoiceMatchingFields | TaxInvoiceMatchingFields;
  is_colored?: boolean; // Whether the PDF contains color
  color_pages?: number[]; // Array of page numbers that contain color
  total_pages?: number; // Total number of pages in the PDF
}

// API response format
export interface ApiResponse {
  processing_id: string;
  results: MatchingResult;
  error?: string;
  log_id?: string;
}
