import { Field<PERSON>atch, FieldStatus, MatchingSummary } from '@/lib/types/invoice';

export interface FieldMatchInput {
  input_value: string | number | undefined | null;
  ocr: any;
  field: string;
  type?: 'string' | 'number' | 'date';
}

function normalizeValue(value: any, type: 'string' | 'number' | 'date', field?: string): string {
  if (value === undefined || value === null) return '';

  if (type === 'number') {
    // Handle numeric values by removing thousand separators
    let numStr = value.toString().trim();

    // For Indonesian/European number formats, dots and commas can be thousand separators
    // We need to be smarter about detecting decimal vs thousand separators

    const lastDotIndex = numStr.lastIndexOf('.');
    const lastCommaIndex = numStr.lastIndexOf(',');

    let decimalPart = '';
    let integerPart = numStr;

    // Logic to determine if the last separator is decimal or thousand separator:
    // 1. If there are exactly 2 digits after the last separator, it's likely decimal
    // 2. If there are 3 digits after the last separator, it's likely thousand separator
    // 3. If there are 1 digit after, it could be either - check context

    if (lastDotIndex > lastCommaIndex && lastDotIndex > 0) {
      // Last dot is the rightmost separator
      const afterDot = numStr.substring(lastDotIndex + 1);

      if (afterDot.length === 2 && /^\d{2}$/.test(afterDot)) {
        // Exactly 2 digits after dot - likely decimal (e.g., "123.45")
        decimalPart = afterDot;
        integerPart = numStr.substring(0, lastDotIndex);
      } else if (afterDot.length === 3 && /^\d{3}$/.test(afterDot)) {
        // Exactly 3 digits after dot - likely thousand separator (e.g., "162.662")
        // Don't treat as decimal, keep as part of integer
        integerPart = numStr;
      } else if (afterDot.length === 1 && /^\d{1}$/.test(afterDot)) {
        // 1 digit after dot - could be decimal (e.g., "123.5")
        decimalPart = afterDot;
        integerPart = numStr.substring(0, lastDotIndex);
      }
      // If more than 3 digits or non-digits, treat entire string as integer part
    } else if (lastCommaIndex > lastDotIndex && lastCommaIndex > 0) {
      // Last comma is the rightmost separator
      const afterComma = numStr.substring(lastCommaIndex + 1);

      if (afterComma.length === 2 && /^\d{2}$/.test(afterComma)) {
        // Exactly 2 digits after comma - likely decimal (e.g., "123,45")
        decimalPart = afterComma;
        integerPart = numStr.substring(0, lastCommaIndex);
      } else if (afterComma.length === 3 && /^\d{3}$/.test(afterComma)) {
        // Exactly 3 digits after comma - likely thousand separator (e.g., "162,662")
        // Don't treat as decimal, keep as part of integer
        integerPart = numStr;
      } else if (afterComma.length === 1 && /^\d{1}$/.test(afterComma)) {
        // 1 digit after comma - could be decimal (e.g., "123,5")
        decimalPart = afterComma;
        integerPart = numStr.substring(0, lastCommaIndex);
      }
      // If more than 3 digits or non-digits, treat entire string as integer part
    }

    // Remove all thousand separators from integer part
    integerPart = integerPart.replace(/[,.]/g, '');

    // Reconstruct the number
    const normalizedNum = decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    return parseFloat(normalizedNum).toString();
  }

  if (type === 'date') {
    if (typeof value === 'string') return value;
    if (value instanceof Date) return value.toISOString().split('T')[0];
    return '';
  }

  let str = value.toString().trim().toLowerCase();
  // Special normalization for tax_invoice_number
  if (field === 'tax_invoice_number') {
    str = str.replace(/[ .-]/g, '');
  }
  return str;
}

function preprocessCompanyName(name: string): string {
  let processed = name.toLowerCase();

  // First, normalize common legal entity abbreviations before removing punctuation
  // Handle variations like "P.T.", "PT.", "P T", "PT" -> all become "pt"
  processed = processed.replace(/\bp\.?\s*t\.?\b/g, 'pt');
  processed = processed.replace(/\bc\.?\s*v\.?\b/g, 'cv');
  processed = processed.replace(/\bt\.?\s*b\.?\s*k\.?\b/g, 'tbk');

  // Remove punctuation (replace with space)
  processed = processed.replace(/[.,/#!$%^&*;:{}=\-_`~()]/g, ' ');

  // Remove legal forms at word boundaries
  processed = processed.replace(/\b(pt|cv|tbk|group|holdings)\b/g, ' ');

  // Remove extra spaces
  processed = processed.replace(/\s+/g, ' ').trim();
  return processed;
}

export function matchField({ input_value, ocr, field, type = 'string' }: FieldMatchInput): FieldMatch {
  const inputStr = input_value !== null && input_value !== undefined ? input_value.toString() : '';
  const normalizedInput = normalizeValue(input_value, type, field);
  const ocrValue = ocr?.ocr_value;

  // For date fields, use remote ocr_value_normalized as date normalization is complex
  // For other fields, normalize locally to ensure consistent handling of thousand separators
  let ocrNorm: string;
  if (type === 'date' && (field === 'invoice_date' || field === 'tax_invoice_date')) {
    ocrNorm = ocr?.ocr_value_normalized ?? normalizeValue(ocrValue, type, field);
  } else {
    ocrNorm = normalizeValue(ocrValue, type, field);
  }

  let status: FieldStatus = 'mismatched';
  if (!normalizedInput || ocrValue === 'N/A' || ocrNorm === 'N/A') {
    status = 'not_found';
  } else if (
    field === 'vendor_name'
      ? preprocessCompanyName(ocrNorm) === preprocessCompanyName(normalizedInput)
      : (type === 'number'
          ? Math.trunc(parseFloat(ocrNorm)) === Math.trunc(parseFloat(normalizedInput))
          : ocrNorm === normalizedInput)
  ) {
    status = 'matched';
  }

  // Debug logging for mismatched cases that should match
  if (status === 'mismatched' && type === 'string' && field !== 'vendor_name') {
    if (normalizedInput && ocrNorm && normalizedInput === ocrNorm) {
      console.warn(`[DEBUG] Field ${field} should match but marked as mismatched:`, {
        input_value: inputStr,
        ocr_value: ocrValue,
        normalized_input: normalizedInput,
        normalized_ocr: ocrNorm,
        input_chars: [...normalizedInput].map(c => c.charCodeAt(0)),
        ocr_chars: [...ocrNorm].map(c => c.charCodeAt(0))
      });
    }
  }

  return {
    ...ocr,
    input_value: inputStr,
    input_value_normalized: normalizedInput,
    status,
  };
}

export function matchFields(
  input: Record<string, any>,
  ocrFields: Record<string, any>,
  fieldTypes: Record<string, 'string' | 'number' | 'date'>
): { fields: Record<string, FieldMatch>; summary: MatchingSummary } {
  const fields: Record<string, FieldMatch> = {};
  let matched = 0, mismatched = 0, not_found = 0;
  for (const field in fieldTypes) {
    const match = matchField({
      input_value: input[field],
      ocr: ocrFields[field],
      field,
      type: fieldTypes[field],
    });
    fields[field] = match;
    if (match.status === 'matched') matched++;
    else if (match.status === 'mismatched') mismatched++;
    else if (match.status === 'not_found') not_found++;
  }
  const summary: MatchingSummary = {
    total_fields: Object.keys(fieldTypes).length,
    matched,
    mismatched,
    not_found,
  };
  return { fields, summary };
}
