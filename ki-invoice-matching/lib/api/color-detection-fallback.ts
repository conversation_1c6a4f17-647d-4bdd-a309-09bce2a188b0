/**
 * Fallback implementation of PDF color detection
 * This implementation doesn't rely on canvas and provides a simpler approach
 * that can be used when the canvas library is not available
 */

import { getDocument, GlobalWorkerOptions, OPS } from 'pdfjs-dist/legacy/build/pdf.mjs';
import { fileURLToPath, pathToFileURL } from 'url';
import * as path from 'path';
import * as fs from 'fs';

// Set up the worker source for PDF.js
try {
  // Try to find the worker script in various locations
  let workerSrcPath = '';

  // Check in node_modules relative to process.cwd()
  const possiblePaths = [
    path.join(process.cwd(), 'node_modules/pdfjs-dist/legacy/build/pdf.worker.mjs'),
    path.join(process.cwd(), '../node_modules/pdfjs-dist/legacy/build/pdf.worker.mjs'),
    path.join(process.cwd(), '../../node_modules/pdfjs-dist/legacy/build/pdf.worker.mjs')
  ];

  for (const possiblePath of possiblePaths) {
    if (fs.existsSync(possiblePath)) {
      workerSrcPath = possiblePath;
      break;
    }
  }

  if (workerSrcPath) {
    GlobalWorkerOptions.workerSrc = pathToFileURL(workerSrcPath).href;
  } else {
    console.warn('PDF.js worker script not found. PDF color detection may not work correctly.');
  }
} catch (error) {
  console.warn('Failed to set PDF.js worker source:', error);
}

// Define the color detection result interface
export interface ColorDetectionResult {
  is_colored: boolean;
  color_pages: number[];
  total_pages: number;
}

/**
 * A simplified color detection implementation that doesn't rely on canvas
 * Instead, it uses PDF.js to extract text and operator lists to make an educated guess
 * about whether the PDF contains color
 *
 * @param pdfBuffer The PDF file as a buffer
 * @returns Object containing color detection results
 */
export async function detectColor(pdfBuffer: Buffer): Promise<ColorDetectionResult> {
  let pdfDocument = null;
  let loadingTask;

  try {
    // Configure the loading task
    const loadingTaskConfig = {
      data: new Uint8Array(pdfBuffer),
      // Disable unnecessary features for faster loading
      disableAutoFetch: true,
      disableStream: true,
      disableRange: true,
      disableFontFace: true,
      ignoreErrors: true,
    };

    // Load the PDF document
    loadingTask = getDocument(loadingTaskConfig);
    pdfDocument = await loadingTask.promise;
    const numPages = pdfDocument.numPages;
    const colorPages: number[] = [];

    // Analyze each page
    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      try {
        const page = await pdfDocument.getPage(pageNum);

        // Get the operator list - this contains drawing commands
        const opList = await page.getOperatorList();

        // Check for color operators (simplified approach)
        // This looks for specific PDF operators that are typically used for color
        let hasColor = false;

        // Check for color operators in the fnArray
        for (let i = 0; i < opList.fnArray.length; i++) {
          const op = opList.fnArray[i];
          const args = opList.argsArray[i];

          // Check for setFillRGBColor, setStrokeRGBColor, etc.
          // These are common operators for setting color in PDFs
          if (
            (op === OPS.setFillRGBColor ||
             op === OPS.setStrokeRGBColor) &&
            args && args.length >= 3
          ) {
            // If the RGB values are not all equal, it's likely a color
            const [r, g, b] = args;
            if (r !== g || g !== b) {
              hasColor = true;
              break;
            }
          }

          // Check for other color operators
          if (
            op === OPS.setFillColorSpace ||
            op === OPS.setStrokeColorSpace
          ) {
            // If it's using a color space other than DeviceGray, it might be color
            if (args && args[0] !== 'DeviceGray') {
              hasColor = true;
              break;
            }
          }
        }

        if (hasColor) {
          colorPages.push(pageNum);
        }
      } catch (pageError) {
        console.error(`Error analyzing page ${pageNum}:`, pageError);
        // Continue with the next page
      }
    }

    return {
      is_colored: colorPages.length > 0,
      color_pages: colorPages,
      total_pages: numPages
    };
  } catch (error) {
    console.error('Error analyzing PDF:', error);
    // Return a default result if analysis fails
    return {
      is_colored: false,
      color_pages: [],
      total_pages: 0
    };
  } finally {
    // Clean up resources
    if (pdfDocument && typeof pdfDocument.destroy === 'function') {
      try {
        await pdfDocument.destroy();
      } catch (error) {
        console.error('Error destroying PDF document:', error);
      }
    }
  }
}
