import * as Sentry from '@sentry/nextjs';
import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse, InvoiceData, TaxInvoiceData } from '@/lib/types/invoice';
import { prisma } from '@/lib/db';
import { Prisma } from '@prisma/client';
import { detectColor } from '@/lib/api/enhanced-color-detection';
import { ColorDetectionResult } from '@/lib/api/enhanced-color-detection';
import formidable from 'formidable';
import { Readable } from 'stream';

/**
 * Creates a default error response for invoice processing
 * @param error The error that occurred
 * @param logId Optional log ID to include in the response
 * @returns A formatted error response
 */
export function createErrorResponse(error: unknown, logId?: string): ApiResponse {
  const errorMessage = error instanceof Error ? error.message :
                      typeof error === 'string' ? error : 'Unknown error';

  // Create a basic error response with only the required field attributes
  const errorResponse: ApiResponse = {
    processing_id: `error-${Date.now()}`,
    results: {
      success: false,
      summary: {
        total_fields: 0,
        matched: 0,
        mismatched: 0,
        not_found: 0
      },
      fields: {
        vendor_name: { input_value: '', ocr_value: '', status: 'not_found' },
        invoice_number: { input_value: '', ocr_value: '', status: 'not_found' },
        invoice_date: { input_value: '', ocr_value: '', status: 'not_found' },
        invoice_amount: { input_value: '', ocr_value: '', status: 'not_found' },
        vat_amount: { input_value: '', ocr_value: '', status: 'not_found' }
      }
    },
    error: errorMessage,
  };

  if (logId) {
    errorResponse.log_id = logId;
  }

  return errorResponse;
}

/**
 * Creates a default error response for tax invoice processing
 * @param error The error that occurred
 * @param logId Optional log ID to include in the response
 * @returns A formatted error response
 */
export function createTaxInvoiceErrorResponse(error: unknown, logId?: string): ApiResponse {
  const errorMessage = error instanceof Error ? error.message :
                      typeof error === 'string' ? error : 'Unknown error';

  // Create a basic error response with only the required field attributes
  const errorResponse: ApiResponse = {
    processing_id: `error-${Date.now()}`,
    results: {
      success: false,
      summary: {
        total_fields: 0,
        matched: 0,
        mismatched: 0,
        not_found: 0
      },
      fields: {
        vendor_name: { input_value: '', ocr_value: '', status: 'not_found' },
        tax_invoice_number: { input_value: '', ocr_value: '', status: 'not_found' },
        tax_invoice_date: { input_value: '', ocr_value: '', status: 'not_found' },
        invoice_amount: { input_value: '', ocr_value: '', status: 'not_found' },
        vat_amount: { input_value: '', ocr_value: '', status: 'not_found' }
      }
    },
    error: errorMessage,
  };

  if (logId) {
    errorResponse.log_id = logId;
  }

  return errorResponse;
}

/**
 * Parses a date string into a Date object or returns null if invalid
 * @param dateStr The date string to parse
 * @returns A Date object or null
 */
export function parseDate(dateStr: string): Date | null {
  try {
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    return null;
  }
}

/**
 * Parses an OCR date from the results
 * @param dateValue The date string from OCR results
 * @returns A Date object or null if invalid
 */
export function parseOcrDate(dateValue: string): Date | null {
  try {
    const ocrDate = new Date(dateValue);
    if (isNaN(ocrDate.getTime())) {
      return null;
    }
    return ocrDate;
  } catch (error) {
    return null;
  }
}

/**
 * Handles color detection for invoice PDFs
 * @param buffer The PDF file as a buffer
 * @returns Color detection results or default values if detection fails
 */
export async function handleColorDetection(buffer: Buffer) {
  const wrapperStartTime = Date.now();
  console.log(`[Color Detection Wrapper] Starting color detection wrapper for PDF (${(buffer.length / 1024).toFixed(2)} KB)`);
  
  return await Sentry.startSpan(
    {
      op: 'processing.color_detection_wrapper',
      name: 'Handle Color Detection',
      attributes: {
        'pdf.size_bytes': buffer.length,
      },
    },
    async (span) => {
      try {
        const colorDetection = await detectColor(buffer);
        const wrapperTime = Date.now() - wrapperStartTime;

        span.setAttribute('detection.success', true);
        span.setAttribute('detection.is_colored', colorDetection.is_colored);
        span.setAttribute('detection.total_pages', colorDetection.total_pages);
        span.setAttribute('detection.color_pages_count', colorDetection.color_pages.length);
        span.setAttribute('detection.confidence', colorDetection.confidence || 'high');
        span.setAttribute('detection.method', colorDetection.method || 'Enhanced ImageMagick');
        span.setStatus({ code: 1, message: 'Success' });

        console.log(`[Color Detection Wrapper] Color detection wrapper completed in ${wrapperTime}ms`);
        return colorDetection;
      } catch (colorError) {
        // Capture error in Sentry
        Sentry.captureException(colorError, {
          tags: {
            operation: 'color_detection_wrapper',
          },
          extra: {
            pdf_size: buffer.length,
          },
        });

        span.setStatus({ code: 2, message: colorError instanceof Error ? colorError.message : 'Unknown error' });
        span.setAttribute('detection.success', false);
        span.setAttribute('error.type', colorError instanceof Error ? colorError.constructor.name : 'unknown');
        span.setAttribute('error.message', colorError instanceof Error ? colorError.message : 'Unknown error');

        const wrapperErrorTime = Date.now() - wrapperStartTime;
        console.error(`[Color Detection Wrapper] Error in color detection after ${wrapperErrorTime}ms:`, colorError);

        // Provide default values if color detection fails
        const defaultResult = {
          is_colored: false,
          color_pages: [],
          total_pages: 0
        };

        span.setAttribute('detection.fallback_used', true);
        return defaultResult;
      }
    }
  );
}

/**
 * Creates a log entry for invoice processing
 * @param requestType The type of request ('invoice' or 'taxInvoice')
 * @param fileName The name of the file being processed
 * @param fileSize The size of the file in bytes
 * @param inputData The input data for matching
 * @param apiKeyId Optional API key ID used for the request
 * @returns The created log entry
 */
export async function createProcessingLog(
  requestType: 'invoice' | 'taxInvoice',
  fileName: string,
  fileSize: number,
  inputData: InvoiceData | TaxInvoiceData,
  apiKeyId?: string
) {
  return await Sentry.startSpan(
    {
      op: 'db.create',
      name: 'Create Processing Log',
      attributes: {
        'log.request_type': requestType,
        'log.file_name': fileName,
        'log.file_size': fileSize,
        'log.has_api_key': !!apiKeyId,
      },
    },
    async (span) => {
      try {
        // Parse the document date
        let documentDate: Date | null = null;
        if (inputData.invoiceDate) {
          documentDate = typeof inputData.invoiceDate === 'string'
            ? parseDate(inputData.invoiceDate)
            : inputData.invoiceDate;
        }

        // Create a log entry
        const log = await prisma.processingLog.create({
          data: {
            requestType,
            fileName,
            fileSize,
            processingStatus: 'processing',
            // Store input values in the fields
            inputVendorName: inputData.vendorName,
            inputDocumentNo: requestType === 'invoice'
              ? (inputData as InvoiceData).invoiceNo
              : (inputData as TaxInvoiceData).taxInvoiceNo,
            inputDocumentDate: documentDate,
            inputInvoiceAmount: inputData.invoiceAmount,
            inputVatAmount: inputData.vatAmount,
          },
        });

        span.setAttribute('log.id', log.id);
        span.setAttribute('log.created_successfully', true);
        span.setStatus({ code: 1, message: 'Success' });

        // Verify if the API key ID was saved correctly
        if (apiKeyId) {
          const savedLog = await prisma.processingLog.findUnique({
            where: { id: log.id },
            select: { apiKeyId: true }
          });
          span.setAttribute('log.api_key_verification', !!savedLog?.apiKeyId);
        }

        return log;
      } catch (error) {
        // Capture error in Sentry
        Sentry.captureException(error, {
          tags: {
            operation: 'create_processing_log',
          },
          extra: {
            request_type: requestType,
            file_name: fileName,
            file_size: fileSize,
            input_data: inputData,
          },
        });

        span.setStatus({ code: 2, message: error instanceof Error ? error.message : 'Unknown error' });
        span.setAttribute('error.type', error instanceof Error ? error.constructor.name : 'unknown');
        span.setAttribute('error.message', error instanceof Error ? error.message : 'Unknown error');

        throw error;
      }
    }
  );
}

/**
 * Creates a log entry for URL-based invoice processing
 * @param requestType The type of request ('invoice' or 'taxInvoice')
 * @param fileUrl The URL of the file being processed
 * @param inputData The input data for matching
 * @param apiKeyId Optional API key ID used for the request
 * @returns The created log entry
 */
export async function createUrlProcessingLog(
  requestType: 'invoice' | 'taxInvoice',
  fileUrl: string,
  inputData: InvoiceData | TaxInvoiceData,
  apiKeyId?: string
) {
  // Parse the document date
  let documentDate: Date | null = null;
  if (inputData.invoiceDate) {
    documentDate = typeof inputData.invoiceDate === 'string'
      ? parseDate(inputData.invoiceDate)
      : inputData.invoiceDate;
  }

  // Extract filename from URL if possible
  const fileName = fileUrl.split('/').pop() || 'remote-file.pdf';

  // Create a log entry
  const log = await prisma.processingLog.create({
    data: {
      requestType,
      fileName,
      fileSize: 0, // We don't know the file size for URL-based processing
      processingStatus: 'processing',
      // Store input values in the fields
      inputVendorName: inputData.vendorName,
      inputDocumentNo: requestType === 'invoice'
        ? (inputData as InvoiceData).invoiceNo
        : (inputData as TaxInvoiceData).taxInvoiceNo,
      inputDocumentDate: documentDate,
      inputInvoiceAmount: inputData.invoiceAmount,
      inputVatAmount: inputData.vatAmount,
    },
  });

  // Verify if the API key ID was saved correctly
  if (apiKeyId) {
    const savedLog = await prisma.processingLog.findUnique({
      where: { id: log.id },
      select: { apiKeyId: true }
    });
  }

  return log;
}

/**
 * Updates a log entry with error information
 * @param logId The ID of the log entry to update
 * @param error The error that occurred
 */
export async function updateLogWithError(logId: string, error: unknown) {
  await prisma.processingLog.update({
    where: { id: logId },
    data: {
      processingStatus: 'failed',
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
    },
  });
}

/**
 * Updates a processing log with invoice results using raw SQL
 * @param logId The ID of the log entry to update
 * @param externalResponse The response from the external service
 * @param colorDetection The color detection results
 * @param results The combined results
 */
export async function updateInvoiceLogWithResults(
  logId: string,
  externalResponse: any,
  colorDetection: ColorDetectionResult,
  results: any
) {
  // Parse OCR date
  const ocrDocumentDate = parseOcrDate(results.fields.invoice_date.ocr_value);

  // Helper to format date for SQL Server DATETIME2 (YYYY-MM-DD HH:MM:SS.FFF)
  const formatSqlServerDateTime = (date: Date | string) => {
    const d = typeof date === 'string' ? new Date(date) : date;
    const pad = (num: number) => num.toString().padStart(2, '0');
    const year = d.getUTCFullYear();
    const month = pad(d.getUTCMonth() + 1);
    const day = pad(d.getUTCDate());
    const hours = pad(d.getUTCHours());
    const minutes = pad(d.getUTCMinutes());
    const seconds = pad(d.getUTCSeconds());
    const ms = d.getUTCMilliseconds().toString().padStart(3, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${ms}`;
  };

  // Extract summary statistics
  const summary = results.summary;

  try {
    // Validate date before formatting
    if (ocrDocumentDate && !(ocrDocumentDate instanceof Date)) {
      throw new Error('Invalid document date - must be a Date object');
    }

    // Update the log with the results
    const result = await prisma.$executeRaw`
    UPDATE "processing_logs"
    SET
      "processing_status" = 'success',
      "processing_id" = ${externalResponse.document_processing_log_id},
      "is_colored" = ${colorDetection.is_colored},
      "color_pages" = CAST(${JSON.stringify(colorDetection.color_pages)} AS NVARCHAR(MAX)),
      "total_pages" = ${results.total_pages},
      "matching_result" = CAST(${JSON.stringify(results)} AS NVARCHAR(MAX)),
      "ocr_vendor_name" = ${results.fields.vendor_name.ocr_value},
      "ocr_document_no" = ${results.fields.invoice_number.ocr_value},
      "ocr_document_date" = ${ocrDocumentDate ? Prisma.sql`CONVERT(DATETIME2, ${formatSqlServerDateTime(ocrDocumentDate)})` : Prisma.sql`NULL`},
      "ocr_invoice_amount" = ${!isNaN(parseFloat(results.fields.invoice_amount.ocr_value)) ? parseFloat(results.fields.invoice_amount.ocr_value) : Prisma.sql`NULL`},
      "ocr_vat_amount" = ${!isNaN(parseFloat(results.fields.vat_amount.ocr_value)) ? parseFloat(results.fields.vat_amount.ocr_value) : Prisma.sql`NULL`},
      "total_fields" = ${summary.total_fields},
      "matched_fields" = ${summary.matched},
      "mismatched_fields" = ${summary.mismatched},
      "not_found_fields" = ${summary.not_found},
      "processed_at" = CONVERT(DATETIME2, ${formatSqlServerDateTime(new Date().toISOString())})
    WHERE "id" = ${logId}
    `;
  } catch (error) {
    console.error(`[ERROR] Failed to update invoice log ${logId}:`, error);
    throw error;
  }
}

/**
 * Updates a processing log with tax invoice results using Prisma client
 * @param logId The ID of the log entry to update
 * @param externalResponse The response from the external service
 * @param results The results
 */
export async function updateTaxInvoiceLogWithResults(
  logId: string,
  externalResponse: any,
  results: any
) {
  // Parse OCR date
  const ocrDocumentDate = parseOcrDate(results.fields.tax_invoice_date.ocr_value);

  // Extract summary statistics
  const summary = results.summary;

  // Create update data object
  const updateData: any = {
    processingStatus: 'success',
    processingId: externalResponse.document_processing_log_id,
    matchingResult: JSON.stringify(results),
    // Store OCR values in the new fields
    ocrVendorName: results.fields.vendor_name.ocr_value,
    ocrDocumentNo: results.fields.tax_invoice_number.ocr_value,
    ocrDocumentDate: ocrDocumentDate,
    ocrInvoiceAmount: parseFloat(results.fields.invoice_amount.ocr_value),
    ocrVatAmount: parseFloat(results.fields.vat_amount.ocr_value),
    // Statistics
    totalFields: summary.total_fields,
    matchedFields: summary.matched,
    mismatchedFields: summary.mismatched,
    notFoundFields: summary.not_found,
    // Set processedAt to current time
    processedAt: new Date().toISOString(),
  };

  // Add total_pages if available in the results
  if (results.total_pages !== undefined) {
    updateData.totalPages = results.total_pages;
  }

  // Update the log with the results
  await prisma.processingLog.update({
    where: { id: logId },
    data: updateData,
  });
}

/**
 * Extracts page_count from the external service response if available
 * @param externalResponse The response from the external service
 * @returns The page_count value or null if not available
 */
export function extractTotalPages(externalResponse: any): number | null {
  // Check if the external response contains page_count at root level
  if (externalResponse && externalResponse.page_count !== undefined) {
    return externalResponse.page_count;
  }

  return null;
}


/**
 * Filters field matching results to only include the required attributes
 * @param fields The field matching results to filter
 * @returns Filtered field matching results
 */
export function filterFieldMatchingResults(fields: any): any {
  const filteredFields: any = {};

  // Process each field in the matching results
  for (const [key, field] of Object.entries(fields)) {
    // Only keep the three required attributes for each field
    filteredFields[key] = {
      input_value: (field as any).input_value,
      ocr_value: (field as any).ocr_value,
      status: (field as any).status
    };
  }

  return filteredFields;
}

/**
 * Updates the API key ID for a processing log
 * @param logId The ID of the log entry to update
 * @param apiKeyId The API key ID to set
 */
export async function updateLogWithApiKeyId(logId: string, apiKeyId: string): Promise<void> {
  try {
    // First try using Prisma client update
    await prisma.processingLog.update({
      where: { id: logId },
      data: { apiKeyId }
    });

    // Verify the update
    const updatedLog = await prisma.processingLog.findUnique({
      where: { id: logId },
      select: { apiKeyId: true }
    });

    // If the update didn't work, try with raw SQL as a fallback
    if (!updatedLog?.apiKeyId) {
      await prisma.$executeRaw`
        UPDATE "processing_logs"
        SET "api_key_id" = ${apiKeyId}::uuid
        WHERE "id" = ${logId}::uuid
      `;

      // Verify the raw SQL update
      const updatedLogAfterRawSql = await prisma.processingLog.findUnique({
        where: { id: logId },
        select: { apiKeyId: true }
      });
    }
  } catch (error) {
    console.error('Error updating API key ID:', error);

    // Try with raw SQL as a last resort if Prisma update failed
    try {
      await prisma.$executeRaw`
        UPDATE "processing_logs"
        SET "api_key_id" = ${apiKeyId}::uuid
        WHERE "id" = ${logId}::uuid
      `;

      // Verify the raw SQL update
      const updatedLogAfterRawSql = await prisma.processingLog.findUnique({
        where: { id: logId },
        select: { apiKeyId: true }
      });

    } catch (sqlError) {
      console.error('Error updating API key ID with raw SQL:', sqlError);
    }
  }
}

/**
 * Creates a standard API response
 * @param externalResponse The response from the external service
 * @param logId The ID of the log entry
 * @param results The results to include in the response
 * @returns A formatted API response
 */
export function createApiResponse(
  externalResponse: any,
  logId: string,
  results: any
): ApiResponse {
  // Create a copy of the results to avoid modifying the original
  const filteredResults = { ...results };

  // Filter the fields to only include the required attributes
  if (filteredResults.fields) {
    filteredResults.fields = filterFieldMatchingResults(filteredResults.fields);
  }

  return {
    processing_id: externalResponse.processing_id,
    results: filteredResults,
    log_id: logId,
  };
}

/**
 * Parses multipart form data using Formidable library
 * @param req The NextRequest object
 * @returns Promise resolving to parsed fields and files
 */
export async function parseFormDataWithFormidable(req: NextRequest): Promise<{
  fields: Record<string, string>;
  files: Record<string, { buffer: Buffer; filename: string; mimetype: string; size: number }>;
}> {
  return new Promise(async (resolve, reject) => {
    try {
      // Check content type
      const contentType = req.headers.get('content-type');

      if (!contentType || !contentType.includes('multipart/form-data')) {
        console.error('Invalid content type:', contentType);
        throw new Error(`Invalid content type: ${contentType}. Expected multipart/form-data.`);
      }

      // Get the raw body as buffer
      const arrayBuffer = await req.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Create a readable stream from the buffer
      const stream = new Readable({
        read() {
          this.push(buffer);
          this.push(null); // End the stream
        }
      });

      // Set up headers for the stream
      (stream as any).headers = Object.fromEntries(req.headers.entries());

      // Create formidable instance
      const form = formidable({
        maxFileSize: 50 * 1024 * 1024, // 50MB limit
        allowEmptyFiles: false,
        keepExtensions: true,
      });

      // Parse the form data
      form.parse(stream as any, (err, fields, files) => {
        if (err) {
          console.error('Formidable parsing error:', err);
          reject(new Error(`Formidable parsing failed: ${err.message}`));
          return;
        }

        try {
          // Convert fields to simple string values
          const parsedFields: Record<string, string> = {};
          for (const [key, value] of Object.entries(fields)) {
            if (Array.isArray(value)) {
              parsedFields[key] = value[0] || '';
            } else {
              parsedFields[key] = value || '';
            }
          }

          // Convert files to buffer-based objects
          const parsedFiles: Record<string, { buffer: Buffer; filename: string; mimetype: string; size: number }> = {};
          for (const [key, value] of Object.entries(files)) {
            let file: formidable.File | undefined;
            if (Array.isArray(value) && value.length > 0) {
              file = value[0];
            } else if (value && !Array.isArray(value)) {
              file = value;
            }

            if (file && file.filepath) {
              // Read the file content into a buffer
              const fs = require('fs');
              const fileBuffer = fs.readFileSync(file.filepath);

              parsedFiles[key] = {
                buffer: fileBuffer,
                filename: file.originalFilename || 'unknown',
                mimetype: file.mimetype || 'application/octet-stream',
                size: file.size
              };

              // Clean up the temporary file
              try {
                fs.unlinkSync(file.filepath);
              } catch (cleanupError) {
                console.warn('Failed to cleanup temp file:', cleanupError);
              }
            } else {
              console.log(`File ${key} has no filepath or is invalid`);
            }
          }
          resolve({ fields: parsedFields, files: parsedFiles });
        } catch (processingError) {
          reject(new Error(`Error processing parsed data: ${processingError}`));
        }
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Form data parsing error:', {
        error: errorMessage,
        contentType: req.headers.get('content-type'),
        method: req.method,
        url: req.url
      });
      reject(new Error(`Failed to parse multipart form data: ${errorMessage}`));
    }
  });
}
