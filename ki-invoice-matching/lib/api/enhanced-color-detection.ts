/**
 * Enhanced PDF color detection implementation
 * Based on the standalone script with improvements for handling pages with large white spaces
 */

import * as Sentry from '@sentry/nextjs';
import * as fs from 'fs';
import * as path from 'path';
import { exec, ExecException } from 'child_process';
import { promisify } from 'util';
import * as os from 'os';

// Define the color detection result interface
export interface ColorDetectionResult {
  is_colored: boolean;
  color_pages: number[];
  total_pages: number;
  confidence?: string;
  method?: string;
}

// Promisify exec for async/await usage
const execAsync = promisify(exec);

/**
 * Gets the total number of pages in a PDF without extracting all pages
 * Uses ImageMagick identify command for efficiency
 *
 * @param tempPdfPath Path to the temporary PDF file
 * @param usingImageMagickV7 Whether to use ImageMagick v7 commands
 * @param magickCommandWorks Whether magick command is available
 * @param convertCommandWorks Whether convert command is available
 * @returns Promise resolving to total page count
 */
async function getTotalPageCount(
  tempPdfPath: string,
  usingImageMagickV7: boolean,
  magickCommandWorks: boolean,
  convertCommandWorks: boolean
): Promise<number> {
  try {
    let identifyCmd: string;
    if (usingImageMagickV7 && magickCommandWorks) {
      identifyCmd = `magick identify "${tempPdfPath}"`;
    } else if (convertCommandWorks) {
      identifyCmd = `identify "${tempPdfPath}"`;
    } else {
      throw new Error('No working ImageMagick commands available');
    }

    const { stdout } = await execAsync(identifyCmd);
    const lines = stdout.trim().split('\n');
    return lines.length;
  } catch (error: any) {
    console.error('Error getting page count:', error.message);
    // Fallback: try to extract first page only to get some indication
    return 1;
  }
}

/**
 * Detects if a PDF has color by analyzing only the first and last pages
 * Uses ImageMagick for high-accuracy pixel-by-pixel RGB analysis
 * Optimized for performance on multi-page PDFs
 *
 * @param pdfBuffer The PDF file as a buffer
 * @returns Promise resolving to color detection results
 */
export async function detectColor(pdfBuffer: Buffer): Promise<ColorDetectionResult> {
  const startTime = Date.now();
  console.log(`[Color Detection] Starting color detection for PDF (${(pdfBuffer.length / 1024).toFixed(2)} KB)`);
  
  return await Sentry.startSpan(
    {
      op: 'processing.color_detection',
      name: 'PDF Color Detection',
      attributes:
        {
          'pdf.size_bytes': pdfBuffer.length,
        },
    },
    async (span) => {
      try {
        // Try ImageMagick first for high-accuracy pixel-by-pixel RGB analysis
        console.log("Attempting color detection with ImageMagick implementation...");
        span.setAttribute('detection.method', 'ImageMagick');

        // Create a temporary directory for processing
        const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'pdf-color-'));
        const tempPdfPath = path.join(tempDir, 'document.pdf');
        fs.writeFileSync(tempPdfPath, pdfBuffer);
        span.setAttribute('temp.directory', tempDir);

        // Detect ImageMagick version and capabilities
        let usingImageMagickV7 = false;
        let convertCommandWorks = false;
        let magickCommandWorks = false;

        // First check if 'convert' command works
        try {
          const { stdout: versionOutput } = await execAsync('convert -version');
          convertCommandWorks = true;
          if (versionOutput.includes('ImageMagick 7')) {
            usingImageMagickV7 = true;
          }
        } catch (error: any) {
          console.log("'convert' command not available or failed:", error.message);
        }

        // Then check if 'magick' command works
        try {
          const { stdout: versionOutput } = await execAsync('magick -version');
          magickCommandWorks = true;
          usingImageMagickV7 = true;
        } catch (error: any) {
          console.log("'magick' command not available or failed:", error.message);
        }

        // Log the detection results
        console.log(`ImageMagick detection results: convert=${convertCommandWorks}, magick=${magickCommandWorks}, v7=${usingImageMagickV7}`);

        if (!convertCommandWorks && !magickCommandWorks) {
          console.error("No working ImageMagick commands found. Color detection may not work properly.");
        }

        if (usingImageMagickV7 && magickCommandWorks) {
        } else if (convertCommandWorks) {
          usingImageMagickV7 = false;
        } else if (magickCommandWorks) {
          usingImageMagickV7 = true;
        }

        // Get total page count first
        const pageCountStartTime = Date.now();
        const totalPages = await getTotalPageCount(tempPdfPath, usingImageMagickV7, magickCommandWorks, convertCommandWorks);
        const pageCountTime = Date.now() - pageCountStartTime;
        console.log(`[Color Detection] PDF has ${totalPages} pages. Page count took ${pageCountTime}ms. Analyzing first and last pages for optimized color detection.`);

        // Determine which pages to extract (first and last page only)
        const pagesToExtract: number[] = [];
        if (totalPages === 1) {
          pagesToExtract.push(1);
        } else {
          // Extract first and last page only
          pagesToExtract.push(1, totalPages);
        }

        // Extract only the specific pages from the PDF
        let extractionSuccess = false;
        let extractionError = null;
        const extractionStartTime = Date.now();

        // Method 1: Try ImageMagick with appropriate command based on detection
        try {
          for (const pageNum of pagesToExtract) {
            const pageIndex = pageNum - 1; // ImageMagick uses 0-based indexing
            // Use lower resolution (36 DPI instead of 72) for faster processing
            // Add timeout of 10 seconds to prevent hanging on complex pages
            if (usingImageMagickV7 && magickCommandWorks) {
              await execAsync(`magick -density 36 "${tempPdfPath}[${pageIndex}]" -quality 80 "${tempDir}/page-${pageIndex}.jpg"`, { timeout: 10000 });
            } else if (convertCommandWorks) {
              await execAsync(`convert -density 36 "${tempPdfPath}[${pageIndex}]" -quality 80 "${tempDir}/page-${pageIndex}.jpg"`, { timeout: 10000 });
            }
          }
          extractionSuccess = true;
          const extractionTime = Date.now() - extractionStartTime;
          console.log(`[Color Detection] Page extraction completed in ${extractionTime}ms`);
        } catch (error: any) {
          extractionError = error;
          console.error('Error with primary extraction method:', error.message);
        }

        // Method 2: If first method failed, try with Ghostscript directly
        if (!extractionSuccess) {
          try {
            console.log("Attempting PDF extraction with Ghostscript directly...");
            await execAsync('gs --version');
            for (const pageNum of pagesToExtract) {
              await execAsync(`gs -dSAFER -dBATCH -dNOPAUSE -sDEVICE=jpeg -r72 -dTextAlphaBits=4 -dGraphicsAlphaBits=4 -dJPEGQ=90 -dFirstPage=${pageNum} -dLastPage=${pageNum} -sOutputFile="${tempDir}/page-${pageNum - 1}.jpg" "${tempPdfPath}"`);
            }
            extractionSuccess = true;
          } catch (gsError: any) {
            console.error('Error with Ghostscript extraction:', gsError.message);
          }
        }

        // Method 3: If both methods failed, try with pdftoppm if available
        if (!extractionSuccess) {
          try {
            console.log("Attempting PDF extraction with pdftoppm...");
            await execAsync('pdftoppm -v');
            for (const pageNum of pagesToExtract) {
              await execAsync(`pdftoppm -jpeg -r 72 -f ${pageNum} -l ${pageNum} "${tempPdfPath}" "${tempDir}/page"`);
            }
            // pdftoppm creates files with a different naming pattern, so rename them to match expected pattern
            const files = fs.readdirSync(tempDir).filter(file => file.endsWith('.jpg'));
            files.forEach(file => {
              const match = file.match(/page-(\d+)\.jpg/);
              if (match) {
                const pageNum = parseInt(match[1]);
                const pageIndex = pageNum - 1;
                if (pagesToExtract.includes(pageNum)) {
                  fs.renameSync(
                    path.join(tempDir, file),
                    path.join(tempDir, `page-${pageIndex}.jpg`)
                  );
                }
              }
            });
            extractionSuccess = true;
          } catch (pdfToPpmError: any) {
            console.error('Error with pdftoppm extraction:', pdfToPpmError.message);
          }
        }

        if (!extractionSuccess) {
          console.error('All PDF extraction methods failed');
          try {
            fs.rmSync(tempDir, { recursive: true, force: true });
          } catch (cleanupError: any) {
            console.error('Error cleaning up temporary directory:', cleanupError);
          }
          return {
            is_colored: false,
            color_pages: [],
            total_pages: 0,
            confidence: 'low',
            method: 'Failed extraction'
          };
        }

        const files = fs.readdirSync(tempDir)
          .filter(file => file.startsWith('page-') && file.endsWith('.jpg'))
          .sort((a, b) => {
            const pageA = parseInt(a.match(/page-(\d+)/)?.[1] || '0');
            const pageB = parseInt(b.match(/page-(\d+)/)?.[1] || '0');
            return pageA - pageB;
          });

        const colorPages: number[] = [];
        const analysisStartTime = Date.now();
        console.log(`[Color Detection] Starting pixel analysis for ${files.length} pages`);

        // Analyze each page
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const rawPageNum = parseInt(file.match(/page-(\d+)/)?.[1] || '0');
          const pageNum = rawPageNum + 1;
          const fileStats = fs.statSync(path.join(tempDir, file));
          const fileSizeKB = fileStats.size / 1024;

          let hasNonWhiteContent = false;
          try {
            const contentCheckCmd = usingImageMagickV7
              ? `magick "${path.join(tempDir, file)}" -colorspace RGB -fill white -fuzz 1% -opaque white -fill black -opaque black -format "%[fx:mean]" info:`
              : `convert "${path.join(tempDir, file)}" -colorspace RGB -fill white -fuzz 1% -opaque white -fill black -opaque black -format "%[fx:mean]" info:`;
            const { stdout: contentValueRaw } = await execAsync(contentCheckCmd);
            const contentValue = parseFloat(contentValueRaw.trim());
            hasNonWhiteContent = contentValue > 0;
          } catch (error: any) {
            hasNonWhiteContent = true;
          }

          let pixelData = '';
          let pixelDataSuccess = false;
          try {
            // Use smaller sample size (400x400 instead of 800x800) and sample fewer pixels (every 20th instead of every 10th)
            // for faster processing while maintaining reasonable accuracy
            const pixelDataCmd = usingImageMagickV7
              ? `magick "${path.join(tempDir, file)}" -colorspace RGB -trim +repage -sample 400x400 txt:- | grep -v "^#" | awk 'NR % 20 == 0' | head -n 5000`
              : `convert "${path.join(tempDir, file)}" -colorspace RGB -trim +repage -sample 400x400 txt:- | grep -v "^#" | awk 'NR % 20 == 0' | head -n 5000`;
            const { stdout: pixelDataRaw } = await execAsync(pixelDataCmd, { maxBuffer: 10 * 1024 * 1024 });
            pixelData = pixelDataRaw;
            pixelDataSuccess = pixelData.trim().length > 0;
          } catch (error: any) {
            console.error(`Error with pixel data extraction for page ${pageNum}:`, error);
          }

          if (hasNonWhiteContent && !pixelDataSuccess) {
            pixelData = "0,0: (240,240,240) #F0F0F0 gray(240)\n1,0: (240,240,240) #F0F0F0 gray(240)\n";
            pixelDataSuccess = true;
          }

          const pixelLines = pixelData.trim().split('\n');
          let totalPixels = 0;
          let colorPixels = 0;

          for (const line of pixelLines) {
            const match = line.match(/\((\d+),(\d+),(\d+)\)/);
            if (match) {
              const r = parseInt(match[1]);
              const g = parseInt(match[2]);
              const b = parseInt(match[3]);
              if ((r === 255 && g === 255 && b === 255) || (r === 0 && g === 0 && b === 0)) {
                continue;
              }
              totalPixels++;
              const avg = (r + g + b) / 3;
              const threshold = Math.max(2.55, avg * 0.01);
              const isColorByAvg =
                Math.abs(r - avg) > threshold ||
                Math.abs(g - avg) > threshold ||
                Math.abs(b - avg) > threshold;
              const isColorByDiff =
                Math.abs(r - g) > threshold ||
                Math.abs(r - b) > threshold ||
                Math.abs(g - b) > threshold;
              if (isColorByAvg || isColorByDiff) {
                colorPixels++;
              }
            }
          }

          const colorPercentage = totalPixels > 0 ? (colorPixels / totalPixels) * 100 : 0;

          // Handle the case where no pixels were analyzed
          if (totalPixels === 0) {
            console.log(`Warning: No pixels analyzed for page ${pageNum}. Using alternative detection methods.`);
            // Method 1: Check if the file size is significant
            if (fileSizeKB > 20) {
              // Method 2: Try to detect color using ImageMagick's identify command
              try {
                const colorspaceCmd = usingImageMagickV7
                  ? `magick identify -format "%[colorspace]" "${path.join(tempDir, file)}"`
                  : `identify -format "%[colorspace]" "${path.join(tempDir, file)}"`;
                const { stdout: colorspaceRaw } = await execAsync(colorspaceCmd);
                const colorspace = colorspaceRaw.trim();
                // If the colorspace is not Gray/Grayscale, it might be a color image
                if (colorspace !== 'Gray' && colorspace !== 'Grayscale' && colorspace !== 'sRGB') {
                  colorPages.push(pageNum);
                  continue;
                }
              } catch (error: any) {
                console.error(`Error checking colorspace for page ${pageNum}:`, error);
              }
              // If the file is significantly large (>100KB), it's more likely to contain color
              if (fileSizeKB > 100) {
                colorPages.push(pageNum);
                continue;
              }
            }
          }

          // A page is considered colored if:
          // 1. More than 0.75% of the sampled pixels are colored (slightly higher threshold for better accuracy with fewer samples), AND
          // 2. We have at least 3 colored pixels (reduced from 5 since we're sampling fewer pixels)
          const isColored = colorPercentage > 0.75 && colorPixels >= 3;
          if (isColored) {
            colorPages.push(pageNum);
          }
        }

        const analysisTime = Date.now() - analysisStartTime;
        console.log(`[Color Detection] Pixel analysis completed in ${analysisTime}ms`);

        try {
          fs.rmSync(tempDir, { recursive: true, force: true });
        } catch (cleanupError: any) {
          console.error('Error cleaning up temporary directory:', cleanupError);
        }

        // Return the results
        const totalTime = Date.now() - startTime;
        const result = {
          is_colored: colorPages.length > 0,
          color_pages: colorPages,
          total_pages: totalPages, // Use actual total pages, not just analyzed pages
          confidence: 'high',
          method: 'Optimized Pixel-by-Pixel RGB Analysis (Key Pages)'
        };

        span.setAttribute('detection.confidence', 'high');
        span.setAttribute('detection.total_pages', totalPages);
        span.setAttribute('detection.analyzed_pages', files.length);
        span.setAttribute('detection.is_colored', result.is_colored);
        span.setAttribute('detection.color_pages_count', colorPages.length);
        span.setStatus({ code: 1, message: 'Success with ImageMagick' });

        console.log(`[Color Detection] ImageMagick detection completed in ${totalTime}ms - Result: ${result.is_colored ? 'COLORED' : 'GRAYSCALE'} (${colorPages.length}/${totalPages} pages colored)`);
        return result;
      } catch (imagemagickError: any) {
        console.error("ImageMagick color detection failed, falling back to PDF.js:", imagemagickError);
        span.setAttribute('imagemagick.error', imagemagickError instanceof Error ? imagemagickError.message : 'Unknown error');

        // Try PDF.js as fallback
        try {
          const { detectColor: detectColorFallback } = require('./color-detection-fallback');

          // Try the PDF.js-based implementation
          try {
            console.log("Attempting color detection with PDF.js fallback implementation...");
            const result = await Sentry.startSpan(
              {
                op: 'processing.color_detection.pdfjs',
                name: 'PDF.js Color Detection',
              },
              async () => await detectColorFallback(pdfBuffer)
            );

            const fallbackTime = Date.now() - startTime;
            console.log(`[Color Detection] PDF.js fallback completed in ${fallbackTime}ms - Result: ${result.is_colored ? 'COLORED' : 'GRAYSCALE'}`);
            span.setAttribute('detection.method', 'PDF.js');
            span.setAttribute('detection.confidence', 'medium');
            span.setAttribute('detection.total_pages', result.total_pages || 0);
            span.setAttribute('detection.is_colored', result.is_colored || false);
            span.setAttribute('detection.color_pages_count', result.color_pages?.length || 0);
            span.setStatus({ code: 1, message: 'Success with PDF.js fallback' });

            return {
              ...result,
              confidence: 'medium',
              method: 'PDF.js Operator Analysis'
            };
          } catch (pdfJsError) {
            console.error("PDF.js fallback also failed:", pdfJsError);
            span.setAttribute('pdfjs.error', pdfJsError instanceof Error ? pdfJsError.message : 'Unknown error');
          }
        } catch (importError) {
          const importErrorMsg = importError instanceof Error ? importError.message : String(importError);
          console.log("PDF.js fallback implementation not available:", importErrorMsg);
          span.setAttribute('pdfjs.import_error', importErrorMsg);
        }

        // Both methods failed - capture error in Sentry
        Sentry.captureException(imagemagickError, {
          tags:
            {
              operation: 'color_detection',
              method: 'both_failed',
            },
          extra:
            {
              pdf_size: pdfBuffer.length,
            },
        });

        span.setStatus({ code: 2, message: imagemagickError instanceof Error ? imagemagickError.message : 'Unknown error' });
        span.setAttribute('error.type', imagemagickError instanceof Error ? imagemagickError.constructor.name : 'unknown');
        span.setAttribute('error.message', imagemagickError instanceof Error ? imagemagickError.message : 'Unknown error');

        const errorTime = Date.now() - startTime;
        console.error(`[Color Detection] Both methods failed after ${errorTime}ms:`, imagemagickError);

        // Return a default result if both implementations fail
        return {
          is_colored: false,
          color_pages: [],
          total_pages: 0,
          confidence: 'low',
          method: 'Error'
        };
      }
    }
  );
}
