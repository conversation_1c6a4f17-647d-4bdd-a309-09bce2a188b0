/**
 * Enhanced PDF color detection implementation
 * Based on the standalone script with improvements for handling pages with large white spaces
 */

import * as Sentry from '@sentry/nextjs';
import * as fs from 'fs';
import * as path from 'path';
import { exec, ExecException } from 'child_process';
import { promisify } from 'util';
import * as os from 'os';

// Define the color detection result interface
export interface ColorDetectionResult {
  is_colored: boolean;
  color_pages: number[];
  total_pages: number;
  confidence?: string;
  method?: string;
}

// Promisify exec for async/await usage
const execAsync = promisify(exec);

/**
 * Detects if a PDF has color and which pages contain color
 * Uses ImageMagick for high-accuracy pixel-by-pixel RGB analysis
 *
 * @param pdfBuffer The PDF file as a buffer
 * @returns Promise resolving to color detection results
 */
export async function detectColor(pdfBuffer: Buffer): Promise<ColorDetectionResult> {
  return await Sentry.startSpan(
    {
      op: 'processing.color_detection',
      name: 'PDF Color Detection',
      attributes:
        {
          'pdf.size_bytes': pdfBuffer.length,
        },
    },
    async (span) => {
      try {
        // Try ImageMagick first for high-accuracy pixel-by-pixel RGB analysis
        console.log("Attempting color detection with ImageMagick implementation...");
        span.setAttribute('detection.method', 'ImageMagick');

        // Create a temporary directory for processing
        const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'pdf-color-'));
        const tempPdfPath = path.join(tempDir, 'document.pdf');
        fs.writeFileSync(tempPdfPath, pdfBuffer);
        span.setAttribute('temp.directory', tempDir);

        // Detect ImageMagick version and capabilities
        let usingImageMagickV7 = false;
        let convertCommandWorks = false;
        let magickCommandWorks = false;

        // First check if 'convert' command works
        try {
          const { stdout: versionOutput } = await execAsync('convert -version');
          convertCommandWorks = true;
          if (versionOutput.includes('ImageMagick 7')) {
            usingImageMagickV7 = true;
          }
        } catch (error: any) {
          console.log("'convert' command not available or failed:", error.message);
        }

        // Then check if 'magick' command works
        try {
          const { stdout: versionOutput } = await execAsync('magick -version');
          magickCommandWorks = true;
          usingImageMagickV7 = true;
        } catch (error: any) {
          console.log("'magick' command not available or failed:", error.message);
        }

        // Log the detection results
        console.log(`ImageMagick detection results: convert=${convertCommandWorks}, magick=${magickCommandWorks}, v7=${usingImageMagickV7}`);

        if (!convertCommandWorks && !magickCommandWorks) {
          console.error("No working ImageMagick commands found. Color detection may not work properly.");
        }

        if (usingImageMagickV7 && magickCommandWorks) {
        } else if (convertCommandWorks) {
          usingImageMagickV7 = false;
        } else if (magickCommandWorks) {
          usingImageMagickV7 = true;
        }

        // Extract images from the PDF with multiple fallback methods
        let extractionSuccess = false;
        let extractionError = null;

        // Method 1: Try ImageMagick with appropriate command based on detection
        try {
          if (usingImageMagickV7 && magickCommandWorks) {
            await execAsync(`magick -density 72 "${tempPdfPath}" -quality 90 "${tempDir}/page-%d.jpg"`);
            extractionSuccess = true;
          } else if (convertCommandWorks) {
            await execAsync(`convert -density 72 "${tempPdfPath}" -quality 90 "${tempDir}/page-%d.jpg"`);
            extractionSuccess = true;
          }
        } catch (error: any) {
          extractionError = error;
          console.error('Error with primary extraction method:', error.message);
        }

        // Method 2: If first method failed, try with Ghostscript directly
        if (!extractionSuccess) {
          try {
            console.log("Attempting PDF extraction with Ghostscript directly...");
            await execAsync('gs --version');
            await execAsync(`gs -dSAFER -dBATCH -dNOPAUSE -sDEVICE=jpeg -r72 -dTextAlphaBits=4 -dGraphicsAlphaBits=4 -dJPEGQ=90 -sOutputFile="${tempDir}/page-%d.jpg" "${tempPdfPath}"`);
            extractionSuccess = true;
          } catch (gsError: any) {
            console.error('Error with Ghostscript extraction:', gsError.message);
          }
        }

        // Method 3: If both methods failed, try with pdftoppm if available
        if (!extractionSuccess) {
          try {
            console.log("Attempting PDF extraction with pdftoppm...");
            await execAsync('pdftoppm -v');
            await execAsync(`pdftoppm -jpeg -r 72 "${tempPdfPath}" "${tempDir}/page"`);
            // pdftoppm creates files with a different naming pattern, so rename them to match expected pattern
            const ppmFiles = fs.readdirSync(tempDir).filter(file => file.startsWith('page-') && file.endsWith('.jpg'));
            if (ppmFiles.length === 0) {
              const files = fs.readdirSync(tempDir).filter(file => file.endsWith('.jpg'));
              files.forEach(file => {
                const match = file.match(/page-(\d+)\.jpg/);
                if (match) {
                  const pageNum = parseInt(match[1]) - 1;
                  fs.renameSync(
                    path.join(tempDir, file),
                    path.join(tempDir, `page-${pageNum}.jpg`)
                  );
                }
              });
            }
            extractionSuccess = true;
          } catch (pdfToPpmError: any) {
            console.error('Error with pdftoppm extraction:', pdfToPpmError.message);
          }
        }

        if (!extractionSuccess) {
          console.error('All PDF extraction methods failed');
          try {
            fs.rmSync(tempDir, { recursive: true, force: true });
          } catch (cleanupError: any) {
            console.error('Error cleaning up temporary directory:', cleanupError);
          }
          return {
            is_colored: false,
            color_pages: [],
            total_pages: 0,
            confidence: 'low',
            method: 'Failed extraction'
          };
        }

        const files = fs.readdirSync(tempDir)
          .filter(file => file.startsWith('page-') && file.endsWith('.jpg'))
          .sort((a, b) => {
            const pageA = parseInt(a.match(/page-(\d+)/)?.[1] || '0');
            const pageB = parseInt(b.match(/page-(\d+)/)?.[1] || '0');
            return pageA - pageB;
          });

        const colorPages: number[] = [];

        // Analyze each page
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const rawPageNum = parseInt(file.match(/page-(\d+)/)?.[1] || '0');
          const pageNum = rawPageNum + 1;
          const fileStats = fs.statSync(path.join(tempDir, file));
          const fileSizeKB = fileStats.size / 1024;

          let hasNonWhiteContent = false;
          try {
            const contentCheckCmd = usingImageMagickV7
              ? `magick "${path.join(tempDir, file)}" -colorspace RGB -fill white -fuzz 1% -opaque white -fill black -opaque black -format "%[fx:mean]" info:`
              : `convert "${path.join(tempDir, file)}" -colorspace RGB -fill white -fuzz 1% -opaque white -fill black -opaque black -format "%[fx:mean]" info:`;
            const { stdout: contentValueRaw } = await execAsync(contentCheckCmd);
            const contentValue = parseFloat(contentValueRaw.trim());
            hasNonWhiteContent = contentValue > 0;
          } catch (error: any) {
            hasNonWhiteContent = true;
          }

          let pixelData = '';
          let pixelDataSuccess = false;
          try {
            const pixelDataCmd = usingImageMagickV7
              ? `magick "${path.join(tempDir, file)}" -colorspace RGB -trim +repage -sample 800x800 txt:- | grep -v "^#" | awk 'NR % 10 == 0' | head -n 10000`
              : `convert "${path.join(tempDir, file)}" -colorspace RGB -trim +repage -sample 800x800 txt:- | grep -v "^#" | awk 'NR % 10 == 0' | head -n 10000`;
            const { stdout: pixelDataRaw } = await execAsync(pixelDataCmd, { maxBuffer: 10 * 1024 * 1024 });
            pixelData = pixelDataRaw;
            pixelDataSuccess = pixelData.trim().length > 0;
          } catch (error: any) {
            console.error(`Error with pixel data extraction for page ${pageNum}:`, error);
          }

          if (hasNonWhiteContent && !pixelDataSuccess) {
            pixelData = "0,0: (240,240,240) #F0F0F0 gray(240)\n1,0: (240,240,240) #F0F0F0 gray(240)\n";
            pixelDataSuccess = true;
          }

          const pixelLines = pixelData.trim().split('\n');
          let totalPixels = 0;
          let colorPixels = 0;

          for (const line of pixelLines) {
            const match = line.match(/\((\d+),(\d+),(\d+)\)/);
            if (match) {
              const r = parseInt(match[1]);
              const g = parseInt(match[2]);
              const b = parseInt(match[3]);
              if ((r === 255 && g === 255 && b === 255) || (r === 0 && g === 0 && b === 0)) {
                continue;
              }
              totalPixels++;
              const avg = (r + g + b) / 3;
              const threshold = Math.max(2.55, avg * 0.01);
              const isColorByAvg =
                Math.abs(r - avg) > threshold ||
                Math.abs(g - avg) > threshold ||
                Math.abs(b - avg) > threshold;
              const isColorByDiff =
                Math.abs(r - g) > threshold ||
                Math.abs(r - b) > threshold ||
                Math.abs(g - b) > threshold;
              if (isColorByAvg || isColorByDiff) {
                colorPixels++;
              }
            }
          }

          const colorPercentage = totalPixels > 0 ? (colorPixels / totalPixels) * 100 : 0;

          // Handle the case where no pixels were analyzed
          if (totalPixels === 0) {
            console.log(`Warning: No pixels analyzed for page ${pageNum}. Using alternative detection methods.`);
            // Method 1: Check if the file size is significant
            if (fileSizeKB > 20) {
              // Method 2: Try to detect color using ImageMagick's identify command
              try {
                const colorspaceCmd = usingImageMagickV7
                  ? `magick identify -format "%[colorspace]" "${path.join(tempDir, file)}"`
                  : `identify -format "%[colorspace]" "${path.join(tempDir, file)}"`;
                const { stdout: colorspaceRaw } = await execAsync(colorspaceCmd);
                const colorspace = colorspaceRaw.trim();
                // If the colorspace is not Gray/Grayscale, it might be a color image
                if (colorspace !== 'Gray' && colorspace !== 'Grayscale' && colorspace !== 'sRGB') {
                  colorPages.push(pageNum);
                  continue;
                }
              } catch (error: any) {
                console.error(`Error checking colorspace for page ${pageNum}:`, error);
              }
              // If the file is significantly large (>100KB), it's more likely to contain color
              if (fileSizeKB > 100) {
                colorPages.push(pageNum);
                continue;
              }
            }
          }

          // A page is considered colored if:
          // 1. More than 0.5% of the sampled pixels are colored, AND
          // 2. We have at least 5 colored pixels (to avoid false positives from noise)
          const isColored = colorPercentage > 0.5 && colorPixels >= 5;
          if (isColored) {
            colorPages.push(pageNum);
          }
        }

        try {
          fs.rmSync(tempDir, { recursive: true, force: true });
        } catch (cleanupError: any) {
          console.error('Error cleaning up temporary directory:', cleanupError);
        }

        // Return the results
        const result = {
          is_colored: colorPages.length > 0,
          color_pages: colorPages,
          total_pages: files.length,
          confidence: 'high',
          method: 'Pixel-by-Pixel RGB Analysis'
        };

        span.setAttribute('detection.confidence', 'high');
        span.setAttribute('detection.total_pages', files.length);
        span.setAttribute('detection.is_colored', result.is_colored);
        span.setAttribute('detection.color_pages_count', colorPages.length);
        span.setStatus({ code: 1, message: 'Success with ImageMagick' });

        return result;
      } catch (imagemagickError: any) {
        console.error("ImageMagick color detection failed, falling back to PDF.js:", imagemagickError);
        span.setAttribute('imagemagick.error', imagemagickError instanceof Error ? imagemagickError.message : 'Unknown error');

        // Try PDF.js as fallback
        try {
          const { detectColor: detectColorFallback } = require('./color-detection-fallback');

          // Try the PDF.js-based implementation
          try {
            console.log("Attempting color detection with PDF.js fallback implementation...");
            const result = await Sentry.startSpan(
              {
                op: 'processing.color_detection.pdfjs',
                name: 'PDF.js Color Detection',
              },
              async () => await detectColorFallback(pdfBuffer)
            );

            console.log("PDF.js color detection successful:", result);
            span.setAttribute('detection.method', 'PDF.js');
            span.setAttribute('detection.confidence', 'medium');
            span.setAttribute('detection.total_pages', result.total_pages || 0);
            span.setAttribute('detection.is_colored', result.is_colored || false);
            span.setAttribute('detection.color_pages_count', result.color_pages?.length || 0);
            span.setStatus({ code: 1, message: 'Success with PDF.js fallback' });

            return {
              ...result,
              confidence: 'medium',
              method: 'PDF.js Operator Analysis'
            };
          } catch (pdfJsError) {
            console.error("PDF.js fallback also failed:", pdfJsError);
            span.setAttribute('pdfjs.error', pdfJsError instanceof Error ? pdfJsError.message : 'Unknown error');
          }
        } catch (importError) {
          const importErrorMsg = importError instanceof Error ? importError.message : String(importError);
          console.log("PDF.js fallback implementation not available:", importErrorMsg);
          span.setAttribute('pdfjs.import_error', importErrorMsg);
        }

        // Both methods failed - capture error in Sentry
        Sentry.captureException(imagemagickError, {
          tags:
            {
              operation: 'color_detection',
              method: 'both_failed',
            },
          extra:
            {
              pdf_size: pdfBuffer.length,
            },
        });

        span.setStatus({ code: 2, message: imagemagickError instanceof Error ? imagemagickError.message : 'Unknown error' });
        span.setAttribute('error.type', imagemagickError instanceof Error ? imagemagickError.constructor.name : 'unknown');
        span.setAttribute('error.message', imagemagickError instanceof Error ? imagemagickError.message : 'Unknown error');

        console.error('Both ImageMagick and PDF.js color detection failed:', imagemagickError);

        // Return a default result if both implementations fail
        return {
          is_colored: false,
          color_pages: [],
          total_pages: 0,
          confidence: 'low',
          method: 'Error'
        };
      }
    }
  );
}
