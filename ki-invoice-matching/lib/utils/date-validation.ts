/**
 * Validates if a string is a valid date in YYYY-MM-DD format
 * @param dateStr Date string to validate
 * @returns Object containing validation result and error message if invalid
 */
export function validateDate(dateStr: string): { isValid: boolean; error?: string } {
  // Check format
  if (!dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
    return {
      isValid: false,
      error: 'Date must be in YYYY-MM-DD format'
    };
  }

  // Parse the date parts
  const [year, month, day] = dateStr.split('-').map(Number);

  // Create date object and verify parts
  const date = new Date(year, month - 1, day);

  // Verify each part matches what we set
  // This catches invalid dates like 2024-13-31
  if (
    date.getFullYear() !== year ||
    date.getMonth() !== month - 1 ||
    date.getDate() !== day ||
    isNaN(date.getTime())
  ) {
    return {
      isValid: false,
      error: 'Invalid date - please provide a valid date'
    };
  }

  return { isValid: true };
}