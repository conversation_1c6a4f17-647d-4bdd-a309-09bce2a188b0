const bcrypt = require("bcryptjs");
const { PrismaClient } = require("@prisma/client");
const readline = require("readline");

const prisma = new PrismaClient();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function resetAdminPassword() {
  console.log("Reset Admin Password");
  console.log("====================");

  const email = await new Promise(resolve => {
    rl.question("Admin Email: ", resolve);
  });

  // Check if user exists
  const user = await prisma.adminUser.findUnique({
    where: { email }
  });

  if (!user) {
    console.error(`User with email ${email} not found`);
    rl.close();
    await prisma.$disconnect();
    return;
  }

  console.log(`Found user: ${user.name} (${user.email})`);

  const newPassword = await new Promise(resolve => {
    rl.question("New Password: ", resolve);
  });

  try {
    // Using bcryptjs to hash the password
    const passwordHash = await bcrypt.hash(newPassword, 10);

    const updatedUser = await prisma.adminUser.update({
      where: { email },
      data: { passwordHash }
    });

    console.log(`Password reset successfully for: ${updatedUser.email}`);
  } catch (error) {
    console.error("Error resetting password:", error);
  } finally {
    rl.close();
    await prisma.$disconnect();
  }
}

resetAdminPassword();