const { PrismaClient } = require("@prisma/client");
const bcrypt = require("bcryptjs");

const DEFAULT_ADMIN = {
  name: "Default Admin",
  email: "<EMAIL>",
  password: "kim2025",
  role: "admin"
};

async function seedDefaultAdmin() {
  const prisma = new PrismaClient();

  try {
    console.log("Checking for existing admin user...");
    
    // Check if the default admin already exists
    const existingAdmin = await prisma.adminUser.findUnique({
      where: { email: DEFAULT_ADMIN.email }
    });

    if (existingAdmin) {
      console.log(`Default admin user already exists: ${DEFAULT_ADMIN.email}`);
      return;
    }

    console.log("Creating default admin user...");
    
    // Hash the password
    const passwordHash = await bcrypt.hash(DEFAULT_ADMIN.password, 10);

    // Create the default admin user
    const adminUser = await prisma.adminUser.create({
      data: {
        name: DEFAULT_ADMIN.name,
        email: DEFAULT_ADMIN.email,
        passwordHash,
        role: DEFAULT_ADMIN.role
      }
    });

    console.log(`✅ Default admin user created successfully!`);
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Password: ${DEFAULT_ADMIN.password}`);
    console.log(`   Please change the password after first login.`);
    
  } catch (error) {
    console.error("❌ Error creating default admin user:", error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
if (require.main === module) {
  seedDefaultAdmin()
    .then(() => {
      console.log("Seeding completed.");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Seeding failed:", error);
      process.exit(1);
    });
}

module.exports = { seedDefaultAdmin };