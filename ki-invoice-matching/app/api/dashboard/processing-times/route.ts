import { NextRequest, NextResponse } from 'next/server';
import { startOfDay, endOfDay, isValid } from 'date-fns';
import { prisma } from '@/lib/db';
import { ProcessingTimeData } from '@/types/dashboard';

interface RawMetric {
  day: Date;
  p50: number;
  p95: number;
  p99: number;
  count: number;
}

interface RawDocumentTypeMetric {
  day: Date;
  request_type: string;
  medianTime: number;
}

interface ProcessedDocumentTypeMetric {
  day: string;
  timestamp: string;
  standardInvoice: number;
  taxInvoice: number;
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');

    // Validate date parameters
    if (!startDateParam || !endDateParam) {
      return NextResponse.json(
        { error: 'Missing required date parameters' },
        { status: 400 }
      );
    }

    // Parse and validate dates
    const startDate = new Date(startDateParam);
    const endDate = new Date(endDateParam);

    if (!isValid(startDate) || !isValid(endDate)) {
      return NextResponse.json(
        { error: 'Invalid date format' },
        { status: 400 }
      );
    }

    // Ensure correct date range
    const adjustedStartDate = startOfDay(startDate);
    const adjustedEndDate = endOfDay(endDate);

    // Log for debugging
    console.log('Processing times request:', {
      startDate: adjustedStartDate.toISOString(),
      endDate: adjustedEndDate.toISOString()
    });

    const [overallMetrics, documentTypeMetrics] = await Promise.all([
      getOverallMetrics(adjustedStartDate, adjustedEndDate),
      getDocumentTypeMetrics(adjustedStartDate, adjustedEndDate)
    ]);

    // Group document type metrics by day
    const groupedByDay = (documentTypeMetrics as RawDocumentTypeMetric[]).reduce((acc, metric) => {
      const dayKey = metric.day.toISOString().split('T')[0];
      if (!acc[dayKey]) {
        acc[dayKey] = {
          day: dayKey,
          timestamp: metric.day.toISOString(),
          standardInvoice: 0,
          taxInvoice: 0
        };
      }

      // Map request_type to chart keys
      if (metric.request_type === 'invoice') {
        acc[dayKey].standardInvoice = metric.medianTime;
      } else if (metric.request_type === 'taxInvoice') {
        acc[dayKey].taxInvoice = metric.medianTime;
      }

      return acc;
    }, {} as Record<string, ProcessedDocumentTypeMetric>);

    const response: ProcessingTimeData = {
      overall: (overallMetrics as RawMetric[]).map(metric => ({
        ...metric,
        timestamp: metric.day.toISOString(),
        day: metric.day.toISOString().split('T')[0],
      })),
      byDocumentType: Object.values(groupedByDay)
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching processing times:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function getOverallMetrics(fromDate: Date, toDate: Date) {
  // Using Prisma's raw query for percentile calculation
  const metrics = await prisma.$queryRaw`
    WITH processing_times AS (
      SELECT
        DATE_TRUNC('day', "request_timestamp") as day,
        EXTRACT(EPOCH FROM ("processed_at" - "request_timestamp"))::float8 as processing_time_sec
      FROM "processing_logs"
      WHERE "request_timestamp" >= ${fromDate}
        AND "request_timestamp" <= ${toDate}
        AND "processed_at" IS NOT NULL
    )
    SELECT
      day,
      CAST(percentile_cont(0.50) WITHIN GROUP (ORDER BY processing_time_sec) AS float8) as "p50",
      CAST(percentile_cont(0.95) WITHIN GROUP (ORDER BY processing_time_sec) AS float8) as "p95",
      CAST(percentile_cont(0.99) WITHIN GROUP (ORDER BY processing_time_sec) AS float8) as "p99",
      CAST(COUNT(*) AS integer) as count
    FROM processing_times
    GROUP BY day
    ORDER BY day;
  `;

  return metrics;
}

async function getDocumentTypeMetrics(fromDate: Date, toDate: Date) {
  // Using Prisma's raw query for document type metrics
  const metrics = await prisma.$queryRaw`
    WITH processing_times AS (
      SELECT
        DATE_TRUNC('day', "request_timestamp") as day,
        "request_type",
        EXTRACT(EPOCH FROM ("processed_at" - "request_timestamp"))::float8 as processing_time_sec
      FROM "processing_logs"
      WHERE "request_timestamp" >= ${fromDate}
        AND "request_timestamp" <= ${toDate}
        AND "processed_at" IS NOT NULL
    )
    SELECT
      day,
      "request_type",
      CAST(percentile_cont(0.50) WITHIN GROUP (ORDER BY processing_time_sec) AS float8) as "medianTime"
    FROM processing_times
    GROUP BY day, "request_type"
    ORDER BY day, "request_type";
  `;

  return metrics;
}
