import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient, Prisma } from '@prisma/client';

const prisma = new PrismaClient();

interface DashboardStatsParams {
  startDate?: string;
  endDate?: string;
}

export interface DashboardStatsResponse {
  total: number;
  success: number;
  failed: number;
  processing: number;
  invoices: number;
  taxInvoices: number;
  totalPages: number;
  invoicePages: number;
  taxInvoicePages: number;
}

/**
 * GET /api/dashboard/stats
 * Fetch dashboard statistics with optional date range filtering
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(req.url);

    // Parse date range
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Build the where clause for filtering
    const where: any = {};

    // Add date range filter
    if (startDate || endDate) {
      where.requestTimestamp = {};

      if (startDate) {
        where.requestTimestamp.gte = new Date(startDate);
      }

      if (endDate) {
        // Include the entire end date by setting to end of day
        const end = new Date(endDate);
        end.setDate(end.getDate() + 1);
        where.requestTimestamp.lt = end;
      }
    }

    // Build the raw SQL query with parameters
    const query = Prisma.sql`
      SELECT
        (COUNT(*))::integer as "total",
        (COUNT(CASE WHEN "processing_status" = 'success' THEN 1 END))::integer as "success",
        (COUNT(CASE WHEN "processing_status" = 'failed' THEN 1 END))::integer as "failed",
        (COUNT(CASE WHEN "processing_status" = 'processing' THEN 1 END))::integer as "processing",
        (COUNT(CASE WHEN "request_type" = 'invoice' THEN 1 END))::integer as "invoices",
        (COUNT(CASE WHEN "request_type" = 'taxInvoice' THEN 1 END))::integer as "taxInvoices",
        COALESCE(SUM(CASE WHEN "processing_status" = 'success' THEN "total_pages" ELSE 0 END)::integer, 0) as "totalPages",
        COALESCE(SUM(CASE WHEN "request_type" = 'invoice' AND "processing_status" = 'success' THEN "total_pages" ELSE 0 END)::integer, 0) as "invoicePages",
        COALESCE(SUM(CASE WHEN "request_type" = 'taxInvoice' AND "processing_status" = 'success' THEN "total_pages" ELSE 0 END)::integer, 0) as "taxInvoicePages"
      FROM "processing_logs"
      ${where.requestTimestamp ? Prisma.sql`
        WHERE "request_timestamp" >= ${where.requestTimestamp.gte || new Date(0)}
        AND "request_timestamp" < ${where.requestTimestamp.lt || new Date()}
      ` : Prisma.sql``}
    `;

    // Execute the query
    const [stats] = await prisma.$queryRaw<Array<{
      total: number;
      success: number;
      failed: number;
      processing: number;
      invoices: number;
      taxInvoices: number;
      totalPages: number;
      invoicePages: number;
      taxInvoicePages: number;
    }>>(query);

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    );
  }
}
