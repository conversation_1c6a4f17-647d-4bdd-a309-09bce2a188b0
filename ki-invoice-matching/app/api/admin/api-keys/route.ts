import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/db';
import crypto from 'crypto';

/**
 * Generate a secure random API key with a prefix and hash
 * @returns An object containing the full API key and its hash
 */
export function generateApiKey(): { fullKey: string, hashedKey: string } {
  // Generate a key with a prefix for identification
  const keyId = 'ki_' + crypto.randomBytes(8).toString('hex');
  const keySecret = crypto.randomBytes(32).toString('hex');
  const fullKey = `${keyId}.${keySecret}`;

  // Hash the key for storage
  const hashedKey = crypto.createHash('sha256').update(fullKey).digest('hex');

  return { fullKey, hashedKey };
}

// Export default object for mocking in tests
export default { generateApiKey };

/**
 * Get all API keys
 */
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all API keys
    const apiKeys = await prisma.ApiKey.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        createdAt: true,
        updatedAt: true,
        lastUsedAt: true,
        isActive: true,
        createdBy: {
          select: {
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json({ apiKeys });
  } catch (error) {
    console.error('Error getting API keys:', error);
    return NextResponse.json(
      { error: 'Error getting API keys' },
      { status: 500 }
    );
  }
}

/**
 * Create a new API key
 */
export async function POST(req: NextRequest) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);
    if (!session || !session.user || !session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { name, description } = body;

    // Validate request body
    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Generate a new API key and its hash
    const { fullKey, hashedKey } = generateApiKey();

    // Create a new API key - store only the hash
    const apiKey = await prisma.ApiKey.create({
      data: {
        name,
        description,
        keyHash: hashedKey, // Store only the hash in the database
        createdById: session.user.id,
      },
    });

    // Return the full key to the user - this is the ONLY time the full key is available
    return NextResponse.json({
      id: apiKey.id,
      name: apiKey.name,
      description: apiKey.description,
      key: fullKey, // Return the full key to the user
      createdAt: apiKey.createdAt,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating API key:', error);
    return NextResponse.json(
      { error: 'Error creating API key' },
      { status: 500 }
    );
  }
}
