import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/db';
import { hash } from 'bcryptjs';
import { z } from 'zod';

// Validation schemas
const updatePasswordSchema = z.object({
  password: z.string().min(6, 'Password must be at least 6 characters')
});

const updateUserSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  email: z.string().email('Invalid email format').optional(),
  role: z.string().optional()
});

/**
 * Update admin user password
 */
export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;
    const body = await req.json();

    // Check if this is a password update
    if (body.password) {
      // Validate password input
      const validatedData = updatePasswordSchema.parse(body);

      // Check if user exists
      const existingUser = await prisma.adminUser.findUnique({
        where: { id }
      });

      if (!existingUser) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      // Hash the new password
      const passwordHash = await hash(validatedData.password, 12);

      // Update the user's password
      const updatedUser = await prisma.adminUser.update({
        where: { id },
        data: { passwordHash },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          updatedAt: true
        }
      });

      return NextResponse.json({
        message: 'Password updated successfully',
        user: updatedUser
      });
    } else {
      // Handle other user updates (name, email, role)
      const validatedData = updateUserSchema.parse(body);

      // Check if user exists
      const existingUser = await prisma.adminUser.findUnique({
        where: { id }
      });

      if (!existingUser) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      // If email is being updated, check for conflicts
      if (validatedData.email && validatedData.email !== existingUser.email) {
        const emailConflict = await prisma.adminUser.findUnique({
          where: { email: validatedData.email }
        });

        if (emailConflict) {
          return NextResponse.json(
            { error: 'User with this email already exists' },
            { status: 400 }
          );
        }
      }

      // Update the user
      const updatedUser = await prisma.adminUser.update({
        where: { id },
        data: validatedData,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
          updatedAt: true
        }
      });

      return NextResponse.json({
        message: 'User updated successfully',
        user: updatedUser
      });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating admin user:', error);
    return NextResponse.json(
      { error: 'Failed to update admin user' },
      { status: 500 }
    );
  }
}

/**
 * Delete admin user
 */
export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Prevent users from deleting themselves
    if (session.user.id === id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await prisma.adminUser.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            apiKeys: true
          }
        }
      }
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if this is the last admin user
    const adminCount = await prisma.adminUser.count();
    if (adminCount <= 1) {
      return NextResponse.json(
        { error: 'Cannot delete the last admin user' },
        { status: 400 }
      );
    }

    // Delete the user (this will cascade delete related API keys)
    await prisma.adminUser.delete({
      where: { id }
    });

    return NextResponse.json({
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting admin user:', error);
    return NextResponse.json(
      { error: 'Failed to delete admin user' },
      { status: 500 }
    );
  }
}