import { NextRequest, NextResponse } from 'next/server';
import * as Sentry from '@sentry/nextjs';
import { InvoiceData } from '@/lib/types/invoice';
import { processInvoiceFile } from '@/lib/api/external-service';
import { validateApiKey } from '@/lib/api/api-key-middleware';
import {
  createErrorResponse,
  handleColorDetection,
  createProcessingLog,
  updateInvoiceLogWithResults,
  updateLogWithError,
  createApiResponse,
  updateLogWithApiKeyId,
  parseFormDataWithFormidable
} from '@/lib/api/shared-invoice-processing';

// Use Node.js runtime for stream and fs support
export const runtime = 'nodejs';

// Disable body parser to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

/**
 * Process and match a standard invoice file upload
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  return await Sentry.startSpan(
    {
      op: 'http.server',
      name: 'POST /api/process-invoice-file',
      attributes: {
        'http.method': 'POST',
        'http.route': '/api/process-invoice-file',
      },
    },
    async (span) => {
      try {
        // Validate API key
        const validation = await Sentry.startSpan(
          {
            op: 'auth.validate',
            name: 'Validate API Key',
          },
          async () => await validateApiKey(req)
        );

        // Handle API key validation
        if (validation && 'error' in validation && validation.error) {
          span.setStatus({ code: 2, message: 'API key validation failed' });
          span.setAttribute('auth.result', 'failed');
          // Return error response if validation failed
          return validation.error;
        }

        span.setAttribute('auth.result', 'success');
        if (validation && 'apiKeyId' in validation && validation.apiKeyId) {
          span.setAttribute('auth.api_key_id', validation.apiKeyId);
        }

        // Use Formidable to parse multipart form data
        const { fields, files } = await Sentry.startSpan(
          {
            op: 'request.parse',
            name: 'Parse multipart form data',
          },
          async () => await parseFormDataWithFormidable(req)
        );
        const pdfFile = files['file'];

        // Validate required parameters using shared utility
        if (!pdfFile) {
          span.setStatus({ code: 2, message: 'Missing file parameter' });
          span.setAttribute('validation.result', 'failed');
          span.setAttribute('validation.error', 'missing_file');
          return NextResponse.json(
            {
              error: 'Missing file parameter',
              results: { success: false }
            },
            { status: 400 }
          );
        }

        span.setAttribute('file.name', pdfFile.filename || 'unknown');
        span.setAttribute('file.size', pdfFile.buffer.length);

        const requiredFields: Array<{ key: string; name: string; type: 'string' | 'number' | 'date' }> = [
          { key: 'vendor_name', name: 'vendor_name', type: 'string' },
          { key: 'invoice_number', name: 'invoice_number', type: 'string' },
          { key: 'invoice_date', name: 'invoice_date', type: 'date' },
          { key: 'invoice_amount', name: 'invoice_amount', type: 'number' },
        ];

        const { validateInvoiceFields } = await import('@/lib/api/validate-invoice-fields');

        // Convert parsed fields to a plain object for validation and parse numbers
        const formValues: Record<string, unknown> = {};
        for (const field of requiredFields) {
          const value = fields[field.key];
          if (field.type === 'number' && typeof value === 'string') {
            formValues[field.key] = parseFloat(value);
          } else {
            formValues[field.key] = value;
          }
        }

        const validationError = await Sentry.startSpan(
          {
            op: 'validation.fields',
            name: 'Validate invoice fields',
          },
          async () => validateInvoiceFields(formValues, requiredFields)
        );

        if (validationError) {
          span.setStatus({ code: 2, message: 'Field validation failed' });
          span.setAttribute('validation.result', 'failed');
          span.setAttribute('validation.error', validationError);
          return NextResponse.json(
            {
              error: validationError,
              results: { success: false }
            },
            { status: 400 }
          );
        }

        span.setAttribute('validation.result', 'success');

        // Extract input data from parsed fields
        const inputData: InvoiceData = {
          vendorName: fields['vendor_name'] || undefined,
          invoiceNo: fields['invoice_number'] || undefined,
          invoiceDate: fields['invoice_date'] || undefined,
          invoiceAmount: fields['invoice_amount'] ?
            parseFloat(fields['invoice_amount']) : undefined,
          vatAmount: fields['vat_amount'] ?
            parseFloat(fields['vat_amount']) : undefined,
        };

        // Use the buffer from the parsed file
        const buffer = pdfFile.buffer;

        // Create a log entry for this request
        const log = await Sentry.startSpan(
          {
            op: 'db.create',
            name: 'Create processing log',
          },
          async () => await createProcessingLog(
            'invoice',
            pdfFile.filename,
            buffer.length,
            inputData
          )
        );

        span.setAttribute('log.id', log.id);

        // If we have an API key ID, update the log with it
        if (validation && 'apiKeyId' in validation && validation.apiKeyId) {
          await Sentry.startSpan(
            {
              op: 'db.update',
              name: 'Update log with API key ID',
            },
            async () => await updateLogWithApiKeyId(log.id, validation.apiKeyId!)
          );
        }

        try {
          // Start both external service processing and color detection in parallel
          // These operations are independent and can run concurrently for better performance
          const externalResponsePromise = Sentry.startSpan(
            {
              op: 'http.client',
              name: 'Process invoice with external service',
              attributes: {
                'external.service': 'DONA',
                'external.operation': 'process_invoice_file',
              },
            },
            async () => await processInvoiceFile(buffer, inputData)
          );

          const colorDetectionPromise = Sentry.startSpan(
            {
              op: 'processing.color_detection',
              name: 'Detect PDF color',
              attributes: {
                'pdf.size_bytes': buffer.length,
              },
            },
            async () => await handleColorDetection(buffer)
          );

          // Wait for both operations to complete in parallel
          const [externalResponse, colorDetection] = await Promise.all([
            externalResponsePromise,
            colorDetectionPromise
          ]);

          // Use shared matching utility for robust field comparison
          const { matchFields } = await import('@/lib/api/field-matching');
          const fieldTypes: Record<string, "string" | "number" | "date"> = {
            vendor_name: 'string',
            invoice_number: 'string',
            invoice_date: 'date',
            invoice_amount: 'number',
            vat_amount: 'number',
          };

          const { fields: enhancedFields, summary: enhancedSummary } = await Sentry.startSpan(
            {
              op: 'processing.field_matching',
              name: 'Match invoice fields',
              attributes: {
                'fields.total': Object.keys(fieldTypes).length,
              },
            },
            async () => matchFields(
              {
                vendor_name: inputData.vendorName || '',
                invoice_number: inputData.invoiceNo || '',
                invoice_date: inputData.invoiceDate || '',
                invoice_amount: inputData.invoiceAmount?.toString() || '',
                vat_amount: inputData.vatAmount?.toString() || '',
              } as Record<string, string>,
              externalResponse.results.fields,
              fieldTypes
            )
          );

          const results = {
            ...externalResponse.results,
            fields: enhancedFields,
            summary: enhancedSummary,
            is_colored: colorDetection.is_colored,
            color_pages: colorDetection.color_pages,
            total_pages: colorDetection.total_pages
          };

          // Add processing metrics to span
          span.setAttribute('processing.external_response_id', externalResponse.processing_id || 'unknown');
          span.setAttribute('processing.total_pages', colorDetection.total_pages);
          span.setAttribute('processing.is_colored', colorDetection.is_colored);
          span.setAttribute('processing.color_pages_count', colorDetection.color_pages.length);
          span.setAttribute('matching.total_fields', enhancedSummary.total_fields);
          span.setAttribute('matching.matched_fields', enhancedSummary.matched);
          span.setAttribute('matching.mismatched_fields', enhancedSummary.mismatched);
          span.setAttribute('matching.not_found_fields', enhancedSummary.not_found);

          // Update the log with the results
          if (externalResponse) {
            await Sentry.startSpan(
              {
                op: 'db.update',
                name: 'Update log with results',
              },
              async () => await updateInvoiceLogWithResults(log.id, externalResponse, colorDetection, results)
            );

            span.setStatus({ code: 1, message: 'Success' });

            // Return the response in the format expected by the client
            return NextResponse.json(createApiResponse(externalResponse, log.id, results));
          }

          // If externalResponse is undefined, return a generic success response
          span.setStatus({ code: 1, message: 'Success (no external response)' });
          return NextResponse.json({
            processing_id: `success-${Date.now()}`,
            results,
            log_id: log?.id || `log-${Date.now()}`
          });

        } catch (error) {
          // Capture error in Sentry
          Sentry.captureException(error, {
            tags: {
              operation: 'invoice_file_processing',
              log_id: log?.id,
            },
            extra: {
              file_name: pdfFile?.filename,
              file_size: pdfFile?.buffer?.length,
              input_data: inputData,
            },
          });

          span.setStatus({ code: 2, message: error instanceof Error ? error.message : 'Unknown error' });
          span.setAttribute('error.type', error instanceof Error ? error.constructor.name : 'unknown');
          span.setAttribute('error.message', error instanceof Error ? error.message : 'Unknown error');

          // Update the log with the error if log exists
          if (log && log.id) {
            await updateLogWithError(log.id, error);

            // Return error response with log ID
            return NextResponse.json(
              createErrorResponse(error, log.id),
              { status: 500 }
            );
          }

          // If log doesn't exist, return a generic error response
          if (process.env.NODE_ENV !== 'test') {
            console.error('Error processing invoice file (no log):', error);
          }
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return NextResponse.json(
            {
              error: errorMessage,
              results: { success: false },
              processing_id: `error-${Date.now()}`
            },
            { status: 500 }
          );
        }

      } catch (error) {
        // Capture error in Sentry for outer catch
        Sentry.captureException(error, {
          tags: {
            operation: 'invoice_file_processing_outer',
            level: 'critical',
          },
          extra: {
            error_location: 'main_try_block',
          },
        });

        span.setStatus({ code: 2, message: error instanceof Error ? error.message : 'Unknown error' });
        span.setAttribute('error.type', error instanceof Error ? error.constructor.name : 'unknown');
        span.setAttribute('error.message', error instanceof Error ? error.message : 'Unknown error');
        span.setAttribute('error.location', 'main_try_block');

        console.error('❌ Error in main try block:', error);
        console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');

        if (process.env.NODE_ENV !== 'test') {
          console.error('Error processing invoice file:', error);
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';

        // Create a generic error response without a log ID
        return NextResponse.json(
          {
            error: errorMessage,
            results: { success: false },
            processing_id: `error-${Date.now()}`
          },
          { status: 500 }
        );
      }
    }
  );
}
