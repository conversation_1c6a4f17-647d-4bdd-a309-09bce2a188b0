import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { format, addDays } from 'date-fns';
import { LogFilterOptions, PaginationOptions, LogsResponse, FormattedLogEntry } from '@/lib/types/log';

// Default page size for pagination
const DEFAULT_PAGE_SIZE = 10;
const MAX_PAGE_SIZE = 100;

/**
 * GET /api/logs
 * Fetch processing logs with optional filtering, sorting, and pagination
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(req.url);

    // Parse pagination options with validation
    const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10)) || 1;
    const pageSize = Math.min(
      MAX_PAGE_SIZE, 
      Math.max(1, parseInt(searchParams.get('pageSize') || DEFAULT_PAGE_SIZE.toString(), 10) || DEFAULT_PAGE_SIZE)
    );

    // Parse sort options
    const sortBy = searchParams.get('sortBy') || 'requestTimestamp';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Parse filter options
    const filters: LogFilterOptions = {
      requestType: searchParams.get('requestType') || undefined,
      processingStatus: searchParams.get('processingStatus') || undefined,
      vendorName: searchParams.get('vendorName') || undefined,
      documentNo: searchParams.get('documentNo') || undefined,
    };

    // Parse date range
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');
    
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (startDateParam) {
      startDate = new Date(startDateParam);
      if (isNaN(startDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid startDate format. Please use ISO 8601 format (e.g., 2023-01-01T00:00:00.000Z)' },
          { status: 400 }
        );
      }
    }

    if (endDateParam) {
      endDate = new Date(endDateParam);
      if (isNaN(endDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid endDate format. Please use ISO 8601 format (e.g., 2023-12-31T23:59:59.999Z)' },
          { status: 400 }
        );
      }
    }

    // Build the where clause for filtering
    const where: any = {};

    // Apply filters if provided
    if (filters.requestType) {
      where.requestType = filters.requestType;
    }

    if (filters.processingStatus) {
      where.processingStatus = filters.processingStatus;
    }

    if (filters.vendorName) {
      where.inputVendorName = {
        contains: filters.vendorName,
        mode: 'insensitive',
      };
    }

    if (filters.documentNo) {
      where.inputDocumentNo = {
        contains: filters.documentNo,
        mode: 'insensitive',
      };
    }

    // Add date range filter
    if (startDate || endDate) {
      where.requestTimestamp = {};

      if (startDate) {
        where.requestTimestamp.gte = startDate;
      }

      if (endDate) {
        // Use the end of the day for the end date
        const endOfDay = new Date(endDate);
        endOfDay.setHours(23, 59, 59, 999);
        where.requestTimestamp.lte = endOfDay;
      }
    }

    // Use a transaction to get both count and data in a single database roundtrip
    const [totalLogs, logs] = await prisma.$transaction([
      prisma.processingLog.count({ where }),
      prisma.processingLog.findMany({
        where,
        orderBy: {
          [sortBy]: sortOrder as 'asc' | 'desc',
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        include: {
          apiKey: {
            select: {
              name: true,
            },
          },
        },
      }),
    ]);

    // Calculate total pages
    const totalPages = Math.ceil(totalLogs / pageSize);

    // Format dates for display and include raw timestamps
    const formattedLogs: FormattedLogEntry[] = logs.map(log => ({
      ...log,
      // Formatted dates for display
      requestTimestamp: format(log.requestTimestamp, 'yyyy-MM-dd HH:mm:ss'),
      inputDocumentDate: log.inputDocumentDate ? format(log.inputDocumentDate, 'yyyy-MM-dd') : null,
      ocrDocumentDate: log.ocrDocumentDate ? format(log.ocrDocumentDate, 'yyyy-MM-dd') : null,
      processedAt: log.processedAt ? format(log.processedAt, 'yyyy-MM-dd HH:mm:ss') : null,
      // Raw timestamps for calculations (only include if not null)
      ...(log.requestTimestamp && { _rawRequestTimestamp: log.requestTimestamp }),
      ...(log.processedAt && { _rawProcessedAt: log.processedAt }),
      // Calculate matched fields percentage for display
      matchedFieldsPercentage: log.totalFields && log.totalFields > 0
        ? Math.round((log.matchedFields || 0) / log.totalFields * 100)
        : null,
      // Add API key name if available
      apiKeyName: log.apiKey?.name || null,
      // Remove the apiKey object from the response
      apiKey: undefined as any,
    }));

    // Prepare response with pagination metadata
    const response: LogsResponse = {
      logs: formattedLogs,
      total: totalLogs,
      page,
      pageSize,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };

    // Add cache control headers
    const responseHeaders = new Headers();
    responseHeaders.set('Cache-Control', 's-maxage=60, stale-while-revalidate=300');
    responseHeaders.set('X-Total-Count', totalLogs.toString());
    responseHeaders.set('X-Page', page.toString());
    responseHeaders.set('X-Page-Size', pageSize.toString());
    responseHeaders.set('X-Total-Pages', totalPages.toString());

    return new NextResponse(JSON.stringify(response), {
      status: 200,
      headers: responseHeaders,
    });

  } catch (error) {
    console.error('Error fetching logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch logs' },
      { status: 500 }
    );
  }
}
