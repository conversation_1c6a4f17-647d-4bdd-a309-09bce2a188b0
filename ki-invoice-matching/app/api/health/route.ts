import { NextResponse } from "next/server";
import prisma from "@/lib/db";

/**
 * Health check endpoint for the API
 * Used by Docker health checks and monitoring
 */
export async function GET() {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;

    return NextResponse.json(
      {
        status: "ok",
        timestamp: new Date().toISOString(),
        database: "connected"
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Health check failed:", error);

    return NextResponse.json(
      {
        status: "error",
        timestamp: new Date().toISOString(),
        error: "Database connection failed"
      },
      { status: 500 }
    );
  }
}
