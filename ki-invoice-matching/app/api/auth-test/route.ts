import { NextRequest, NextResponse } from "next/server";
import { compare } from "bcryptjs";
import prisma from "@/lib/db";

// Add a debug log to check if prisma is properly imported
console.log("Auth test - Prisma client loaded:", !!prisma, "AdminUser model:", !!prisma?.adminUser);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json({ success: false, error: "Missing credentials" }, { status: 400 });
    }

    // Check if prisma and adminUser are defined
    if (!prisma || !prisma.adminUser) {
      console.error("Prisma client or adminUser model is undefined");
      return NextResponse.json({
        success: false,
        error: "Database connection error",
        debug: {
          prismaExists: !!prisma,
          adminUserExists: prisma ? !!prisma.adminUser : false
        }
      }, { status: 500 });
    }

    const user = await prisma.adminUser.findUnique({
      where: { email }
    });

    if (!user) {
      return NextResponse.json({ success: false, error: "User not found" }, { status: 404 });
    }

    const isPasswordValid = await compare(password, user.passwordHash);

    if (!isPasswordValid) {
      return NextResponse.json({ success: false, error: "Invalid password" }, { status: 401 });
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    console.error("Auth test error:", error);
    return NextResponse.json({
      success: false,
      error: "Server error",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
